import { Controller, Get, HttpStatus, UseGuards, Request, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CustomersService } from './customers.service';
import { CustomerResponseDto } from './dto/customer-response.dto';

/**
 * Controller handling customer-related HTTP endpoints
 * Provides customer data from leads table with RBAC implementation
 */
@ApiTags('Customers')
@Controller('customers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CustomersController {
  constructor(private readonly customersService: CustomersService) {}

  /**
   * Gets customers based on user permissions
   * GET /customers
   * Implements RBAC: view.all.customers, view.branch.customers, view.my.customers
   */
  @Get()
  @ApiOperation({
    summary: 'Get customers based on user permissions',
    description:
      'Retrieves customers from the leads table based on user permissions. Only includes leads that have an account number. RBAC: view.all.customers (all customers), view.branch.customers (branch customers), view.my.customers (own customers).',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customers retrieved successfully',
    type: [CustomerResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Insufficient permissions to view customers',
  })
  async getCustomers(@Request() req: any): Promise<CustomerResponseDto[]> {
    return this.customersService.getCustomers(req.user);
  }

  /**
   * Exports customers to Excel based on user permissions
   * GET /customers/export
   * Implements the same RBAC logic as getCustomers
   */
  @Get('export')
  @ApiOperation({
    summary: 'Export customers to Excel based on user permissions',
    description:
      'Exports customers to Excel file based on user permissions. Implements the same RBAC logic: view.all.customers (all customers), view.branch.customers (branch customers), view.my.customers (own customers).',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Excel file generated successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Insufficient permissions to export customers',
  })
  async exportCustomers(@Request() req: any, @Res() res: Response): Promise<void> {
    const buffer = await this.customersService.exportCustomers(req.user);
    
    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': 'attachment; filename="customers-export.xlsx"',
      'Content-Length': buffer.length,
    });
    
    res.end(buffer);
  }
}
