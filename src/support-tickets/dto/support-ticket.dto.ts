import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, MaxLength, IsOptional } from 'class-validator';

/**
 * Data Transfer Object for support ticket response
 */
export class SupportTicketResponseDto {
  @ApiProperty({
    description: 'Support ticket ID',
    example: 'ST-2025-001',
  })
  id: string;

  @ApiProperty({
    description: 'Ticket type',
    example: 'Report a bug',
  })
  type: string;

  @ApiProperty({
    description: 'Ticket priority',
    example: 'High',
  })
  priority: string;

  @ApiProperty({
    description: 'Ticket title',
    example: 'Login page not loading properly',
  })
  title: string;

  @ApiProperty({
    description: 'Ticket description',
    example: 'The login page takes too long to load and sometimes shows a blank screen. This is affecting user experience significantly.',
  })
  description: string;

  @ApiProperty({
    description: 'Ticket status',
    example: 'Open',
  })
  status: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2025-01-15',
  })
  createdAt: string;
}

/**
 * Data Transfer Object for creating a support ticket
 */
export class CreateSupportTicketDto {
  @ApiProperty({
    description: 'Ticket type',
    example: 'Report a bug',
  })
  @IsNotEmpty({ message: 'Ticket type is required' })
  @IsString({ message: 'Ticket type must be a string' })
  @MaxLength(100, { message: 'Ticket type cannot exceed 100 characters' })
  type: string;

  @ApiProperty({
    description: 'Ticket priority',
    example: 'Medium',
  })
  @IsNotEmpty({ message: 'Ticket priority is required' })
  @IsString({ message: 'Ticket priority must be a string' })
  @MaxLength(50, { message: 'Ticket priority cannot exceed 50 characters' })
  priority: string;

  @ApiProperty({
    description: 'Ticket title',
    example: 'Login page not loading properly',
  })
  @IsNotEmpty({ message: 'Ticket title is required' })
  @IsString({ message: 'Ticket title must be a string' })
  @MaxLength(255, { message: 'Ticket title cannot exceed 255 characters' })
  title: string;

  @ApiProperty({
    description: 'Ticket description',
    example: 'The login page takes too long to load and sometimes shows a blank screen.',
  })
  @IsNotEmpty({ message: 'Ticket description is required' })
  @IsString({ message: 'Ticket description must be a string' })
  @MaxLength(2000, { message: 'Ticket description cannot exceed 2000 characters' })
  description: string;

  @ApiProperty({
    description: 'Ticket status',
    example: 'Open',
  })
  @IsNotEmpty({ message: 'Ticket status is required' })
  @IsString({ message: 'Ticket status must be a string' })
  @MaxLength(50, { message: 'Ticket status cannot exceed 50 characters' })
  status: string;
}

/**
 * Data Transfer Object for updating a support ticket
 */
export class UpdateSupportTicketDto {
  @ApiPropertyOptional({
    description: 'Ticket type',
    example: 'Report a bug',
  })
  @IsOptional()
  @IsString({ message: 'Ticket type must be a string' })
  @MaxLength(100, { message: 'Ticket type cannot exceed 100 characters' })
  type?: string;

  @ApiPropertyOptional({
    description: 'Ticket priority',
    example: 'High',
  })
  @IsOptional()
  @IsString({ message: 'Ticket priority must be a string' })
  @MaxLength(50, { message: 'Ticket priority cannot exceed 50 characters' })
  priority?: string;

  @ApiPropertyOptional({
    description: 'Ticket title',
    example: 'Login page not loading properly',
  })
  @IsOptional()
  @IsString({ message: 'Ticket title must be a string' })
  @MaxLength(255, { message: 'Ticket title cannot exceed 255 characters' })
  title?: string;

  @ApiPropertyOptional({
    description: 'Ticket description',
    example: 'The login page takes too long to load and sometimes shows a blank screen. This is affecting user experience significantly.',
  })
  @IsOptional()
  @IsString({ message: 'Ticket description must be a string' })
  @MaxLength(2000, { message: 'Ticket description cannot exceed 2000 characters' })
  description?: string;

  @ApiPropertyOptional({
    description: 'Ticket status',
    example: 'Open',
  })
  @IsOptional()
  @IsString({ message: 'Ticket status must be a string' })
  @MaxLength(50, { message: 'Ticket status cannot exceed 50 characters' })
  status?: string;
}
