import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { NotificationHandler, NotificationContext } from './notification.handler.interface';
import { NotificationType, NotificationPriority } from '@prisma/client';

@Injectable()
export class DormancyHitlistAssignedHandler implements NotificationHandler {
  private readonly logger = new Logger(DormancyHitlistAssignedHandler.name);
  readonly type = NotificationType.DORMANCY_HITLIST_ASSIGNED;

  constructor(private readonly prisma: PrismaService) {}

  shouldHandle(context: NotificationContext): boolean {
    return context.entityType === 'dormancy_hitlist' && context.entityData?.assignedUserId;
  }

  async generateNotification(context: NotificationContext): Promise<{
    title: string;
    message: string;
    priority: NotificationPriority;
    data?: Record<string, any>;
    recipientIds: string[];
  }> {
    const { entityId, entityData, triggerUserId } = context;
    
    try {
      // Get hitlist record details
      const hitlistRecord = await this.prisma.customerServiceHitlistRecord.findUnique({
        where: { id: entityId },
        include: {
          customer_service_hitlist: {
            include: {
              uploader: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  rm_code: true,
                },
              },
            },
          },
        },
      });

      if (!hitlistRecord) {
        this.logger.warn(`Hitlist record ${entityId} not found for notification`);
        return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
      }

      // Get the assigned user details (using rm_code from the record)
      const assignedUser = await this.prisma.user.findUnique({
        where: { rm_code: hitlistRecord.rm_code },
        select: {
          id: true,
          name: true,
          email: true,
          rm_code: true,
        },
      });

      if (!assignedUser) {
        this.logger.warn(`Assigned user with rm_code ${hitlistRecord.rm_code} not found for hitlist record ${entityId}`);
        return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
      }

      // Get the user who uploaded the hitlist (trigger user)
      let uploadedByUser: { id: string; name: string; email: string; rm_code: string } | null = null;
      if (triggerUserId) {
        uploadedByUser = await this.prisma.user.findUnique({
          where: { id: triggerUserId },
          select: {
            id: true,
            name: true,
            email: true,
            rm_code: true,
          },
        });
      }

      const title = 'New Dormancy Hitlist Assigned';
      const message = `You have been assigned a new dormancy hitlist record: ${hitlistRecord.customer_name}`;
      
      const data = {
        hitlistRecordId: hitlistRecord.id,
        hitlistId: hitlistRecord.customer_service_hitlist.id,
        hitlistCode: hitlistRecord.customer_service_hitlist.code,
        customerName: hitlistRecord.customer_name,
        accountNumber: hitlistRecord.account_number,
        phoneNumber: hitlistRecord.phone_number,
        uploadedBy: uploadedByUser?.name || 'System',
        uploadedByRmCode: uploadedByUser?.rm_code,
        uploadDate: hitlistRecord.customer_service_hitlist.uploaded_date,
        createdAt: hitlistRecord.created_at,
      };

      return {
        title,
        message,
        priority: NotificationPriority.NORMAL,
        data,
        recipientIds: [assignedUser.id],
      };

    } catch (error) {
      this.logger.error(`Failed to generate dormancy hitlist assigned notification: ${error.message}`, error.stack);
      return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
    }
  }
}
