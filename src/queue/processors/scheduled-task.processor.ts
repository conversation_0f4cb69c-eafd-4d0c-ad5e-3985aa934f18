import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Inject, forwardRef } from '@nestjs/common';
import { Job } from 'bullmq';
import { ScheduledTaskService } from '../../scheduled-tasks/scheduled-task.service';
import { TaskHandlerRegistry } from '../../scheduled-tasks/task-handlers/task-handler.registry';

export interface ScheduledTaskJobData {
  taskId: string;
  type: string;
  payload: any;
  attemptNumber: number;
}

@Processor('scheduled-tasks')
export class ScheduledTaskProcessor extends WorkerHost {
  private readonly logger = new Logger(ScheduledTaskProcessor.name);
  private readonly workerId: string;

  constructor(
    @Inject(forwardRef(() => ScheduledTaskService))
    private readonly scheduledTaskService: ScheduledTaskService,
    @Inject(forwardRef(() => TaskHandlerRegistry))
    private readonly taskHandlerRegistry: TaskHandlerRegistry,
  ) {
    super();
    this.workerId = `worker-${process.pid}-${Date.now()}`;
  }

  async process(job: Job<ScheduledTaskJobData>) {
    const { taskId, type, payload, attemptNumber } = job.data;
    const startTime = Date.now();

    this.logger.log(`Processing scheduled task ${taskId} of type ${type} (attempt ${attemptNumber})`);
    console.log('=== WORKER PROCESSOR START ===');
    console.log(`Job ID: ${job.id}`);
    console.log(`Task ID: ${taskId}`);
    console.log(`Task Type: ${type}`);
    console.log(`Attempt: ${attemptNumber}`);
    console.log(`Worker ID: ${this.workerId}`);
    console.log(`Container ID: ${process.env.HOSTNAME || 'unknown'}`);
    console.log(`Process PID: ${process.pid}`);
    console.log(`Queue: ${job.queueName}`);
    console.log(`Timestamp: ${new Date().toISOString()}`);

    try {
      // Mark task as running and create execution record
      console.log(`Attempting to mark task ${taskId} as running...`);
      const { task, execution } = await this.scheduledTaskService.markAsRunning(taskId, this.workerId);
      console.log(`Successfully marked task ${taskId} as running (execution: ${execution.id})`);
      
      // Update job progress
      await job.updateProgress(10);

      // Process the task using the task handler registry
      console.log(`Executing task ${taskId} using task handler registry...`);
      const result = await this.taskHandlerRegistry.executeTask(type, payload, job);
      console.log(`Task ${taskId} execution completed with result:`, result);

      await job.updateProgress(90);

      // Calculate execution duration
      const durationMs = Date.now() - startTime;

      // Mark task as completed
      await this.scheduledTaskService.markAsCompleted(taskId, execution.id, result, durationMs);

      await job.updateProgress(100);

      this.logger.log(`Completed scheduled task ${taskId} in ${durationMs}ms`);
      console.log(`=== WORKER PROCESSOR COMPLETED ===`);
      console.log(`Task ID: ${taskId}`);
      console.log(`Duration: ${durationMs}ms`);
      console.log(`Result:`, result);
      return result;

    } catch (error) {
      const durationMs = Date.now() - startTime;
      const errorMessage = error.message || 'Unknown error occurred';

      this.logger.error(`Failed to process scheduled task ${taskId}: ${errorMessage}`, error.stack);
      console.log(`=== WORKER PROCESSOR FAILED ===`);
      console.log(`Task ID: ${taskId}`);
      console.log(`Error:`, errorMessage);
      console.log(`Duration: ${durationMs}ms`);
      console.log(`Stack:`, error.stack);

      // Get the execution record to update it
      const task = await this.scheduledTaskService.findOne(taskId);
      const execution = task.executions[0]; // Most recent execution

      // Mark task as failed
      await this.scheduledTaskService.markAsFailed(taskId, execution.id, errorMessage, durationMs);

      throw error;
    }
  }


}
