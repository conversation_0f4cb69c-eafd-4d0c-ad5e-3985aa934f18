import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  ParseUUIDPipe,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PurposeCategoryService } from './purpose-category.service';
import {
  CreatePurposeCategoryDto,
  UpdatePurposeCategoryDto,
  PurposeCategoryResponseDto,
  PurposeCategoryListResponseDto,
} from './dto';

/**
 * PurposeCategoryController
 * 
 * RESTful API controller for managing Purpose Category entities.
 * Provides comprehensive CRUD operations with proper HTTP status codes,
 * validation, error handling, and API documentation.
 * 
 * Base URL: /api/v1/purpose-categories
 * 
 * Features:
 * - Full CRUD operations (Create, Read, Update, Delete)
 * - Search and filtering capabilities
 * - Usage statistics and analytics
 * - Comprehensive error handling
 * - Swagger API documentation
 * - Input validation and sanitization
 */
@ApiTags('Purpose Categories')
@Controller('purpose-categories')
export class PurposeCategoryController {
  constructor(private readonly purposeCategoryService: PurposeCategoryService) {}

  /**
   * Create a new Purpose Category
   * POST /purpose-categories
   */
  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new purpose category',
    description:
      'Creates a new purpose category with the provided name and description. ' +
      'The name must be unique (case-insensitive). Returns the created category with usage statistics.',
  })
  @ApiCreatedResponse({
    description: 'Purpose category created successfully',
    type: PurposeCategoryResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or validation failed',
  })
  @ApiConflictResponse({
    description: 'Purpose category with this name already exists',
  })
  async create(
    @Body(ValidationPipe) createPurposeCategoryDto: CreatePurposeCategoryDto,
    @Request() req: any,
  ): Promise<PurposeCategoryResponseDto> {
    return this.purposeCategoryService.create(createPurposeCategoryDto, req.user.id);
  }

  /**
   * Get all purpose categories with optional search
   * GET /purpose-categories
   */
  @Get()
  @ApiOperation({
    summary: 'Get all purpose categories',
    description:
      'Retrieves all purpose categories with optional search filtering. ' +
      'Search is performed across name and description fields (case-insensitive). ' +
      'Results include usage statistics for each category.',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term to filter categories by name or description',
    example: 'sales',
  })
  @ApiOkResponse({
    description: 'Purpose categories retrieved successfully',
    type: PurposeCategoryListResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid query parameters',
  })
  async findAll(
    @Query('search') search?: string,
  ): Promise<PurposeCategoryListResponseDto> {
    return this.purposeCategoryService.findAll(search);
  }

  /**
   * Get usage statistics for all categories
   * GET /purpose-categories/statistics
   */
  @Get('statistics')
  @ApiOperation({
    summary: 'Get usage statistics for purpose categories',
    description:
      'Provides comprehensive analytics about category usage across the system. ' +
      'Includes total counts, most used categories, and unused categories list.',
  })
  @ApiOkResponse({
    description: 'Usage statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        total_categories: { type: 'number', example: 10 },
        used_categories: { type: 'number', example: 7 },
        unused_categories: { type: 'number', example: 3 },
        total_purposes: { type: 'number', example: 50 },
        most_used_categories: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              purposes_count: { type: 'number' },
            },
          },
        },
        unused_category_list: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              description: { type: 'string', nullable: true },
            },
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Failed to retrieve statistics',
  })
  async getStatistics() {
    return this.purposeCategoryService.getUsageStatistics();
  }

  /**
   * Get a specific purpose category by ID
   * GET /purpose-categories/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get a purpose category by ID',
    description:
      'Retrieves a specific purpose category by its UUID. ' +
      'Includes comprehensive usage statistics and relationship data.',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'UUID of the purpose category to retrieve',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiOkResponse({
    description: 'Purpose category retrieved successfully',
    type: PurposeCategoryResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Purpose category not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid UUID format',
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<PurposeCategoryResponseDto> {
    return this.purposeCategoryService.findOne(id);
  }

  /**
   * Update a purpose category
   * PATCH /purpose-categories/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update a purpose category',
    description:
      'Updates an existing purpose category with the provided data. ' +
      'Supports partial updates - only provided fields will be updated. ' +
      'Name uniqueness is validated (excluding the current record).',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'UUID of the purpose category to update',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiOkResponse({
    description: 'Purpose category updated successfully',
    type: PurposeCategoryResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Purpose category not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or UUID format',
  })
  @ApiConflictResponse({
    description: 'Purpose category with this name already exists',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updatePurposeCategoryDto: UpdatePurposeCategoryDto,
  ): Promise<PurposeCategoryResponseDto> {
    return this.purposeCategoryService.update(id, updatePurposeCategoryDto);
  }

  /**
   * Delete a purpose category
   * DELETE /purpose-categories/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete a purpose category',
    description:
      'Deletes a purpose category by its UUID. ' +
      'Deletion is prevented if the category is currently in use by any purposes ' +
      'to maintain data integrity. Returns confirmation message on success.',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'UUID of the purpose category to delete',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiOkResponse({
    description: 'Purpose category deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Purpose category "Sales Activities" deleted successfully',
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Purpose category not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid UUID format',
  })
  @ApiConflictResponse({
    description: 'Cannot delete category as it is currently in use by purposes',
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<{ message: string }> {
    return this.purposeCategoryService.remove(id);
  }
}
