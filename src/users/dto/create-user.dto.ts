import { <PERSON><PERSON><PERSON>Empty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for creating a new user
 * 
 * This DTO validates input data for user creation and provides
 * comprehensive API documentation for Swagger/OpenAPI.
 * 
 * @class CreateUserDto
 */
export class CreateUserDto {
  @ApiProperty({
    description: 'Full name of the user',
    example: '<PERSON>',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'User name is required' })
  @IsString({ message: 'User name must be a string' })
  @MaxLength(255, { message: 'User name cannot exceed 255 characters' })
  name: string;

  @ApiProperty({
    description: 'Email address of the user (must be unique)',
    example: '<EMAIL>',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @MaxLength(255, { message: 'Em<PERSON> cannot exceed 255 characters' })
  email: string;

  @ApiProperty({
    description: 'Phone number of the user. Supports international format (+**********) or local formats (07XXXXXXXX, 01XXXXXXXX)',
    example: '+********** or ********** or **********',
    maxLength: 20,
  })
  @IsNotEmpty({ message: 'Phone number is required' })
  @IsString({ message: 'Phone number must be a string' })
  @MaxLength(20, { message: 'Phone number cannot exceed 20 characters' })
  @Matches(/^(\+[\d]{1,4}[\d]{4,15}|0[17][\d]{8})$/, {
    message: 'Phone number must be in international format (+**********) or local format (07XXXXXXXX or 01XXXXXXXX)'
  })
  phone_number: string;

  @ApiProperty({
    description: 'Password for the user account (will be hashed before storage)',
    example: 'SecurePassword123!',
    minLength: 8,
    maxLength: 100,
  })
  @IsNotEmpty({ message: 'Password is required' })
  @IsString({ message: 'Password must be a string' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @MaxLength(100, { message: 'Password cannot exceed 100 characters' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
  })
  password: string;

  @ApiProperty({
    description: 'Relationship Manager code for the user',
    example: 'RM001',
    maxLength: 50,
  })
  @IsNotEmpty({ message: 'RM code is required' })
  @IsString({ message: 'RM code must be a string' })
  @MaxLength(50, { message: 'RM code cannot exceed 50 characters' })
  rm_code: string;

  @ApiProperty({
    description: 'UUID of the role to assign to this user',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Role ID is required' })
  @IsUUID('4', { message: 'Role ID must be a valid UUID' })
  role_id: string;

  @ApiProperty({
    description: 'UUID of the branch this user belongs to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Branch ID is required' })
  @IsUUID('4', { message: 'Branch ID must be a valid UUID' })
  branch_id: string;

  @ApiPropertyOptional({
    description: 'Last login timestamp (optional, usually set by system)',
    example: '2024-01-15T10:30:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  @IsOptional()
  last_login?: Date;

  @ApiPropertyOptional({
    description: 'UUID of the user who added this user (optional)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Added by user ID must be a valid UUID' })
  added_by?: string;
}
