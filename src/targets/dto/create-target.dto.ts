import {
  IsString,
  IsNumber,
  IsDateString,
  IsIn,
  IsArray,
  IsUUID,
  ArrayNotEmpty,
  Min,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';

/**
 * Data Transfer Object for date range
 */
export class DateRangeDto {
  @ApiProperty({
    description: 'Start date of the range',
    example: '2025-08-01T02:00:00.000Z',
  })
  @IsDateString({}, { message: 'Start date must be a valid ISO date string' })
  startDate: string;

  @ApiProperty({
    description: 'End date of the range',
    example: '2025-08-31T20:59:59.999Z',
  })
  @IsDateString({}, { message: 'End date must be a valid ISO date string' })
  endDate: string;
}

/**
 * Data Transfer Object for creating targets
 */
export class CreateTargetDto {
  @ApiProperty({
    description: 'Type of metric to track',
    example: 'Call',
    enum: ['Call', 'Visit'],
  })
  @IsString({ message: 'Metric type must be a string' })
  @IsIn(['Call', 'Visit'], {
    message: 'Metric type must be either "Call" or "Visit"',
  })
  metricType: string;

  @ApiProperty({
    description: 'Target value to achieve',
    example: 25,
    minimum: 1,
  })
  @IsNumber({}, { message: 'Target value must be a number' })
  @Min(1, { message: 'Target value must be at least 1' })
  targetValue: number;

  @ApiProperty({
    description: 'Frequency of the target (only daily is supported)',
    example: 'daily',
    enum: ['daily'],
  })
  @IsString({ message: 'Frequency must be a string' })
  @IsIn(['daily'], {
    message: 'Frequency must be "daily"',
  })
  frequency: string;

  @ApiProperty({
    description: 'Type of activity for the target',
    example: 'LEADS_HITLIST',
    enum: ['LEADS', 'CUSTOMER_SERVICE', 'LOAN_ACTIVITIES'],
  })
  @IsString({ message: 'Activity must be a string' })
  @IsIn(['LEADS', 'CUSTOMER_SERVICE', 'LOAN_ACTIVITIES'], {
    message:
      'Activity must be one of: LEADS, CUSTOMER_SERVICE, LOAN_ACTIVITIES',
  })
  activity: string;

  @ApiProperty({
    description: 'Start date of the target period',
    example: '2025-08-01',
  })
  @IsDateString({}, { message: 'Start date must be a valid date string' })
  startDate: string;

  @ApiPropertyOptional({
    description: 'End date of the target period (optional, can be null)',
    example: '2025-08-31',
    nullable: true,
  })
  @IsOptional()
  @IsDateString(
    {},
    { message: 'End date must be a valid date string when provided' },
  )
  endDate?: string | null;

  @ApiProperty({
    description: 'Scope of assignment - either "Role" or "Individual"',
    example: 'Role',
    enum: ['Role', 'Individual'],
  })
  @IsString({ message: 'Scope must be a string' })
  @Transform(({ value }) => value.toLowerCase())
  @IsIn(['role', 'individual'], {
    message: 'Scope must be either "Role" or "Individual" (case-insensitive)',
  })
  scope: string;

  @ApiProperty({
    description:
      'Array of UUIDs to assign targets to (roles or users based on scope)',
    example: [
      '550e8400-e29b-41d4-a716-************',
      '660e8400-e29b-41d4-a716-************',
    ],
    type: [String],
  })
  @IsArray({ message: 'AssignTo must be an array' })
  @ArrayNotEmpty({ message: 'AssignTo array cannot be empty' })
  @IsUUID(4, { each: true, message: 'Each assignTo item must be a valid UUID' })
  assignTo: string[];

  @ApiPropertyOptional({
    description: 'Array of date ranges for custom frequency targets (optional)',
    example: [
      {
        startDate: '2025-08-01T02:00:00.000Z',
        endDate: '2025-08-07T20:59:59.999Z',
      },
      {
        startDate: '2025-08-08T02:00:00.000Z',
        endDate: '2025-08-14T20:59:59.999Z',
      },
    ],
    type: [DateRangeDto],
    nullable: true,
  })
  @IsOptional()
  @IsArray({ message: 'Ranges must be an array when provided' })
  @ValidateNested({ each: true })
  @Type(() => DateRangeDto)
  ranges?: DateRangeDto[] | null;
}
