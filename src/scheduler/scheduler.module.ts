import { Module } from '@nestjs/common';
import { SchedulerService } from './scheduler.service';
import { QueueProducerModule } from '../queue/queue-producer.module';
import { PrismaModule } from '../prisma/prisma.module';
import { ScheduledTaskServiceModule } from '../scheduled-tasks/scheduled-task-service.module';

@Module({
  imports: [QueueProducerModule, PrismaModule, ScheduledTaskServiceModule],
  providers: [SchedulerService],
  exports: [SchedulerService],
})
export class SchedulerModule {}
