import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  IsDateString,
  IsIn,
  IsOptional,
  Min,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for updating targets
 */
export class UpdateTargetDto {
  @ApiPropertyOptional({
    description: 'Type of metric to track',
    example: 'Call',
    enum: ['Call', 'Visit'],
  })
  @IsOptional()
  @IsString({ message: 'Metric type must be a string' })
  @IsIn(['Call', 'Visit'], {
    message: 'Metric type must be either "Call" or "Visit"',
  })
  metricType?: string;

  @ApiPropertyOptional({
    description: 'Target value to achieve',
    example: 25,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Target value must be a number' })
  @Min(1, { message: 'Target value must be at least 1' })
  targetValue?: number;

  @ApiPropertyOptional({
    description: 'Frequency of the target',
    example: 'weekly',
    enum: ['daily', 'weekly', 'custom'],
  })
  @IsOptional()
  @IsString({ message: 'Frequency must be a string' })
  @IsIn(['daily', 'weekly', 'custom'], {
    message: 'Frequency must be either "daily", "weekly", or "custom"',
  })
  frequency?: string;

  @ApiPropertyOptional({
    description: 'Type of activity for the target',
    example: 'LEADS_HITLIST',
    enum: ['LEADS', 'CUSTOMER_SERVICE', 'LOAN_ACTIVITIES'],
  })
  @IsOptional()
  @IsString({ message: 'Activity must be a string' })
  @IsIn(['LEADS', 'CUSTOMER_SERVICE', 'LOAN_ACTIVITIES'], {
    message:
      'Activity must be one of: LEADS, CUSTOMER_SERVICE, LOAN_ACTIVITIES',
  })
  activity?: string;

  @ApiPropertyOptional({
    description: 'Start date of the target period',
    example: '2025-08-01',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Start date must be a valid date string' })
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date of the target period',
    example: '2025-08-31',
  })
  @IsOptional()
  @IsDateString({}, { message: 'End date must be a valid date string' })
  endDate?: string;
}
