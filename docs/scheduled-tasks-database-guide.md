# Scheduled Tasks Database Guide

This guide explains how to add scheduled tasks directly to the database, including all field descriptions and examples for different types of tasks.

## Table Structure

The `scheduled_tasks` table contains the following fields:

### Required Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `id` | UUID | Unique identifier for the task | `gen_random_uuid()` |
| `name` | VARCHAR | Human-readable name for the task | `'Daily Report Task'` |
| `type` | VARCHAR | Task type (must match registered handler) | `'console-log'` |
| `payload` | JSONB | Task data/parameters | `'{"message": "Hello World"}'` |
| `run_at` | TIMESTAMP | When the task should first run | `NOW() + INTERVAL '1 hour'` |
| `updated_at` | TIMESTAMP | Last update timestamp | `NOW()` |

### Optional Fields

| Field | Type | Description | Default | Example |
|-------|------|-------------|---------|---------|
| `description` | VARCHAR | Detailed task description | `NULL` | `'Sends daily reports to users'` |
| `status` | ENUM | Task status | `'PENDING'` | `'PENDING'`, `'RUNNING'`, `'COMPLETED'`, `'FAILED'`, `'CANCELLED'` |
| `priority` | INTEGER | Task priority (0-10, higher = more important) | `0` | `5` |
| `max_attempts` | INTEGER | Maximum retry attempts | `3` | `5` |
| `attempts` | INTEGER | Current attempt count | `0` | `0` |
| `queue_name` | VARCHAR | Queue name for processing | `'default'` | `'high-priority'` |
| `created_by` | VARCHAR | User who created the task | `NULL` | `'<EMAIL>'` |

### Recurring Task Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `interval_type` | ENUM | Interval type for recurring tasks | `'MINUTES'`, `'HOURS'`, `'DAYS'`, `'WEEKS'`, `'MONTHS'` |
| `interval_value` | INTEGER | Interval value (e.g., 5 for every 5 hours) | `30` |
| `cron_expression` | VARCHAR | Cron expression for complex schedules | `'0 9 * * 1-5'` |
| `next_run_at` | TIMESTAMP | Next scheduled run time (auto-calculated) | Auto-calculated |

**Note:** Use either `interval_type`/`interval_value` OR `cron_expression`, not both.

## Task Types

### Available Task Types

1. **`console-log`** - Logs a message to the console
2. **`daily-nairobi-task`** - Example daily task with timezone handling

## Examples

### 1. One-Time Task

```sql
INSERT INTO scheduled_tasks (
    id, name, type, payload, run_at, priority, max_attempts, updated_at
) VALUES (
    gen_random_uuid(),
    'One-Time Console Log',
    'console-log',
    '{"message": "This runs once at the specified time"}',
    NOW() + INTERVAL '30 minutes',
    5,
    3,
    NOW()
);
```

### 2. Interval-Based Recurring Task

```sql
-- Every 15 minutes
INSERT INTO scheduled_tasks (
    id, name, type, payload, run_at, 
    interval_type, interval_value, priority, max_attempts, updated_at
) VALUES (
    gen_random_uuid(),
    'Every 15 Minutes Task',
    'console-log',
    '{"message": "This runs every 15 minutes"}',
    NOW() + INTERVAL '1 minute',
    'MINUTES',
    15,
    7,
    3,
    NOW()
);
```

```sql
-- Every 2 hours
INSERT INTO scheduled_tasks (
    id, name, type, payload, run_at,
    interval_type, interval_value, priority, max_attempts, updated_at
) VALUES (
    gen_random_uuid(),
    'Every 2 Hours Task',
    'console-log',
    '{"message": "This runs every 2 hours"}',
    NOW() + INTERVAL '5 minutes',
    'HOURS',
    2,
    6,
    3,
    NOW()
);
```

```sql
-- Daily task
INSERT INTO scheduled_tasks (
    id, name, type, payload, run_at,
    interval_type, interval_value, priority, max_attempts, updated_at
) VALUES (
    gen_random_uuid(),
    'Daily Backup Task',
    'daily-nairobi-task',
    '{"message": "Daily backup completed", "backup_type": "full"}',
    NOW() + INTERVAL '1 hour',
    'DAYS',
    1,
    8,
    3,
    NOW()
);
```

### 3. Cron Expression Tasks

```sql
-- Every 2 minutes
INSERT INTO scheduled_tasks (
    id, name, type, payload, run_at, cron_expression, priority, max_attempts, updated_at
) VALUES (
    gen_random_uuid(),
    'Every 2 Minutes Cron Task',
    'console-log',
    '{"message": "Hello from cron task! Runs every 2 minutes."}',
    NOW() + INTERVAL '1 minute',
    '*/2 * * * *',
    5,
    3,
    NOW()
);
```

```sql
-- Every weekday at 9 AM Nairobi time (6 AM UTC)
INSERT INTO scheduled_tasks (
    id, name, type, payload, run_at, cron_expression, priority, max_attempts, updated_at
) VALUES (
    gen_random_uuid(),
    'Weekday Morning Task',
    'console-log',
    '{"message": "Good morning! Weekday 9 AM Nairobi time task."}',
    NOW() + INTERVAL '1 hour',
    '0 6 * * 1-5',
    8,
    3,
    NOW()
);
```

```sql
-- Every day at midnight Nairobi time (9 PM UTC)
INSERT INTO scheduled_tasks (
    id, name, type, payload, run_at, cron_expression, priority, max_attempts, updated_at
) VALUES (
    gen_random_uuid(),
    'Daily Midnight Task',
    'daily-nairobi-task',
    '{"message": "Daily midnight processing", "timezone": "Africa/Nairobi"}',
    NOW() + INTERVAL '2 hours',
    '0 21 * * *',
    9,
    3,
    NOW()
);
```

```sql
-- First day of every month at 8 AM Nairobi time (5 AM UTC)
INSERT INTO scheduled_tasks (
    id, name, type, payload, run_at, cron_expression, priority, max_attempts, updated_at
) VALUES (
    gen_random_uuid(),
    'Monthly Report Task',
    'console-log',
    '{"message": "Monthly report generation", "report_type": "summary"}',
    NOW() + INTERVAL '3 hours',
    '0 5 1 * *',
    10,
    3,
    NOW()
);
```

## Cron Expression Format

Cron expressions use 5 fields: `minute hour day month day-of-week`

```
* * * * *
│ │ │ │ │
│ │ │ │ └─── Day of week (0-7, Sunday = 0 or 7)
│ │ │ └───── Month (1-12)
│ │ └─────── Day of month (1-31)
│ └───────── Hour (0-23)
└─────────── Minute (0-59)
```

### Common Cron Examples

| Expression | Description |
|------------|-------------|
| `*/5 * * * *` | Every 5 minutes |
| `0 * * * *` | Every hour at minute 0 |
| `0 9 * * *` | Every day at 9:00 AM |
| `0 9 * * 1-5` | Every weekday at 9:00 AM |
| `0 0 1 * *` | First day of every month at midnight |
| `0 0 * * 0` | Every Sunday at midnight |
| `30 14 * * 1,3,5` | Every Monday, Wednesday, Friday at 2:30 PM |

## Timezone Considerations

- All times are stored in UTC in the database
- Cron expressions are evaluated in **Africa/Nairobi timezone** by default
- To schedule for 9 AM Nairobi time, use `0 6 * * *` (6 AM UTC = 9 AM EAT)
- Nairobi is UTC+3 (no daylight saving time)

## Monitoring Tasks

### Check Task Status
```sql
SELECT id, name, type, status, run_at, next_run_at, attempts, max_attempts, created_at
FROM scheduled_tasks 
ORDER BY created_at DESC;
```

### View Recent Executions
```sql
SELECT st.name, st.type, ste.status, ste.started_at, ste.completed_at, ste.error_message
FROM scheduled_tasks st
JOIN scheduled_task_executions ste ON st.id = ste.task_id
ORDER BY ste.started_at DESC
LIMIT 20;
```

### Find Failed Tasks
```sql
SELECT id, name, type, status, error_message, failed_at, attempts, max_attempts
FROM scheduled_tasks 
WHERE status = 'FAILED'
ORDER BY failed_at DESC;
```

## Best Practices

1. **Always set `updated_at`** to `NOW()` when inserting
2. **Use meaningful names** that describe what the task does
3. **Set appropriate priorities** (0-10, higher = more important)
4. **Consider max_attempts** based on task criticality
5. **Use cron expressions** for complex scheduling needs
6. **Test with near-future times** first (e.g., `NOW() + INTERVAL '1 minute'`)
7. **Monitor task executions** regularly for failures
8. **Clean up old completed tasks** periodically

## Troubleshooting

- **Task not running**: Check if `run_at` is in the future and status is `PENDING`
- **Cron not working**: Verify cron expression syntax using online validators
- **Task failing**: Check `scheduled_task_executions` table for error messages
- **Wrong timezone**: Remember Nairobi is UTC+3, adjust cron expressions accordingly
