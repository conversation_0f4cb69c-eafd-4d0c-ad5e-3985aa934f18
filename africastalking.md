A) WHEN A USER IS ON AND PICKS UP:
Here is the data sent by africa's talking to:

1. /voice/callback:

{
"callSessionState": "Ringing",
"direction": "Inbound",
"callerCountryCode": "-1",
"callerNumber": "kb_tracker.kbtracker-1757149460516",
"sessionId": "ATVId_d534417f8ea423cb143dbdb0e5f54ef1",
"clientDialedNumber": "+254729165447",
"destinationNumber": "+254711082660",
"callerCarrierName": "None",
"callStartTime": "2025-09-06 09:04:24",
"isActive": "1"
}

2. POST /voice/events:

   {
   "callSessionState": "Active",
   "direction": "Inbound",
   "callerCountryCode": "-1",
   "callerNumber": "kb_tracker.kbtracker-1757148680998",
   "sessionId": "ATVId_8663ae20e7b0c70d2e012a2311148e20",
   "clientDialedNumber": "+254729165447",
   "destinationNumber": "+254711082660",
   "callerCarrierName": "None",
   "callStartTime": "2025-09-06 08:51:25",
   "isActive": "1"
   }

3. POST /voice/events:

{
"callSessionState": "Dialing",
"direction": "Inbound",
"callerCountryCode": "-1",
"callerNumber": "kb_tracker.kbtracker-1757148680998",
"dialDestinationNumbers": "+254729165447",
"sessionId": "ATVId_8663ae20e7b0c70d2e012a2311148e20",
"clientDialedNumber": "+254729165447",
"destinationNumber": "+254711082660",
"callerCarrierName": "None",
"callStartTime": "2025-09-06 08:51:25",
"isActive": "1"
}

4. POST /voice/events:

{
"direction": "Inbound",
"callerCountryCode": "-1",
"callerNumber": "kb_tracker.kbtracker-1757148680998",
"sessionId": "ATVId_8663ae20e7b0c70d2e012a2311148e20",
"clientDialedNumber": "+254729165447",
"callSessionState": "Bridged",
"dialDestinationNumber": "+254729165447",
"destinationNumber": "+254711082660",
"callerCarrierName": "None",
"callStartTime": "2025-09-06 08:52:02",
"isActive": "1"
}

5. POST /voice/callback:
   {
   "direction": "Inbound",
   "callerCountryCode": "-1",
   "durationInSeconds": "8",
   "amount": "0.3666666666666666575",
   "callerNumber": "kb_tracker.kbtracker-1757149460516",
   "dialStartTime": "2025-09-06 09:04:34",
   "callSessionState": "Completed",
   "dialDestinationNumber": "+254729165447",
   "destinationNumber": "+254711082660",
   "callerCarrierName": "None",
   "status": "Success",
   "sessionId": "ATVId_d534417f8ea423cb143dbdb0e5f54ef1",
   "recordingUrl": "https://jolly-heisenberg-meninsky.at-internal.com/a8941b2cf1d2235407ec7890ee1e2463.mp3",
   "callStartTime": "2025-09-06 09:04:34",
   "isActive": "0",
   "currencyCode": "KES",
   "dialDurationInSeconds": "8",
   "clientDialedNumber": "+254729165447"
   }

6. POST /voice/events:
   same as number 5

B) WHEN A USER IS ON AND DOESN'T PICKS UP

1. /voice/callback:
   same as above (A)
2. POST /voice/events:
   same as above (A)
3. POST /voice/events:
   same as above (A)
4. POST /voice/events:
   {
   "callSessionState": "DialCompleted",
   "hangupCause": "NO_ANSWER",
   "direction": "Inbound",
   "callerCountryCode": "-1",
   "callerNumber": "kb_tracker.kbtracker-1757150063987",
   "sessionId": "ATVId_b6ff178369a22cfecc6bce77e6252a45",
   "clientDialedNumber": "+254729165447",
   "destinationNumber": "+254711082660",
   "callerCarrierName": "None",
   "callStartTime": "2025-09-06 09:14:29",
   "isActive": "1"
   }

5. POST /voice/callback:
   {
   "callSessionState": "Completed",
   "direction": "Inbound",
   "callerCountryCode": "-1",
   "durationInSeconds": "0",
   "amount": "0.000",
   "sessionId": "ATVId_b6ff178369a22cfecc6bce77e6252a45",
   "clientDialedNumber": "+254729165447",
   "lastBridgeHangupCause": "NO_ANSWER",
   "callerNumber": "kb_tracker.kbtracker-1757150063987",
   "destinationNumber": "+254711082660",
   "callerCarrierName": "None",
   "status": "Success",
   "callStartTime": "2025-09-06 09:14:29",
   "isActive": "0",
   "currencyCode": "KES"
   }

6. POST /voice/events:
   same as number 5

C) WHEN A USER IS ON AND HANGS UP

1. /voice/callback:
   same as above (A)

2. POST /voice/events:
   same as above (A)

3. POST /voice/events:
   same as above (A)

4. POST /voice/events:
   {
   "callSessionState": "DialCompleted",
   "hangupCause": "USER_BUSY",
   "direction": "Inbound",
   "callerCountryCode": "-1",
   "callerNumber": "kb_tracker.kbtracker-1757150450586",
   "sessionId": "ATVId_a5349234f7ab2d3399c31aefadde54ce",
   "clientDialedNumber": "+254729165447",
   "destinationNumber": "+254711082660",
   "callerCarrierName": "None",
   "callStartTime": "2025-09-06 09:21:24",
   "isActive": "1"
   }

5. POST /voice/callback:
   {
   "callSessionState": "Completed",
   "direction": "Inbound",
   "callerCountryCode": "-1",
   "durationInSeconds": "0",
   "amount": "0.000",
   "sessionId": "ATVId_a5349234f7ab2d3399c31aefadde54ce",
   "clientDialedNumber": "+254729165447",
   "lastBridgeHangupCause": "USER_BUSY",
   "callerNumber": "kb_tracker.kbtracker-1757150450586",
   "destinationNumber": "+254711082660",
   "callerCarrierName": "None",
   "status": "Success",
   "callStartTime": "2025-09-06 09:21:24",
   "isActive": "0",
   "currencyCode": "KES"
   }

6. POST /voice/events:
   same as number 5

D) WHEN A USER'S PHONE IS OFF

1. /voice/callback:
   same as above (A)

2. POST /voice/events:
   same as above (A)

3. POST /voice/events:
   same as above (A)

4. POST /voice/events:
   {
   "direction": "Inbound",
   "callerCountryCode": "-1",
   "callerNumber": "kb_tracker.kbtracker-1757150876062",
   "sessionId": "ATVId_8457d8a7b80a066a2e81a9a0c48e068b",
   "clientDialedNumber": "+254729165447",
   "callSessionState": "Bridged",
   "dialDestinationNumber": "+254729165447",
   "destinationNumber": "+254711082660",
   "callerCarrierName": "None",
   "callStartTime": "2025-09-06 09:28:20",
   "isActive": "1"
   }

5. POST /voice/callback:

{
"direction": "Inbound",
"callerCountryCode": "-1",
"durationInSeconds": "16",
"amount": "0.6916666666666666650",
"callerNumber": "kb_tracker.kbtracker-1757150876062",
"dialStartTime": "2025-09-06 09:28:20",
"callSessionState": "Completed",
"dialDestinationNumber": "+254729165447",
"destinationNumber": "+254711082660",
"callerCarrierName": "None",
"status": "Success",
"sessionId": "ATVId_8457d8a7b80a066a2e81a9a0c48e068b",
"recordingUrl": "https://jolly-heisenberg-meninsky.at-internal.com/6c571a95a9b24b78e77cad4bc2f05be3.mp3",
"callStartTime": "2025-09-06 09:28:20",
"isActive": "0",
"currencyCode": "KES",
"dialDurationInSeconds": "15",
"clientDialedNumber": "+254729165447"
}

6. POST /voice/events:
   same as number 5
