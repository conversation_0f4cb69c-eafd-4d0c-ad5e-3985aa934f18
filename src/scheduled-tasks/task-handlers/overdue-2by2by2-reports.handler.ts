import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';
import { EmailService } from '../../common/services/email.service';
import { isApplicable } from '../../common/utils/date.utils';
import { NotificationUtils } from '../../notifications/utils/notification.utils';
import { NotificationType } from '@prisma/client';
// Initialize pdfmake
const pdfMake = require('pdfmake/build/pdfmake');
const pdfFonts = require('pdfmake/build/vfs_fonts');

// Set fonts for pdfmake (pdfFonts contains the font data directly)
pdfMake.vfs = pdfFonts;

export interface Overdue2by2by2ReportsPayload {
  // Empty payload - uses current date
}

interface Overdue2by2by2Phase {
  id: string;
  type: string;
  execution_date: Date | null;
  expected_completion_date: Date | null;
  customer_service_hitlist_record: {
    customer_name: string;
    account_number: string;
  };
  assigned_to: {
    id: string;
    name: string;
    branch: {
      id: string;
      name: string;
      region: {
        id: string;
        name: string;
      };
    };
  };
}

interface UserWithPermissions {
  id: string;
  name: string;
  email: string;
  branch: {
    id: string;
    name: string;
    region: {
      id: string;
      name: string;
    };
  };
  role: {
    role_permissions: Array<{
      permission: {
        id: string;
      };
    }>;
  };
}

@Injectable()
export class Overdue2by2by2ReportsHandler implements TaskHandler {
  private readonly logger = new Logger(Overdue2by2by2ReportsHandler.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly emailService: EmailService,
    private readonly notificationUtils: NotificationUtils,
  ) {}

  getTaskType(): string {
    return 'overdue-2by2by2-reports';
  }

  getDescription(): string {
    return 'Sends overdue 2by2by2 reports to users with appropriate permissions';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    return { isValid: true, errors: [] };
  }

  async handle(payload: Overdue2by2by2ReportsPayload, job: Job): Promise<any> {
    this.logger.log('Processing overdue 2by2by2 reports');

    let reportsSent = 0;
    let overdueCount = 0;
    let usersProcessed = 0;

    try {
      await job.updateProgress(5);

      // Check if today is applicable for sending reports (not weekend or holiday)
      const today = new Date();
      const applicable = await isApplicable(today, this.prisma);

      if (!applicable) {
        this.logger.log(
          'Skipping overdue 2by2by2 reports - today is a weekend or holiday',
        );
        return {
          success: true,
          overdueCount: 0,
          reportsSent: 0,
          usersProcessed: 0,
          skipped: true,
          reason: 'Weekend or holiday',
          processedAt: new Date().toISOString(),
        };
      }

      // Get today's date (DATE ONLY, no time)
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);

      // Get overdue 2by2by2 phases
      const overduePhases = (await this.prisma.twoByTwoPhase.findMany({
        where: {
          expected_completion_date: {
            lt: tomorrow,
          },
          is_completed: false,
        },
        include: {
          customer_service_hitlist_record: {
            select: {
              customer_name: true,
              account_number: true,
            },
          },
          assigned_to: {
            include: {
              branch: {
                include: {
                  region: true,
                },
              },
            },
          },
        },
        orderBy: [
          { assigned_to: { branch: { name: 'asc' } } },
          { expected_completion_date: 'asc' },
        ],
      })) as any[];

      await job.updateProgress(20);

      if (overduePhases.length === 0) {
        this.logger.log('No overdue 2by2by2 phases found');
        return {
          success: true,
          overdueCount: 0,
          reportsSent: 0,
          usersProcessed: 0,
          processedAt: new Date().toISOString(),
        };
      }

      overdueCount = overduePhases.length;
      this.logger.log(`Found ${overdueCount} overdue 2by2by2 phases`);

      // Get users with overdue 2by2by2 report permissions
      const usersWithPermissions = (await this.prisma.user.findMany({
        where: {
          role: {
            role_permissions: {
              some: {
                permission: {
                  id: {
                    in: [
                      'reports.overdue.2by2by2.all',
                      'reports.overdue.2by2by2.region',
                      'reports.overdue.2by2by2.branch',
                    ],
                  },
                },
              },
            },
          },
        },
        include: {
          branch: {
            include: {
              region: true,
            },
          },
          role: {
            include: {
              role_permissions: {
                include: {
                  permission: {
                    select: {
                      id: true,
                    },
                  },
                },
              },
            },
          },
        },
      })) as any[];

      await job.updateProgress(40);

      if (usersWithPermissions.length === 0) {
        this.logger.log(
          'No users with overdue 2by2by2 report permissions found',
        );
        return {
          success: true,
          overdueCount,
          reportsSent: 0,
          usersProcessed: 0,
          processedAt: new Date().toISOString(),
        };
      }

      this.logger.log(
        `Found ${usersWithPermissions.length} users with overdue 2by2by2 report permissions`,
      );

      const progressStep = 50 / usersWithPermissions.length;

      // Process each user with permissions
      for (const user of usersWithPermissions) {
        try {
          const userPermissions = user.role.role_permissions.map(
            (rp: any) => rp.permission.id,
          );

          let reportScope = '';
          let filteredPhases: Overdue2by2by2Phase[] = [];

          // Determine permission hierarchy and filter data
          if (userPermissions.includes('reports.overdue.2by2by2.all')) {
            reportScope = 'All Branches';
            filteredPhases = overduePhases;
          } else if (
            userPermissions.includes('reports.overdue.2by2by2.region')
          ) {
            reportScope = user.branch.region.name;
            filteredPhases = overduePhases.filter(
              (phase) =>
                phase.assigned_to.branch.region.id === user.branch.region.id,
            );
          } else if (
            userPermissions.includes('reports.overdue.2by2by2.branch')
          ) {
            reportScope = user.branch.name;
            filteredPhases = overduePhases.filter(
              (phase) => phase.assigned_to.branch.id === user.branch.id,
            );
          }

          if (filteredPhases.length > 0) {
            await this.generateAndSendReport(
              user,
              filteredPhases,
              reportScope,
              today,
            );
            reportsSent++;
          }

          usersProcessed++;
          await job.updateProgress(40 + progressStep * usersProcessed);
        } catch (error) {
          this.logger.error(
            `Failed to process overdue 2by2by2 report for user ${user.email}:`,
            error,
          );
        }
      }

      await job.updateProgress(100);

      this.logger.log(
        `Overdue 2by2by2 reports processing completed. Reports sent: ${reportsSent}, Users processed: ${usersProcessed}`,
      );

      return {
        success: true,
        overdueCount,
        reportsSent,
        usersProcessed,
        processedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to process overdue 2by2by2 reports:', error);
      throw error;
    }
  }

  private async generateAndSendReport(
    user: UserWithPermissions,
    overduePhases: Overdue2by2by2Phase[],
    reportScope: string,
    reportDate: Date,
  ): Promise<void> {
    try {
      // Generate PDF
      const pdfBuffer = await this.generatePDF(
        overduePhases,
        reportScope,
        reportDate,
      );

      // Send email with PDF attachment
      await this.emailService.sendEmailWithAttachments(
        user.email,
        `Overdue 2by2by2 Report – ${reportScope}`,
        'overdue-2by2by2-report',
        {
          userName: user.name,
          reportDate: reportDate.toLocaleDateString(),
          overdueCount: overduePhases.length,
          reportScope,
        },
        [
          {
            filename: `Overdue_2by2by2_Report_${reportScope.replace(/\s+/g, '_')}_${reportDate.toISOString().split('T')[0]}.pdf`,
            content: pdfBuffer,
            contentType: 'application/pdf',
          },
        ],
      );

      this.logger.log(
        `Successfully sent overdue 2by2by2 report to ${user.email}`,
      );

      // Create notification
      const notificationMessage = `${overduePhases.length} overdue 2by2by2 activities found. Check email for full report.`;

      await this.notificationUtils.createNotification({
        type: NotificationType.CUSTOM,
        title: 'Overdue 2by2by2 Report',
        message: notificationMessage,
        recipient_id: user.id,
        priority: 'HIGH' as any,
        data: {
          reportType: 'overdue-2by2by2',
          overdueCount: overduePhases.length,
          reportScope,
          reportDate: reportDate.toISOString(),
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to send overdue 2by2by2 report to ${user.email}:`,
        error,
      );
      throw error;
    }
  }

  private async generatePDF(
    overduePhases: Overdue2by2by2Phase[],
    reportScope: string,
    reportDate: Date,
  ): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        // Calculate overdue days and hours for each phase
        const phasesWithOverdue = overduePhases.map((phase) => {
          // Use expected_completion_date for overdue calculation since execution_date is null for incomplete phases
          const completionDate = phase.expected_completion_date;
          if (!completionDate) {
            // Fallback if expected_completion_date is also null
            return {
              ...phase,
              overdueDays: 0,
              overdueHours: 0,
              overdueDisplay: 'N/A',
            };
          }

          const overdueDays = Math.floor(
            (reportDate.getTime() - completionDate.getTime()) /
              (1000 * 60 * 60 * 24),
          );
          const overdueHours = Math.floor(
            ((reportDate.getTime() - completionDate.getTime()) %
              (1000 * 60 * 60 * 24)) /
              (1000 * 60 * 60),
          );

          return {
            ...phase,
            overdueDays,
            overdueHours,
            overdueDisplay:
              overdueDays > 0
                ? `${overdueDays}d ${overdueHours}h`
                : `${overdueHours}h`,
          };
        });

        const docDefinition = {
          content: [
            // Header
            {
              text: 'Overdue 2by2by2 Activities Report',
              style: 'header',
              alignment: 'center',
            },
            {
              text: `Report Date: ${reportDate.toLocaleDateString()}`,
              style: 'subheader',
              alignment: 'center',
            },
            {
              text: `Scope: ${reportScope}`,
              style: 'subheader',
              alignment: 'center',
            },
            { text: '', margin: [0, 20] }, // Spacer

            // Summary
            {
              text: 'Summary',
              style: 'sectionHeader',
            },
            {
              text: `Total Overdue Activities: ${overduePhases.length}`,
              style: 'normal',
            },
            { text: '', margin: [0, 20] }, // Spacer

            // Table
            {
              text: 'Overdue Activities Details',
              style: 'sectionHeader',
            },
            {
              table: {
                headerRows: 1,
                widths: ['*', 'auto', 'auto', 'auto', 'auto'],
                body: [
                  [
                    { text: 'Customer', style: 'tableHeader' },
                    { text: 'Phase', style: 'tableHeader' },
                    { text: 'Agent', style: 'tableHeader' },
                    { text: 'Account', style: 'tableHeader' },
                    { text: 'Overdue By', style: 'tableHeader' },
                  ],
                  ...phasesWithOverdue.map((phase) => [
                    {
                      text:
                        phase.customer_service_hitlist_record.customer_name ||
                        'N/A',
                      style: 'tableCell',
                    },
                    {
                      text: this.getPhaseDisplayName(phase.type),
                      style: 'tableCell',
                    },
                    { text: phase.assigned_to.name, style: 'tableCell' },
                    {
                      text:
                        phase.customer_service_hitlist_record.account_number ||
                        'N/A',
                      style: 'tableCell',
                    },
                    { text: phase.overdueDisplay, style: 'tableCell' },
                  ]),
                ],
              },
              layout: 'lightHorizontalLines',
            },

            // Footer
            {
              text: `Generated on ${new Date().toLocaleString()}`,
              style: 'footer',
              alignment: 'center',
              absolutePosition: { x: 0, y: 750 },
            },
          ],
          styles: {
            header: {
              fontSize: 20,
              bold: true,
              margin: [0, 0, 0, 10],
            },
            subheader: {
              fontSize: 14,
              margin: [0, 0, 0, 5],
            },
            sectionHeader: {
              fontSize: 14,
              bold: true,
              margin: [0, 10, 0, 5],
            },
            normal: {
              fontSize: 10,
              margin: [0, 2],
            },
            tableHeader: {
              fontSize: 10,
              bold: true,
              fillColor: '#f0f0f0',
              margin: [5, 5],
            },
            tableCell: {
              fontSize: 9,
              margin: [5, 3],
            },
            footer: {
              fontSize: 8,
              margin: [0, 10],
            },
          },
        };

        if (!pdfMake || typeof pdfMake.createPdf !== 'function') {
          throw new Error('pdfMake is not properly initialized');
        }

        const pdfDoc = pdfMake.createPdf(docDefinition);

        if (!pdfDoc || typeof pdfDoc.getBuffer !== 'function') {
          throw new Error('Failed to create PDF document');
        }

        pdfDoc.getBuffer((buffer: Buffer) => {
          if (!buffer) {
            reject(new Error('Failed to generate PDF buffer'));
          } else {
            resolve(buffer);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  private getPhaseDisplayName(type: string): string {
    switch (type) {
      case 'first2':
        return 'First 2';
      case 'second2':
        return 'Second 2';
      case 'third2':
        return 'Third 2';
      default:
        return type;
    }
  }
}
