import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  SupportTicketResponseDto,
  CreateSupportTicketDto,
  UpdateSupportTicketDto,
} from './dto/support-ticket.dto';

/**
 * Service handling support ticket operations
 */
@Injectable()
export class SupportTicketsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Gets all support tickets for the logged-in user
   */
  async getUserSupportTickets(userId: string): Promise<SupportTicketResponseDto[]> {
    const tickets = await this.prisma.supportTicket.findMany({
      where: { submitted_by: userId },
      orderBy: { created_at: 'desc' },
    });

    return tickets.map((ticket) => ({
      id: ticket.id.startsWith('ST-') ? ticket.id : this.generateTicketId(ticket.created_at), // Handle legacy tickets
      type: ticket.type,
      priority: ticket.priority,
      title: ticket.title,
      description: ticket.description,
      status: ticket.status,
      createdAt: ticket.created_at.toISOString().split('T')[0], // Format as YYYY-MM-DD
    }));
  }

  /**
   * Creates a new support ticket
   */
  async createSupportTicket(
    userId: string,
    createDto: CreateSupportTicketDto,
  ): Promise<SupportTicketResponseDto> {
    // Generate unique ticket ID
    const ticketId = await this.generateUniqueTicketId();

    const ticket = await this.prisma.supportTicket.create({
      data: {
        id: ticketId,
        type: createDto.type,
        priority: createDto.priority,
        title: createDto.title,
        description: createDto.description,
        status: createDto.status,
        submitted_by: userId,
      },
    });

    return {
      id: ticket.id,
      type: ticket.type,
      priority: ticket.priority,
      title: ticket.title,
      description: ticket.description,
      status: ticket.status,
      createdAt: ticket.created_at.toISOString().split('T')[0], // Format as YYYY-MM-DD
    };
  }

  /**
   * Updates a support ticket
   */
  async updateSupportTicket(
    ticketId: string,
    userId: string,
    updateDto: UpdateSupportTicketDto,
  ): Promise<SupportTicketResponseDto> {
    // Find the ticket by ID and ensure it belongs to the user
    const existingTicket = await this.prisma.supportTicket.findFirst({
      where: {
        id: ticketId,
        submitted_by: userId,
      },
    });

    if (!existingTicket) {
      throw new NotFoundException('Support ticket not found');
    }

    // Prepare update data - only include provided fields
    const updateData: any = {};
    if (updateDto.type !== undefined) updateData.type = updateDto.type;
    if (updateDto.priority !== undefined) updateData.priority = updateDto.priority;
    if (updateDto.title !== undefined) updateData.title = updateDto.title;
    if (updateDto.description !== undefined) updateData.description = updateDto.description;
    if (updateDto.status !== undefined) updateData.status = updateDto.status;

    const updatedTicket = await this.prisma.supportTicket.update({
      where: { id: ticketId },
      data: updateData,
    });

    return {
      id: updatedTicket.id,
      type: updatedTicket.type,
      priority: updatedTicket.priority,
      title: updatedTicket.title,
      description: updatedTicket.description,
      status: updatedTicket.status,
      createdAt: updatedTicket.created_at.toISOString().split('T')[0], // Format as YYYY-MM-DD
    };
  }

  /**
   * Deletes a support ticket
   */
  async deleteSupportTicket(ticketId: string, userId: string): Promise<void> {
    // Find the ticket by ID and ensure it belongs to the user
    const existingTicket = await this.prisma.supportTicket.findFirst({
      where: {
        id: ticketId,
        submitted_by: userId,
      },
    });

    if (!existingTicket) {
      throw new NotFoundException('Support ticket not found');
    }

    await this.prisma.supportTicket.delete({
      where: { id: ticketId },
    });
  }

  /**
   * Generates a unique ticket ID in the format ST-YYYY-XXXX
   */
  private async generateUniqueTicketId(): Promise<string> {
    const currentYear = new Date().getFullYear();

    // Get count of tickets created this year
    const startOfYear = new Date(currentYear, 0, 1);
    const endOfYear = new Date(currentYear, 11, 31, 23, 59, 59);

    const ticketsThisYear = await this.prisma.supportTicket.count({
      where: {
        created_at: {
          gte: startOfYear,
          lte: endOfYear,
        },
      },
    });

    const sequenceNumber = (ticketsThisYear + 1).toString().padStart(4, '0');
    return `ST-${currentYear}-${sequenceNumber}`;
  }

  /**
   * Generates a ticket ID in the format ST-YYYY-XXX (legacy method for existing tickets)
   */
  private generateTicketId(createdAt: Date): string {
    const year = createdAt.getFullYear();
    const dayOfYear = Math.floor(
      (createdAt.getTime() - new Date(year, 0, 0).getTime()) / (1000 * 60 * 60 * 24)
    );
    const paddedDay = dayOfYear.toString().padStart(3, '0');
    return `ST-${year}-${paddedDay}`;
  }
}
