import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { VoiceController } from './voice.controller';
import { WebRTCController } from './webrtc.controller';
import { VoiceService } from './voice.service';
import { VoiceXmlService } from './voice-xml.service';
import { InboundCallService } from './inbound-call.service';
import { OutboundCallService } from './outbound-call.service';
import { PrismaModule } from '../prisma/prisma.module';
import { ActivitiesModule } from '../activities/activities.module';

/**
 * Voice module for handling Africa's Talking voice callbacks and events
 *
 * This module provides:
 * - Voice callback endpoint (/voice/callback) for handling inbound/outbound call notifications
 * - Voice events endpoint (/voice/events) for receiving call lifecycle events
 * - WebRTC capability token endpoint (/get-capability-token) for frontend WebRTC integration
 * - Logging of all voice-related activities for monitoring and debugging
 */
@Module({
  imports: [ConfigModule, PrismaModule, ActivitiesModule],
  controllers: [VoiceController, WebRTCController],
  providers: [
    VoiceService,
    VoiceXmlService,
    InboundCallService,
    OutboundCallService,
  ],
  exports: [
    VoiceService,
    VoiceXmlService,
    InboundCallService,
    OutboundCallService,
  ],
})
export class VoiceModule {}
