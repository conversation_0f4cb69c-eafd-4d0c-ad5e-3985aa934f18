import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';
import { TargetProgressService } from '../../targets/services/target-progress.service';
import { isApplicable, getTodayAt2AM } from '../../common/utils/date.utils';

export interface TargetCreationPayload {
  metricType: 'Call' | 'Visit';
  targetValue: number;
  frequency: 'daily';
  startDate: string;
  endDate?: string | null;
  activity: 'LEADS' | 'CUSTOMER_SERVICE' | 'LOAN_ACTIVITIES';
  scope: 'role';
  assignTo: string[]; // role IDs
  userBranchId: string;
  loggedInUserId: string;
}

@Injectable()
export class TargetCreationHandler implements TaskHandler {
  private readonly logger = new Logger(TargetCreationHandler.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly targetProgressService: TargetProgressService,
  ) {}

  getTaskType(): string {
    return 'target-creation';
  }

  getDescription(): string {
    return 'Creates targets with permission-based user selection for role scope';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payload || typeof payload !== 'object') {
      errors.push('Payload must be an object');
      return { isValid: false, errors };
    }

    // Validate required fields
    const requiredFields = [
      'metricType',
      'targetValue',
      'frequency',
      'startDate',
      'activity',
      'scope',
      'assignTo',
      'userBranchId',
      'loggedInUserId',
    ];
    for (const field of requiredFields) {
      if (!payload[field]) {
        errors.push(`${field} is required`);
      }
    }

    // Validate specific field types and values
    if (payload.metricType && !['Call', 'Visit'].includes(payload.metricType)) {
      errors.push('metricType must be either "Call" or "Visit"');
    }

    if (payload.frequency && payload.frequency !== 'daily') {
      errors.push('frequency must be "daily"');
    }

    if (payload.scope && payload.scope !== 'role') {
      errors.push('scope must be "role"');
    }

    if (payload.assignTo && !Array.isArray(payload.assignTo)) {
      errors.push('assignTo must be an array');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  async handle(payload: TargetCreationPayload, job: Job): Promise<any> {
    this.logger.log('Starting target creation background task');
    console.log('Target Creation Handler: Processing payload', payload);
    await job.updateProgress(10);

    const {
      metricType,
      targetValue,
      frequency,
      startDate,
      endDate,
      activity,
      scope,
      assignTo,
      userBranchId,
      loggedInUserId,
    } = payload;

    try {
      // Validate role IDs exist
      await job.updateProgress(20);
      await this.validateRoles(assignTo);

      // Parse dates
      const start = new Date(startDate);
      const end = endDate ? new Date(endDate) : null;

      // Create targets for each role
      await job.updateProgress(30);
      const createdTargets = await this.createTargets(assignTo, {
        metricType,
        targetValue,
        frequency,
        activity,
        start,
        end,
        userBranchId,
      });

      // Get all users of the branch for each role
      await job.updateProgress(50);
      const allUserIds = new Set<string>();

      console.log(
        'Target Creation Handler: Getting users for roles:',
        assignTo,
        'in branch:',
        userBranchId,
      );

      for (const roleId of assignTo) {
        const userIds = await this.getUsersByBranchAndRole(
          roleId,
          userBranchId,
        );
        console.log(
          `Target Creation Handler: Role ${roleId} has ${userIds.length} users:`,
          userIds,
        );
        userIds.forEach((id) => allUserIds.add(id));
      }

      const userIdsArray = Array.from(allUserIds);
      this.logger.log(`Found ${userIdsArray.length} users for target creation`);
      console.log('Target Creation Handler: All eligible users:', userIdsArray);

      // Create target progress records for today
      await job.updateProgress(70);
      if (userIdsArray.length > 0) {
        const targetIds = createdTargets.map((t) => t.id);
        await this.createTargetProgressRecords(targetIds, userIdsArray);
      }

      await job.updateProgress(100);

      const result = {
        type: 'target-creation',
        success: true,
        createdTargets: createdTargets.length,
        affectedUsers: userIdsArray.length,
        targetIds: createdTargets.map((t) => t.id),
        timestamp: new Date().toISOString(),
      };

      this.logger.log(
        `Target creation completed successfully: ${JSON.stringify(result)}`,
      );
      console.log(
        'Target Creation Handler: Task completed successfully',
        result,
      );
      return result;
    } catch (error) {
      this.logger.error('Target creation failed:', error);
      console.error('Target Creation Handler: Task failed', error);
      throw error;
    }
  }

  private async validateRoles(roleIds: string[]): Promise<void> {
    const roles = await this.prisma.role.findMany({
      where: { id: { in: roleIds } },
      select: { id: true },
    });

    const foundRoleIds = roles.map((role) => role.id);
    const missingRoleIds = roleIds.filter((id) => !foundRoleIds.includes(id));

    if (missingRoleIds.length > 0) {
      throw new Error(
        `The following role IDs were not found: ${missingRoleIds.join(', ')}`,
      );
    }
  }

  private async createTargets(
    roleIds: string[],
    targetData: any,
  ): Promise<any[]> {
    const createdTargets: any[] = [];

    for (const roleId of roleIds) {
      const target = await this.prisma.target.create({
        data: {
          metric_type: targetData.metricType,
          target_value: targetData.targetValue,
          frequency: targetData.frequency,
          activity: targetData.activity,
          start_date: targetData.start,
          end_date: targetData.end || undefined,
          branch_id: targetData.userBranchId,
          role_id: roleId,
        },
      });
      createdTargets.push(target);
    }

    return createdTargets;
  }

  private async getUsersByBranchAndRole(
    roleId: string,
    userBranchId: string,
  ): Promise<string[]> {
    // Get all users of the specified role in the specified branch
    const users = await this.prisma.user.findMany({
      where: {
        role_id: roleId,
        branch_id: userBranchId,
      },
      select: { id: true },
    });

    return users.map((user) => user.id);
  }

  private async createTargetProgressRecords(
    targetIds: string[],
    userIds: string[],
  ): Promise<void> {
    console.log(
      'Target Creation Handler: Creating progress records for targets:',
      targetIds,
      'users:',
      userIds,
    );

    const today = new Date();
    const forDate = getTodayAt2AM();
    const applicable = await isApplicable(today, this.prisma);

    console.log('Target Creation Handler: Date info:', {
      today: today.toISOString(),
      forDate: forDate.toISOString(),
      applicable,
    });

    const progressRecords: any[] = [];

    for (const userId of userIds) {
      for (const targetId of targetIds) {
        // Get target value for this specific target
        const target = await this.prisma.target.findUnique({
          where: { id: targetId },
          select: { target_value: true },
        });

        console.log(
          `Target Creation Handler: Target ${targetId} has value:`,
          target?.target_value,
        );

        progressRecords.push({
          target_id: targetId,
          user_id: userId,
          for_date: forDate,
          achieved_count: 0,
          target_value: target?.target_value || 0,
          is_achieved: null,
          is_applicable: applicable,
        });
      }
    }

    console.log(
      `Target Creation Handler: Created ${progressRecords.length} progress records:`,
      progressRecords,
    );

    if (progressRecords.length > 0) {
      const result = await this.prisma.targetProgress.createMany({
        data: progressRecords,
        skipDuplicates: true,
      });
      console.log(
        'Target Creation Handler: Database createMany result:',
        result,
      );
    } else {
      console.log('Target Creation Handler: No progress records to create');
    }
  }
}
