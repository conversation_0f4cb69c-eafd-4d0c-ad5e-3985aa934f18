import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';
import { isApplicable, getTodayAt2AM } from '../../common/utils/date.utils';

export interface DailyTargetProgressGenerationPayload {
  date?: string; // Optional date, defaults to today
}

@Injectable()
export class DailyTargetProgressGenerationHandler implements TaskHandler {
  private readonly logger = new Logger(DailyTargetProgressGenerationHandler.name);

  constructor(
    private readonly prisma: PrismaService,
  ) {}

  getTaskType(): string {
    return 'daily-target-progress-generation';
  }

  getDescription(): string {
    return 'Generates missing target progress records for all active targets at 1 AM daily';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (payload && typeof payload !== 'object') {
      errors.push('Payload must be an object or null');
      return { isValid: false, errors };
    }

    // Validate optional date field
    if (payload?.date && typeof payload.date !== 'string') {
      errors.push('date must be a string');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  async handle(payload: DailyTargetProgressGenerationPayload, job: Job): Promise<any> {
    this.logger.log('Starting daily target progress generation background task');
    console.log('Daily Target Progress Generation Handler: Processing payload', payload);
    
    await job.updateProgress(5);

    const targetDate = payload?.date ? new Date(payload.date) : new Date();
    // Create forDate at 2 AM for the target date
    const forDate = new Date(
      targetDate.getFullYear(),
      targetDate.getMonth(),
      targetDate.getDate(),
      2, // 2 AM
      0,
      0,
      0
    );
    const applicable = await isApplicable(targetDate, this.prisma);

    console.log('Daily Target Progress Generation Handler: Target date:', targetDate.toISOString());
    console.log('Daily Target Progress Generation Handler: For date:', forDate.toISOString());
    console.log('Daily Target Progress Generation Handler: Is applicable:', applicable);

    try {
      // Get all active targets
      await job.updateProgress(10);
      const activeTargets = await this.prisma.target.findMany({
        where: {
          status: { not: 'Archived' },
          start_date: { lte: targetDate },
          OR: [
            { end_date: null },
            { end_date: { gte: targetDate } }
          ]
        },
        select: {
          id: true,
          target_value: true,
          branch_id: true,
          role_id: true,
          user_id: true,
          exempted_target_users: {
            select: {
              user_id: true,
            },
          },
        },
      });

      this.logger.log(`Found ${activeTargets.length} active targets to process`);
      console.log('Daily Target Progress Generation Handler: Found active targets', activeTargets.length);

      let totalProgressRecordsCreated = 0;
      const progressIncrement = 80 / activeTargets.length; // Reserve 80% for processing targets
      let currentProgress = 10;

      // Process each target
      for (const target of activeTargets) {
        const progressRecordsCreated = await this.processTarget(target, forDate, applicable);
        totalProgressRecordsCreated += progressRecordsCreated;
        
        currentProgress += progressIncrement;
        await job.updateProgress(Math.min(90, currentProgress));
      }

      await job.updateProgress(100);

      const result = {
        type: 'daily-target-progress-generation',
        success: true,
        processedTargets: activeTargets.length,
        createdProgressRecords: totalProgressRecordsCreated,
        date: targetDate.toISOString().split('T')[0],
        timestamp: new Date().toISOString(),
      };

      this.logger.log(`Daily target progress generation completed successfully: ${JSON.stringify(result)}`);
      console.log('Daily Target Progress Generation Handler: Task completed successfully', result);
      
      return result;

    } catch (error) {
      this.logger.error('Daily target progress generation failed:', error);
      console.error('Daily Target Progress Generation Handler: Task failed', error);
      throw error;
    }
  }

  private async processTarget(
    target: {
      id: string;
      target_value: number;
      branch_id: string;
      role_id: string | null;
      user_id: string | null;
      exempted_target_users: { user_id: string }[];
    },
    forDate: Date,
    applicable: boolean
  ): Promise<number> {
    try {
      console.log(`Processing target ${target.id}:`, {
        role_id: target.role_id,
        user_id: target.user_id,
        branch_id: target.branch_id,
        target_value: target.target_value
      });

      // Get exempted user IDs
      const exemptedUserIds = target.exempted_target_users.map(etu => etu.user_id);
      console.log(`Target ${target.id} exempted users:`, exemptedUserIds);

      let eligibleUserIds: string[] = [];

      if (target.role_id) {
        // Role-based target: get all users of the role in the target's branch
        const roleUsers = await this.prisma.user.findMany({
          where: {
            role_id: target.role_id,
            branch_id: target.branch_id,
            id: { notIn: exemptedUserIds }, // Exclude exempted users
          },
          select: { id: true },
        });
        eligibleUserIds = roleUsers.map(u => u.id);
        console.log(`Target ${target.id} found ${eligibleUserIds.length} eligible users:`, eligibleUserIds);
      } else if (target.user_id) {
        // Individual target: check if the user is not exempted
        if (!exemptedUserIds.includes(target.user_id)) {
          eligibleUserIds = [target.user_id];
        }
        console.log(`Target ${target.id} individual user eligible:`, eligibleUserIds);
      }

      if (eligibleUserIds.length === 0) {
        console.log(`Target ${target.id} has no eligible users`);
        return 0; // No eligible users for this target
      }

      // Check which users already have progress records for today
      const dateStart = new Date(forDate.getFullYear(), forDate.getMonth(), forDate.getDate());
      const dateEnd = new Date(forDate.getFullYear(), forDate.getMonth(), forDate.getDate() + 1);

      console.log(`Target ${target.id} checking existing progress records between:`, {
        start: dateStart.toISOString(),
        end: dateEnd.toISOString()
      });

      const existingProgressRecords = await this.prisma.targetProgress.findMany({
        where: {
          target_id: target.id,
          user_id: { in: eligibleUserIds },
          for_date: {
            gte: dateStart,
            lt: dateEnd,
          },
        },
        select: { user_id: true, for_date: true },
      });

      console.log(`Target ${target.id} found ${existingProgressRecords.length} existing progress records:`, existingProgressRecords);

      const existingUserIds = existingProgressRecords.map(pr => pr.user_id);
      const missingUserIds = eligibleUserIds.filter(userId => !existingUserIds.includes(userId));

      console.log(`Target ${target.id} missing progress for ${missingUserIds.length} users:`, missingUserIds);

      if (missingUserIds.length === 0) {
        console.log(`Target ${target.id} all users already have progress records`);
        return 0; // All users already have progress records
      }

      // Create missing progress records
      const progressRecords = missingUserIds.map(userId => ({
        target_id: target.id,
        user_id: userId,
        for_date: forDate,
        achieved_count: 0,
        target_value: target.target_value,
        is_achieved: null,
        is_applicable: applicable,
      }));

      console.log(`Target ${target.id} creating ${progressRecords.length} progress records:`, progressRecords);

      const result = await this.prisma.targetProgress.createMany({
        data: progressRecords,
        skipDuplicates: true,
      });

      console.log(`Target ${target.id} createMany result:`, result);

      this.logger.log(`Created ${progressRecords.length} progress records for target ${target.id}`);
      return progressRecords.length;

    } catch (error) {
      this.logger.error(`Error processing target ${target.id}:`, error);
      return 0;
    }
  }
}
