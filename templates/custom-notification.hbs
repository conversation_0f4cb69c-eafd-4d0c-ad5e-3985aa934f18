<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
            color: #555;
        }
        .message {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 25px;
            color: #333;
        }
        .highlight {
            background-color: #f8f9ff;
            padding: 20px;
            border-left: 4px solid #667eea;
            margin: 20px 0;
            border-radius: 4px;
        }
        .highlight h3 {
            margin: 0 0 15px 0;
            color: #667eea;
            font-size: 18px;
        }
        .data-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .data-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }
        .data-list li:last-child {
            border-bottom: none;
        }
        .data-key {
            font-weight: 600;
            color: #555;
            text-transform: capitalize;
        }
        .data-value {
            color: #333;
            text-align: right;
        }
        .cta-section {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9ff;
            border-radius: 6px;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            margin-top: 10px;
            transition: transform 0.2s ease;
        }
        .cta-button:hover {
            transform: translateY(-2px);
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        .footer p {
            margin: 5px 0;
            font-size: 12px;
            color: #666;
        }
        .signature {
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #555;
        }
        
        /* Report-specific styles */
        .report-summary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: center;
        }
        .report-summary h3 {
            margin: 0 0 10px 0;
            font-size: 20px;
        }
        .report-summary .count {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        /* Responsive design */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .header {
                padding: 20px 15px;
            }
            .content {
                padding: 20px 15px;
            }
            .header h1 {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{title}}</h1>
        </div>
        
        <div class="content">
            <div class="greeting">
                Hello {{recipientName}},
            </div>
            
            <div class="message">
                {{message}}
            </div>
            
            {{#if data}}
            {{#if data.reportType}}
            <!-- Report-specific display -->
            {{#if data.totalOverdue}}
            <div class="report-summary">
                <h3>Report Summary</h3>
                <div class="count">{{data.totalOverdue}}</div>
                <p>{{#if data.callsCount}}{{data.callsCount}} calls, {{/if}}{{#if data.visitsCount}}{{data.visitsCount}} visits{{/if}}{{#if data.overdueCount}}overdue activities{{/if}}</p>
            </div>
            {{else if data.convertedCount}}
            <div class="report-summary">
                <h3>Conversion Summary</h3>
                <div class="count">{{data.convertedCount}}</div>
                <p>Converted Leads{{#if data.newLeadsCount}} | {{data.newLeadsCount}} New Leads{{/if}}</p>
            </div>
            {{/if}}
            {{/if}}
            
            <div class="highlight">
                <h3>Details</h3>
                <ul class="data-list">
                    {{#each data}}
                    {{#unless (eq @key "phases")}}
                    <li>
                        <span class="data-key">{{@key}}:</span>
                        <span class="data-value">{{this}}</span>
                    </li>
                    {{/unless}}
                    {{/each}}
                </ul>
            </div>
            
            {{#if data.phases}}
            <div class="highlight">
                <h3>Overdue Activities ({{data.phases.length}})</h3>
                {{#each data.phases}}
                <div style="margin-bottom: 10px; padding: 10px; background: #fff; border: 1px solid #ddd; border-radius: 4px;">
                    <strong>{{this.customerName}}</strong><br>
                    <small>{{this.type}} - Due: {{this.executionDate}}</small>
                </div>
                {{/each}}
            </div>
            {{/if}}
            {{/if}}
            
            <div class="cta-section">
                <p>For more details and to take action:</p>
                <a href="#" class="cta-button">Open KB Tracker</a>
            </div>
            
            <div class="signature">
                <p>Best regards,<br>
                <strong>KB Tracker System</strong></p>
            </div>
        </div>
        
        <div class="footer">
            <p>This is an automated notification from KB Tracker.</p>
            <p>Please do not reply to this email.</p>
            <p>Generated on {{timestamp}}</p>
        </div>
    </div>
</body>
</html>
