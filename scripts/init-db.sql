-- Initialize database for KB Tracker
-- This script runs when the PostgreSQL container starts for the first time

-- Create database if it doesn't exist (handled by POSTGRES_DB env var)
-- CREATE DATABASE IF NOT EXISTS kb_tracker;

-- Create extensions that might be useful
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Set timezone
SET timezone = 'Africa/Nairobi';

-- Create a read-only user for reporting (optional)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'kb_tracker_readonly') THEN
        CREATE ROLE kb_tracker_readonly WITH LOGIN PASSWORD 'readonly_password';
    END IF;
END
$$;

-- Grant connect permission
GRANT CONNECT ON DATABASE kb_tracker TO kb_tracker_readonly;

-- Note: Table-level permissions will be granted after Prisma migrations run
-- This can be done via a post-migration script
