import { ApiProperty } from '@nestjs/swagger';

/**
 * PurposeCategoryResponseDto
 *
 * Data Transfer Object for Purpose Category responses.
 * Defines the structure of data returned by the API endpoints.
 * Includes metadata about related entities for better API usability.
 */
export class PurposeCategoryResponseDto {
  /**
   * Unique identifier for the purpose category
   */
  @ApiProperty({
    description: 'Unique identifier for the purpose category',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  /**
   * Name of the purpose category
   */
  @ApiProperty({
    description: 'Name of the purpose category',
    example: 'Sales Activities',
  })
  name: string;

  /**
   * Detailed description of the purpose category
   */
  @ApiProperty({
    description: 'Detailed description of the purpose category',
    example: 'Category for all sales-related activity purposes',
    nullable: true,
  })
  description: string | null;

  /**
   * Count of purposes in this category
   */
  @ApiProperty({
    description: 'Number of purposes associated with this category',
    example: 15,
  })
  purposes_count: number;

  /**
   * Indicates if this category is currently in use
   */
  @ApiProperty({
    description: 'Whether this category is currently being used by any purposes',
    example: true,
  })
  is_in_use: boolean;

  /**
   * ID of the user who added this category
   */
  @ApiProperty({
    description: 'ID of the user who added this category',
    example: '550e8400-e29b-41d4-a716-446655440000',
    nullable: true,
  })
  added_by?: string | null;

  /**
   * User who added this category
   */
  @ApiProperty({
    description: 'User who added this category',
    example: {
      id: '550e8400-e29b-41d4-a716-446655440000',
      name: 'John Doe',
      email: '<EMAIL>',
      rm_code: 'RM001',
    },
    nullable: true,
  })
  added_by_user?: {
    id: string;
    name: string;
    email: string;
    rm_code: string;
  } | null;

  /**
   * Timestamp when the category was created
   */
  @ApiProperty({
    description: 'Timestamp when the category was created',
    example: '2025-08-01T10:30:00.000Z',
  })
  created_at: string;
}

/**
 * PurposeCategoryListResponseDto
 * 
 * Response DTO for paginated list of purpose categories with metadata
 */
export class PurposeCategoryListResponseDto {
  /**
   * Array of purpose category objects
   */
  @ApiProperty({
    description: 'Array of purpose category objects',
    type: [PurposeCategoryResponseDto],
  })
  data: PurposeCategoryResponseDto[];

  /**
   * Total number of categories in the database
   */
  @ApiProperty({
    description: 'Total number of categories in the database',
    example: 10,
  })
  total: number;

  /**
   * Number of categories returned in this response
   */
  @ApiProperty({
    description: 'Number of categories returned in this response',
    example: 5,
  })
  count: number;

  /**
   * Success message
   */
  @ApiProperty({
    description: 'Success message',
    example: 'Retrieved 5 purpose categories successfully',
  })
  message: string;
}
