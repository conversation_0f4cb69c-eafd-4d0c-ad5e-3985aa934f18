import { PrismaClient } from '@prisma/client';
import { seedAnchorRelationships } from './seeders/anchor-relationships.seeder';
import { seedMfaMethods } from './seeders/mfa-methods.seeder';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function seedScheduledTasks() {
  console.log('⏰ Checking for scheduled tasks...');

  // 1. Check if the daily target progress generation task already exists
  const existingTargetTask = await prisma.scheduledTask.findFirst({
    where: {
      type: 'daily-target-progress-generation',
      status: { not: 'CANCELLED' },
    },
  });

  if (!existingTargetTask) {
    console.log('🆕 Creating daily target progress generation task...');

    const targetTask = await prisma.scheduledTask.create({
      data: {
        type: 'daily-target-progress-generation',
        name: 'Daily Target Progress Generation',
        description:
          'Generates missing target progress records for all active targets at 1 AM daily',
        payload: {},
        run_at: new Date(),
        interval_type: 'DAYS',
        interval_value: 1,
        priority: 8,
        max_attempts: 3,
        queue_name: 'default',
        status: 'PENDING',
      },
    });

    console.log(
      `✅ Daily target progress generation task created (ID: ${targetTask.id})`,
    );
  } else {
    console.log(
      `✅ Daily target progress generation task already exists (ID: ${existingTargetTask.id})`,
    );
  }

  // 2. Check if the overdue follow-ups notification task already exists
  const existingOverdueNotificationTask = await prisma.scheduledTask.findFirst({
    where: {
      type: 'overdue-followups-notification',
      status: { not: 'CANCELLED' },
    },
  });

  if (!existingOverdueNotificationTask) {
    console.log('🆕 Creating overdue follow-ups notification task...');

    const overdueNotificationTask = await prisma.scheduledTask.create({
      data: {
        type: 'overdue-followups-notification',
        name: 'Daily Overdue Follow-ups Notifications',
        description:
          'Sends notifications to users about their overdue follow-ups daily at 4 PM',
        payload: {},
        run_at: new Date(),
        interval_type: 'DAYS',
        interval_value: 1,
        priority: 7,
        max_attempts: 3,
        queue_name: 'default',
        status: 'PENDING',
      },
    });

    console.log(
      `✅ Overdue follow-ups notification task created (ID: ${overdueNotificationTask.id})`,
    );
  } else {
    console.log(
      `✅ Overdue follow-ups notification task already exists (ID: ${existingOverdueNotificationTask.id})`,
    );
  }

  // 3. Check if the overdue reports task already exists
  const existingOverdueReportsTask = await prisma.scheduledTask.findFirst({
    where: {
      type: 'overdue-followups-reports',
      status: { not: 'CANCELLED' },
    },
  });

  if (!existingOverdueReportsTask) {
    console.log('🆕 Creating overdue reports task...');

    const overdueReportsTask = await prisma.scheduledTask.create({
      data: {
        type: 'overdue-followups-reports',
        name: 'Daily Overdue Follow-ups Reports',
        description:
          'Generates and sends PDF reports of overdue follow-ups to users based on their permissions',
        payload: {},
        run_at: new Date(),
        interval_type: 'DAYS',
        interval_value: 1,
        priority: 6,
        max_attempts: 3,
        queue_name: 'default',
        status: 'PENDING',
      },
    });

    console.log(
      `✅ Overdue reports task created (ID: ${overdueReportsTask.id})`,
    );
  } else {
    console.log(
      `✅ Overdue reports task already exists (ID: ${existingOverdueReportsTask.id})`,
    );
  }

  // 4. Check if the overdue targets reports task already exists
  const existingOverdueTargetsReportsTask =
    await prisma.scheduledTask.findFirst({
      where: {
        type: 'overdue-targets-reports',
        status: { not: 'CANCELLED' },
      },
    });

  if (!existingOverdueTargetsReportsTask) {
    console.log('🆕 Creating overdue targets reports task...');

    const overdueTargetsReportsTask = await prisma.scheduledTask.create({
      data: {
        type: 'overdue-targets-reports',
        name: 'Daily Overdue Targets Reports',
        description:
          'Generates and sends PDF reports of overdue targets to users based on their permissions at 5 PM',
        payload: {},
        run_at: new Date(),
        interval_type: 'DAYS',
        interval_value: 1,
        priority: 5,
        max_attempts: 3,
        queue_name: 'default',
        status: 'PENDING',
      },
    });

    console.log(
      `✅ Overdue targets reports task created (ID: ${overdueTargetsReportsTask.id})`,
    );
  } else {
    console.log(
      `✅ Overdue targets reports task already exists (ID: ${existingOverdueTargetsReportsTask.id})`,
    );
  }

  // 5. Check if the weekly converted leads reports task already exists
  const existingWeeklyConvertedLeadsTask = await prisma.scheduledTask.findFirst(
    {
      where: {
        type: 'weekly-converted-leads-reports',
        status: { not: 'CANCELLED' },
      },
    },
  );

  if (!existingWeeklyConvertedLeadsTask) {
    console.log('🆕 Creating weekly converted leads reports task...');

    const weeklyConvertedLeadsTask = await prisma.scheduledTask.create({
      data: {
        type: 'weekly-converted-leads-reports',
        name: 'Weekly Converted Leads Reports',
        description:
          'Generates and sends PDF reports of weekly converted leads and new leads to users based on their permissions',
        payload: {},
        run_at: new Date(),
        interval_type: 'DAYS',
        interval_value: 7, // Weekly
        priority: 5,
        max_attempts: 3,
        queue_name: 'default',
        status: 'PENDING',
      },
    });

    console.log(
      `✅ Weekly converted leads reports task created (ID: ${weeklyConvertedLeadsTask.id})`,
    );
  } else {
    console.log(
      `✅ Weekly converted leads reports task already exists (ID: ${existingWeeklyConvertedLeadsTask.id})`,
    );
  }

  // 6. Check if the monthly converted leads reports task already exists
  const existingMonthlyConvertedLeadsTask =
    await prisma.scheduledTask.findFirst({
      where: {
        type: 'monthly-converted-leads-reports',
        status: { not: 'CANCELLED' },
      },
    });

  if (!existingMonthlyConvertedLeadsTask) {
    console.log('🆕 Creating monthly converted leads reports task...');

    const monthlyConvertedLeadsTask = await prisma.scheduledTask.create({
      data: {
        type: 'monthly-converted-leads-reports',
        name: 'Monthly Converted Leads Reports',
        description:
          'Generates and sends PDF reports of monthly converted leads and new leads to users based on their permissions',
        payload: {},
        run_at: new Date(),
        interval_type: 'DAYS',
        interval_value: 30, // Monthly (approximately)
        priority: 5,
        max_attempts: 3,
        queue_name: 'default',
        status: 'PENDING',
      },
    });

    console.log(
      `✅ Monthly converted leads reports task created (ID: ${monthlyConvertedLeadsTask.id})`,
    );
  } else {
    console.log(
      `✅ Monthly converted leads reports task already exists (ID: ${existingMonthlyConvertedLeadsTask.id})`,
    );
  }

  // 7. Check if the overdue 2by2by2 reports task already exists
  const existingOverdue2by2by2ReportsTask =
    await prisma.scheduledTask.findFirst({
      where: {
        type: 'overdue-2by2by2-reports',
        status: { not: 'CANCELLED' },
      },
    });

  if (!existingOverdue2by2by2ReportsTask) {
    console.log('🆕 Creating overdue 2by2by2 reports task...');

    const overdue2by2by2ReportsTask = await prisma.scheduledTask.create({
      data: {
        type: 'overdue-2by2by2-reports',
        name: 'Overdue 2by2by2 Reports',
        description:
          'Generates and sends PDF reports of overdue 2by2by2 activities to users based on their permissions',
        payload: {},
        run_at: new Date(),
        interval_type: 'DAYS',
        interval_value: 1, // Daily
        priority: 5,
        max_attempts: 3,
        queue_name: 'default',
        status: 'PENDING',
      },
    });

    console.log(
      `✅ Overdue 2by2by2 reports task created (ID: ${overdue2by2by2ReportsTask.id})`,
    );
  } else {
    console.log(
      `✅ Overdue 2by2by2 reports task already exists (ID: ${existingOverdue2by2by2ReportsTask.id})`,
    );
  }

  // 8. Check if the overdue 2by2by2 notifications task already exists
  const existingOverdue2by2by2NotificationsTask =
    await prisma.scheduledTask.findFirst({
      where: {
        type: 'overdue-2by2by2-notifications',
        status: { not: 'CANCELLED' },
      },
    });

  if (!existingOverdue2by2by2NotificationsTask) {
    console.log('🆕 Creating overdue 2by2by2 notifications task...');

    const overdue2by2by2NotificationsTask = await prisma.scheduledTask.create({
      data: {
        type: 'overdue-2by2by2-notifications',
        name: 'Overdue 2by2by2 Notifications',
        description:
          'Sends individual notifications to users with overdue 2by2by2 activities',
        payload: {},
        run_at: new Date(),
        interval_type: 'DAYS',
        interval_value: 1, // Daily
        priority: 5,
        max_attempts: 3,
        queue_name: 'default',
        status: 'PENDING',
      },
    });

    console.log(
      `✅ Overdue 2by2by2 notifications task created (ID: ${overdue2by2by2NotificationsTask.id})`,
    );
  } else {
    console.log(
      `✅ Overdue 2by2by2 notifications task already exists (ID: ${existingOverdue2by2by2NotificationsTask.id})`,
    );
  }

  console.log(
    '📅 All scheduled tasks are configured with appropriate intervals',
  );
}

async function main() {
  console.log('🌱 Starting database seeding...');

  // First, let's create permissions if they don't exist
  const existingPermissions = await prisma.permission.findMany();

  if (existingPermissions.length < 100) {
    console.log('🔐 Creating permissions...');

    const permissionsData = [
      {
        id: 'users.create',
        name: 'Create Users',
        description: 'User Management',
      },
      {
        id: 'users.import',
        name: 'Import Users',
        description: 'User Management',
      },
      {
        id: 'users.export.print',
        name: 'Export and Print Users',
        description: 'User Management',
      },
      {
        id: 'users.view',
        name: 'View Users',
        description: 'User Management',
      },
      {
        id: 'view.all.users',
        name: 'View All Users',
        description: 'User Management',
      },
      {
        id: 'view.region.users',
        name: 'View Region Users',
        description: 'User Management',
      },
      {
        id: 'view.branch.users',
        name: 'View Branch Users',
        description: 'User Management',
      },
      {
        id: 'users.update',
        name: 'Update Users',
        description: 'User Management',
      },
      {
        id: 'users.delete',
        name: 'Delete Users',
        description: 'User Management',
      },
      {
        id: 'roles.create',
        name: 'Create Roles',
        description: 'Role Management',
      },

      {
        id: 'roles.export.print',
        name: 'Export and Print Roles',
        description: 'Role Management',
      },
      {
        id: 'roles.view',
        name: 'View Roles',
        description: 'Role Management',
      },
      {
        id: 'roles.update',
        name: 'Update Roles',
        description: 'Role Management',
      },

      {
        id: 'targets.create',
        name: 'Create Targets',
        description: 'Target Management',
      },
      {
        id: 'targets.export.print',
        name: 'Export and Print Targets',
        description: 'Target Management',
      },
      {
        id: 'targets.view',
        name: 'View All Targets',
        description: 'Target Management',
      },
      {
        id: 'targets.view.all.branches',
        name: 'View All Branch Targets',
        description: 'Target Management',
      },
      {
        id: 'targets.view.my.region',
        name: 'View My Region Targets',
        description: 'Target Management',
      },
      {
        id: 'targets.view.my.branch',
        name: 'View My Branch Targets',
        description: 'Target Management',
      },
      {
        id: 'targets.update',
        name: 'Update Targets',
        description: 'Target Management',
      },

      {
        id: 'targets.delete',
        name: 'Delete Targets',
        description: 'Target Management',
      },
      {
        id: 'customer.service.hitlists.view',
        name: 'View Hitlists',
        description: 'Customer Service',
      },
      {
        id: 'customer.service.import',
        name: 'Import Hitlists',
        description: 'Customer Service',
      },
      {
        id: 'customer.service.export.print',
        name: 'Export & Print Hitlists',
        description: 'Customer Service',
      },
      {
        id: 'customer.service.personal.calls.view',
        name: 'View Personal Calls',
        description: 'Customer Service',
      },
      {
        id: 'view.all.leads',
        name: 'View All Leads',
        description: 'leads',
      },
      {
        id: 'view.branch.leads',
        name: 'View Branch Leads',
        description: 'leads',
      },
      {
        id: 'view.region.leads',
        name: 'View Region Leads',
        description: 'leads',
      },
      {
        id: 'view.my.leads',
        name: 'View My Leads',
        description: 'leads',
      },
      {
        id: 'leads.edit',
        name: 'Edit Leads',
        description: 'leads',
      },
      {
        id: 'leads.delete',
        name: 'Delete Leads',
        description: 'leads',
      },
      {
        id: 'leads.create',
        name: 'Add  Leads',
        description: 'leads',
      },
      {
        id: 'leads.call',
        name: 'Call a Lead',
        description: 'leads',
      },
      {
        id: 'leads.visit',
        name: 'Visit Leads',
        description: 'leads',
      },
      {
        id: 'leads.change.status',
        name: 'Change Lead Status',
        description: 'leads',
      },
      {
        id: 'leads.reassign',
        name: 'Reassign Lead',
        description: 'leads',
      },
      {
        id: 'leads.convert.to.client',
        name: 'Convert Leads to Client',
        description: 'leads',
      },
      {
        id: 'leads.view.calls',
        name: 'View Calls on Leads',
        description: 'leads',
      },
      {
        id: 'view.all.calls',
        name: 'View All Calls on Leads',
        description: 'leads',
      },
      {
        id: 'view.region.calls',
        name: 'View Region Calls on Leads',
        description: 'leads',
      },
      {
        id: 'view.branch.calls',
        name: 'View Branch Calls on Leads',
        description: 'leads',
      },
      {
        id: 'view.my.calls',
        name: 'View My Calls on Leads',
        description: 'leads',
      },
      {
        id: 'leads.view.visits',
        name: 'View Visits on Leads',
        description: 'leads',
      },
      {
        id: 'view.all.visits',
        name: 'View All Visits on Leads',
        description: 'leads',
      },
      {
        id: 'view.region.visits',
        name: 'View Region Visits on Leads',
        description: 'leads',
      },
      {
        id: 'view.branch.visits',
        name: 'View Branch Visits on Leads',
        description: 'leads',
      },
      {
        id: 'view.my.visits',
        name: 'View My Visits on Leads',
        description: 'leads',
      },
      {
        id: 'leads.view.followups',
        name: 'View Followups on Leads',
        description: 'leads',
      },
      {
        id: 'followups.all.branches',
        name: 'All Branches Follow Ups',
        description: 'leads',
      },
      {
        id: 'followups.my.branch',
        name: 'My Branch Follow Ups',
        description: 'leads',
      },
      {
        id: 'followups.my.region',
        name: 'My Region Follow Ups',
        description: 'leads',
      },
      {
        id: 'anchors.view',
        name: 'View Anchors',
        description: 'anchors',
      },
      {
        id: 'anchors.edit',
        name: 'Edit Anchors',
        description: 'anchors',
      },
      {
        id: 'anchors.delete',
        name: 'Delete Anchors',
        description: 'anchors',
      },
      {
        id: 'anchors.create',
        name: 'Add Anchors',
        description: 'anchors',
      },
      {
        id: 'view.all.customers',
        name: 'View All Customers',
        description: 'customers',
      },
      {
        id: 'view.branch.customers',
        name: 'View Branch Customers',
        description: 'customers',
      },
      {
        id: 'view.region.customers',
        name: 'View Region Customers',
        description: 'customers',
      },
      {
        id: 'view.my.customers',
        name: 'View My Customers',
        description: 'customers',
      },
      {
        id: 'customers.edit',
        name: 'Edit Customers',
        description: 'customers',
      },
      {
        id: 'customers.delete',
        name: 'Delete Customers',
        description: 'customers',
      },
      {
        id: 'customers.create',
        name: 'Add Customers',
        description: 'customers',
      },

      {
        id: 'regions.view',
        name: 'View Regions',
        description: 'items',
      },
      {
        id: 'regions.edit',
        name: 'Edit Regions ',
        description: 'items',
      },
      {
        id: 'regions.delete',
        name: 'Delete Regions ',
        description: 'items',
      },
      {
        id: 'regions.create',
        name: 'Add Regions ',
        description: 'items',
      },
      {
        id: 'branches.view',
        name: 'View   Branches',
        description: 'items',
      },
      {
        id: 'branches.edit',
        name: 'Edit Branches ',
        description: 'items',
      },
      {
        id: 'branches.delete',
        name: 'Delete Branches ',
        description: 'items',
      },
      {
        id: 'branches.create',
        name: 'Add Branches ',
        description: 'items',
      },
      {
        id: 'purpose.categories.view',
        name: 'View   Purpose Categories',
        description: 'items',
      },
      {
        id: 'purpose.categories.edit',
        name: 'Edit Purpose Categories ',
        description: 'items',
      },
      {
        id: 'purpose.categories.delete',
        name: 'Delete Purpose Categories ',
        description: 'items',
      },
      {
        id: 'purpose.categories.create',
        name: 'Add Purpose Categories ',
        description: 'items',
      },
      {
        id: 'purposes.view',
        name: 'View   Purposes',
        description: 'items',
      },
      {
        id: 'purposes.edit',
        name: 'Edit Purposes ',
        description: 'items',
      },
      {
        id: 'purposes.delete',
        name: 'Delete Purposes  ',
        description: 'items',
      },
      {
        id: 'purposes.create',
        name: 'Add Purposes  ',
        description: 'items',
      },
      {
        id: 'customer.categories.view',
        name: 'View   Customer Categories',
        description: 'items',
      },
      {
        id: 'customer.categories.edit',
        name: 'Edit Customer Categories ',
        description: 'items',
      },
      {
        id: 'customer.categories.delete',
        name: 'Delete Customer Categories ',
        description: 'items',
      },
      {
        id: 'customer.categories.create',
        name: 'Add Customer Categories ',
        description: 'items',
      },
      {
        id: 'customer.feedback.category.view',
        name: 'View   Customer Feedback Categories',
        description: 'items',
      },
      {
        id: 'customer.feedback.category.edit',
        name: 'Edit Customer Feedback Category ',
        description: 'items',
      },
      {
        id: 'customer.feedback.category.delete',
        name: 'Delete Customer Feedback Category ',
        description: 'items',
      },
      {
        id: 'customer.feedback.category.create',
        name: 'Add Customer Feedback Category ',
        description: 'items',
      },
      {
        id: 'isic.sector.view',
        name: 'View   ISIC Sectors',
        description: 'items',
      },
      {
        id: 'isic.sector.edit',
        name: 'Edit ISIC Sectors ',
        description: 'items',
      },
      {
        id: 'isic.sector.delete',
        name: 'Delete ISIC Sectors  ',
        description: 'items',
      },
      {
        id: 'isic.sector.create',
        name: 'Add ISIC Sectors ',
        description: 'items',
      },
      {
        id: 'dashboard.banker',
        name: ' Banker Dashboard',
        description: 'dashboard',
      },
      {
        id: 'dashboard.finance.analyst',
        name: 'Finance Analyst Dashboard',
        description: 'dashboard',
      },
      {
        id: 'dashboard.segment.head',
        name: 'Segment Head Dashboard',
        description: 'dashboard',
      },
      {
        id: 'dashboard.branch.manager',
        name: 'Branch Manager Dashboard',
        description: 'dashboard',
      },
      {
        id: 'dashboard.service.delivery.supervisor',
        name: 'Service Delivery Supervisor Dashboard',
        description: 'dashboard',
      },
      {
        id: 'dashboard.cxo',
        name: 'Customer Experience Officer Dashboard',
        description: 'dashboard',
      },
      {
        id: 'dashboard.regional.manager',
        name: 'Regional Manager Dashboard',
        description: 'dashboard',
      },
      {
        id: 'notifications.view',
        name: 'View Notifications',
        description: 'Notification Management',
      },
      {
        id: 'notifications.manage',
        name: 'Manage Notifications',
        description: 'Notification Management',
      },
      {
        id: 'notifications.send',
        name: 'Send Notifications',
        description: 'Notification Management',
      },
      {
        id: 'reports.overdue.followups.all',
        name: 'All Overdue Follow-ups Reports',
        description: 'Reports',
      },
      {
        id: 'reports.overdue.followups.branch',
        name: 'Branch Overdue Follow-ups Reports',
        description: 'Reports',
      },
      {
        id: 'reports.overdue.followups.region',
        name: 'Region Overdue Follow-ups Reports',
        description: 'Reports',
      },
      {
        id: 'reports.overdue.targets.cs.all',
        name: 'All CS Overdue Targets Reports',
        description: 'Reports',
      },
      {
        id: 'reports.overdue.targets.leads.all',
        name: 'All Leads Overdue Targets Reports',
        description: 'Reports',
      },
      {
        id: 'reports.overdue.targets.cs.branch',
        name: 'Branch CS Overdue Targets Reports',
        description: 'Reports',
      },
      {
        id: 'reports.overdue.targets.leads.branch',
        name: 'Branch Leads Overdue Targets Reports',
        description: 'Reports',
      },
      {
        id: 'reports.overdue.targets.cs.region',
        name: 'Region CS Overdue Targets Reports',
        description: 'Reports',
      },
      {
        id: 'reports.overdue.targets.leads.region',
        name: 'Region Leads Overdue Targets Reports',
        description: 'Reports',
      },
      // Weekly Converted Leads Reports
      {
        id: 'reports.converted.leads.all.weekly',
        name: 'All Branches Weekly Converted Leads Reports',
        description: 'Reports',
      },
      {
        id: 'reports.converted.leads.region.weekly',
        name: 'Region Weekly Converted Leads Reports',
        description: 'Reports',
      },
      {
        id: 'reports.converted.leads.branch.weekly',
        name: 'Branch Weekly Converted Leads Reports',
        description: 'Reports',
      },
      // Monthly Converted Leads Reports
      {
        id: 'reports.converted.leads.all.monthly',
        name: 'All Branches Monthly Converted Leads Reports',
        description: 'Reports',
      },
      {
        id: 'reports.converted.leads.region.monthly',
        name: 'Region Monthly Converted Leads Reports',
        description: 'Reports',
      },
      {
        id: 'reports.converted.leads.branch.monthly',
        name: 'Branch Monthly Converted Leads Reports',
        description: 'Reports',
      },
      // Overdue 2by2by2 Reports
      {
        id: 'reports.overdue.2by2by2.all',
        name: 'All Branches Overdue 2by2by2 Reports',
        description: 'Reports',
      },
      {
        id: 'reports.overdue.2by2by2.region',
        name: 'Region Overdue 2by2by2 Reports',
        description: 'Reports',
      },
      {
        id: 'reports.overdue.2by2by2.branch',
        name: 'Branch Overdue 2by2by2 Reports',
        description: 'Reports',
      },
    ];

    // Create permissions in bulk
    await prisma.permission.createMany({
      data: permissionsData,
      skipDuplicates: true,
    });

    console.log(
      `✅ Created ${permissionsData.length} permissions: ${permissionsData.map((p) => p.name).join(', ')}`,
    );
  } else {
    console.log(
      `🔐 Permissions already exist (${existingPermissions.length} found)`,
    );
  }

  // Now, let's create some regions if they don't exist
  const existingRegions = await prisma.region.findMany();

  let centralRegion,
    northEasternRegion,
    nyanzaRegion,
    westernRegion,
    easternRegion,
    nairobiRegion,
    coastalRegion,
    riftValleyRegion;

  if (existingRegions.length < 8) {
    console.log('📍 Creating regions...');

    centralRegion = await prisma.region.create({
      data: {
        name: 'Central Region',
      },
    });

    northEasternRegion = await prisma.region.create({
      data: {
        name: 'North Eastern Region',
      },
    });

    nyanzaRegion = await prisma.region.create({
      data: {
        name: 'Nyanza Region',
      },
    });
    westernRegion = await prisma.region.create({
      data: {
        name: 'Western Region',
      },
    });

    easternRegion = await prisma.region.create({
      data: {
        name: 'Eastern Region',
      },
    });

    nairobiRegion = await prisma.region.create({
      data: {
        name: 'Nairobi Region',
      },
    });

    coastalRegion = await prisma.region.create({
      data: {
        name: 'Coastal Region',
      },
    });

    riftValleyRegion = await prisma.region.create({
      data: {
        name: 'Rift Valley Region',
      },
    });

    console.log(
      `✅ Created regions: ${centralRegion.name}, ${northEasternRegion.name}, ${nyanzaRegion.name}, ${westernRegion.name}, ${easternRegion.name}, ${nairobiRegion.name}, ${coastalRegion.name}, ${riftValleyRegion.name}`,
    );
  } else {
    // Use existing regions
    centralRegion = existingRegions[0];
    northEasternRegion = existingRegions[1];
    nyanzaRegion = existingRegions[2];
    westernRegion = existingRegions[3];
    easternRegion = existingRegions[4];
    nairobiRegion = existingRegions[5];
    coastalRegion = existingRegions[6];
    riftValleyRegion = existingRegions[7];
    console.log(
      `📍 Using existing regions: ${centralRegion.name}, ${northEasternRegion.name}, ${nyanzaRegion.name}, ${westernRegion.name}, ${easternRegion.name}, ${nairobiRegion.name}, ${coastalRegion.name}, ${riftValleyRegion.name}`,
    );
  }

  // Check if branches already exist
  const existingBranches = await prisma.branch.findMany();

  if (existingBranches.length < 23) {
    console.log('🏢 Creating test branches...');

    const branch1 = await prisma.branch.create({
      data: {
        name: 'Thika Branch',
        region_id: centralRegion.id,
      },
    });

    const branch2 = await prisma.branch.create({
      data: {
        name: 'Nakuru Branch',
        region_id: riftValleyRegion.id,
      },
    });

    const branch3 = await prisma.branch.create({
      data: {
        name: 'Meru Branch',
        region_id: centralRegion.id,
      },
    });

    const branch4 = await prisma.branch.create({
      data: {
        name: 'Kisumu Branch',
        region_id: nyanzaRegion.id,
      },
    });

    const branch5 = await prisma.branch.create({
      data: {
        name: 'Kitale Branch',
        region_id: riftValleyRegion.id,
      },
    });

    const branch6 = await prisma.branch.create({
      data: {
        name: 'Kayole Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch7 = await prisma.branch.create({
      data: {
        name: 'Gikomba Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch8 = await prisma.branch.create({
      data: {
        name: 'Wangige Branch',
        region_id: centralRegion.id,
      },
    });

    const branch9 = await prisma.branch.create({
      data: {
        name: 'Ongata Rongai Branch',
        region_id: riftValleyRegion.id,
      },
    });

    const branch10 = await prisma.branch.create({
      data: {
        name: 'Mtwapa Branch',
        region_id: coastalRegion.id,
      },
    });

    const branch11 = await prisma.branch.create({
      data: {
        name: 'Machakos Branch',
        region_id: easternRegion.id,
      },
    });

    const branch12 = await prisma.branch.create({
      data: {
        name: 'Kisii Branch',
        region_id: nyanzaRegion.id,
      },
    });

    const branch13 = await prisma.branch.create({
      data: {
        name: ' Kikuyu Branch',
        region_id: centralRegion.id,
      },
    });

    const branch14 = await prisma.branch.create({
      data: {
        name: 'Kawangware Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch15 = await prisma.branch.create({
      data: {
        name: 'Eldoret Branch',
        region_id: riftValleyRegion.id,
      },
    });

    const branch16 = await prisma.branch.create({
      data: {
        name: 'Utawala Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch17 = await prisma.branch.create({
      data: {
        name: ' Nyeri Branch',
        region_id: centralRegion.id,
      },
    });

    const branch18 = await prisma.branch.create({
      data: {
        name: ' Mombasa Branch',
        region_id: coastalRegion.id,
      },
    });

    const branch19 = await prisma.branch.create({
      data: {
        name: ' Kirinyaga Road Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch20 = await prisma.branch.create({
      data: {
        name: ' Kitengela Branch',
        region_id: easternRegion.id,
      },
    });

    const branch21 = await prisma.branch.create({
      data: {
        name: ' Kiambu Branch',
        region_id: centralRegion.id,
      },
    });

    const branch22 = await prisma.branch.create({
      data: {
        name: ' Koinange Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch23 = await prisma.branch.create({
      data: {
        name: ' Main Office',
        region_id: nairobiRegion.id,
      },
    });
  } else {
    console.log(`🏢 Branches already exist (${existingBranches.length} found)`);
  }

  // Check if customer categories already exist
  const existingCustomerCategories = await prisma.customerCategory.findMany();

  if (existingCustomerCategories.length === 0) {
    console.log('🏷️ Creating test customer categories...');

    // Get an existing user to use as the creator
    const existingUser = await prisma.user.findFirst();
    if (!existingUser) {
      console.log('⚠️ No users found, skipping customer category creation');
    } else {
      const category1 = await prisma.customerCategory.create({
        data: {
          name: 'Corporate',
          added_by: existingUser.id,
        },
      });

      const category2 = await prisma.customerCategory.create({
        data: {
          name: 'Small Business',
          added_by: existingUser.id,
        },
      });

      const category3 = await prisma.customerCategory.create({
        data: {
          name: 'Individual',
          added_by: existingUser.id,
        },
      });

      console.log(
        `✅ Created customer categories: ${category1.name}, ${category2.name}, ${category3.name}`,
      );
    }
  } else {
    console.log(
      `🏷️ Customer categories already exist (${existingCustomerCategories.length} found)`,
    );
  }

  // Check if employers already exist
  const existingEmployers = await prisma.employer.findMany();

  if (existingEmployers.length === 0) {
    console.log('🏭 Creating test employers...');

    const employer1 = await prisma.employer.create({
      data: {
        name: 'Tech Solutions Ltd',
      },
    });

    const employer2 = await prisma.employer.create({
      data: {
        name: 'Manufacturing Corp',
      },
    });

    const employer3 = await prisma.employer.create({
      data: {
        name: 'Healthcare Services Inc',
      },
    });

    console.log(
      `✅ Created employers: ${employer1.name}, ${employer2.name}, ${employer3.name}`,
    );
  } else {
    console.log(
      `🏭 Employers already exist (${existingEmployers.length} found)`,
    );
  }

  // Check if users already exist
  const existingUsers = await prisma.user.findMany();

  // Get existing roles (needed for both branches)
  const existingRoles = await prisma.role.findMany();

  if (existingUsers.length === 0) {
    console.log('👥 Creating test users...');
    let rmRole;

    if (existingRoles.length === 0) {
      rmRole = await prisma.role.create({
        data: {
          name: 'Relationship Manager',
          description: 'Manages customer relationships and leads',
        },
      });
      console.log(`✅ Created role: ${rmRole.name}`);
    } else {
      rmRole = existingRoles[0];
    }

    // Get the branches to assign users to
    const branches = await prisma.branch.findMany();
    const branch1 = branches[0];
    const branch2 = branches.length > 1 ? branches[1] : branches[0];
    const branch3 = branches.length > 2 ? branches[2] : branches[0];

    // Hash the default password
    const hashedPassword = await bcrypt.hash('defaultPassword123', 12);

    const user1 = await prisma.user.create({
      data: {
        name: 'John Smith',
        email: '<EMAIL>',
        password: hashedPassword,
        phone_number: '+254712345678',
        rm_code: 'RM001',
        role_id: rmRole.id,
        branch_id: branch1.id,
      },
    });

    const user2 = await prisma.user.create({
      data: {
        name: 'Jane Doe',
        email: '<EMAIL>',
        password: hashedPassword,
        phone_number: '+254787654321',
        rm_code: 'RM002',
        role_id: rmRole.id,
        branch_id: branch2.id,
      },
    });

    const user3 = await prisma.user.create({
      data: {
        name: 'Samuel Maiko',
        email: '<EMAIL>',
        password: hashedPassword,
        phone_number: '+254798765432',
        rm_code: 'RM8660',
        role_id: rmRole.id,
        branch_id: branch3.id,
      },
    });

    console.log(
      `✅ Created users: ${user1.name}, ${user2.name}, ${user3.name}`,
    );

    // Assign lead permissions to the Relationship Manager role
    const leadPermissions = ['view.all.leads', 'view.my.leads'];

    for (const permissionId of leadPermissions) {
      await prisma.rolePermission.create({
        data: {
          role_id: rmRole.id,
          permission_id: permissionId,
        },
      });
    }

    console.log(`✅ Assigned lead permissions to ${rmRole.name} role`);

    // Assign overdue targets report permissions to the Relationship Manager role
    const overdueTargetsReportPermissions = [
      'reports.overdue.targets.cs.all',
      'reports.overdue.targets.leads.all',
      'reports.overdue.targets.cs.branch',
      'reports.overdue.targets.leads.branch',
      'reports.overdue.targets.cs.region',
      'reports.overdue.targets.leads.region',
    ];

    for (const permissionId of overdueTargetsReportPermissions) {
      await prisma.rolePermission.create({
        data: {
          role_id: rmRole.id,
          permission_id: permissionId,
        },
      });
    }

    console.log(
      `✅ Assigned overdue targets report permissions to ${rmRole.name} role`,
    );
  } else {
    console.log(`👥 Users already exist (${existingUsers.length} found)`);

    // Even if users exist, ensure the Relationship Manager role has the required permissions
    const rmRole =
      existingRoles.length > 0
        ? existingRoles[0]
        : await prisma.role.findFirst();

    if (rmRole) {
      // Check if overdue targets report permissions are already assigned
      const existingPermissions = await prisma.rolePermission.findMany({
        where: {
          role_id: rmRole.id,
          permission_id: {
            in: [
              'reports.overdue.targets.cs.all',
              'reports.overdue.targets.leads.all',
              'reports.overdue.targets.cs.branch',
              'reports.overdue.targets.leads.branch',
              'reports.overdue.targets.cs.region',
              'reports.overdue.targets.leads.region',
            ],
          },
        },
      });

      if (existingPermissions.length === 0) {
        console.log(
          `🔐 Assigning missing overdue targets report permissions to ${rmRole.name} role...`,
        );

        const overdueTargetsReportPermissions = [
          'reports.overdue.targets.cs.all',
          'reports.overdue.targets.leads.all',
          'reports.overdue.targets.cs.branch',
          'reports.overdue.targets.leads.branch',
          'reports.overdue.targets.cs.region',
          'reports.overdue.targets.leads.region',
        ];

        for (const permissionId of overdueTargetsReportPermissions) {
          await prisma.rolePermission.create({
            data: {
              role_id: rmRole.id,
              permission_id: permissionId,
            },
          });
        }

        console.log(
          `✅ Assigned missing overdue targets report permissions to ${rmRole.name} role`,
        );
      } else {
        console.log(
          `🔐 Overdue targets report permissions already assigned to ${rmRole.name} role`,
        );
      }
    }
  }

  // Check if anchors already exist
  const existingAnchors = await prisma.anchor.findMany();

  if (existingAnchors.length === 0) {
    console.log('⚓ Creating test anchors...');

    const anchor1 = await prisma.anchor.create({
      data: {
        name: 'ABC Corporation',
        email: '<EMAIL>',
        phone_number: '0712345678',
      },
    });

    const anchor2 = await prisma.anchor.create({
      data: {
        name: 'XYZ Holdings Ltd',
        email: '<EMAIL>',
        phone_number: '0723456789',
      },
    });

    const anchor3 = await prisma.anchor.create({
      data: {
        name: 'Kenya Business Partners',
        email: '<EMAIL>',
        phone_number: '0734567890',
      },
    });

    const anchor4 = await prisma.anchor.create({
      data: {
        name: 'East Africa Enterprises',
        email: '<EMAIL>',
        phone_number: '0745678901',
      },
    });

    const anchor5 = await prisma.anchor.create({
      data: {
        name: 'Nairobi Investment Group',
        email: '<EMAIL>',
        phone_number: '0756789012',
      },
    });

    const anchor6 = await prisma.anchor.create({
      data: {
        name: 'Mombasa Trade Center',
        email: '<EMAIL>',
        phone_number: '0767890123',
      },
    });

    const anchor7 = await prisma.anchor.create({
      data: {
        name: 'Kisumu Development Corp',
        email: '<EMAIL>',
        phone_number: '0778901234',
      },
    });

    const anchor8 = await prisma.anchor.create({
      data: {
        name: 'Eldoret Agricultural Hub',
        email: '<EMAIL>',
        phone_number: '0789012345',
      },
    });

    const anchor9 = await prisma.anchor.create({
      data: {
        name: 'Nakuru Manufacturing Alliance',
        email: '<EMAIL>',
        phone_number: '0790123456',
      },
    });

    const anchor10 = await prisma.anchor.create({
      data: {
        name: 'Thika Industrial Park',
        email: '<EMAIL>',
        phone_number: '0701234567',
      },
    });

    console.log(
      `✅ Created anchors: ${anchor1.name}, ${anchor2.name}, ${anchor3.name}, ${anchor4.name}, ${anchor5.name}, ${anchor6.name}, ${anchor7.name}, ${anchor8.name}, ${anchor9.name}, ${anchor10.name}`,
    );
  } else {
    console.log(`⚓ Anchors already exist (${existingAnchors.length} found)`);
  }

  // Seed anchor relationships
  await seedAnchorRelationships();

  // Seed MFA methods
  await seedMfaMethods();

  // Check if ISIC sectors already exist
  const existingIsicSectors = await prisma.iSICSector.findMany();

  if (existingIsicSectors.length === 0) {
    console.log('🏭 Creating test ISIC sectors...');

    const sector1 = await prisma.iSICSector.create({
      data: {
        code: 'A01',
        name: 'Agriculture and Forestry',
      },
    });

    const sector2 = await prisma.iSICSector.create({
      data: {
        code: 'C10',
        name: 'Manufacturing',
      },
    });

    const sector3 = await prisma.iSICSector.create({
      data: {
        code: 'J62',
        name: 'Information Technology',
      },
    });

    console.log(
      `✅ Created ISIC sectors: ${sector1.name}, ${sector2.name}, ${sector3.name}`,
    );
  } else {
    console.log(
      `🏭 ISIC sectors already exist (${existingIsicSectors.length} found)`,
    );
  }

  // Seed scheduled tasks
  console.log('⏰ Seeding scheduled tasks...');
  await seedScheduledTasks();

  console.log('🎉 Database seeding completed!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
