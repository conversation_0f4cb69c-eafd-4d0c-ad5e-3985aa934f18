const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function addPermissions() {
  try {
    console.log('=== CHECKING PERMISSIONS ===');
    
    // Get test user
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { 
        role: { 
          include: { 
            role_permissions: { 
              include: { permission: true } 
            } 
          } 
        } 
      }
    });
    
    if (!testUser) {
      console.log('❌ Test user not found');
      return;
    }
    
    console.log(`✅ Found test user: ${testUser.name}`);
    console.log(`   Role: ${testUser.role.name}`);
    console.log(`   Current permissions: ${testUser.role.role_permissions.length}`);
    
    // Check for required permissions
    const requiredPermissions = [
      'reports.converted.leads.all.monthly',
      'reports.converted.leads.region.monthly', 
      'reports.converted.leads.branch.monthly',
      'reports.converted.leads.all.weekly',
      'reports.converted.leads.region.weekly',
      'reports.converted.leads.branch.weekly',
      'reports.overdue.2by2by2.all',
      'reports.overdue.2by2by2.region',
      'reports.overdue.2by2by2.branch'
    ];
    
    console.log('\n=== CHECKING REQUIRED PERMISSIONS ===');
    
    for (const permId of requiredPermissions) {
      let permission = await prisma.permission.findUnique({
        where: { id: permId }
      });
      
      if (!permission) {
        // Create permission if it doesn't exist
        permission = await prisma.permission.create({
          data: {
            id: permId,
            name: permId.replace(/\./g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            description: `Permission for ${permId}`
          }
        });
        console.log(`✅ Created permission: ${permission.id}`);
      } else {
        console.log(`✅ Permission exists: ${permission.id}`);
      }
      
      // Check if user's role has this permission
      const rolePermission = await prisma.rolePermission.findUnique({
        where: {
          role_id_permission_id: {
            role_id: testUser.role.id,
            permission_id: permission.id
          }
        }
      });
      
      if (!rolePermission) {
        // Add permission to role
        await prisma.rolePermission.create({
          data: {
            role_id: testUser.role.id,
            permission_id: permission.id
          }
        });
        console.log(`✅ Added permission ${permission.id} to role ${testUser.role.name}`);
      } else {
        console.log(`⚠️  Role already has permission: ${permission.id}`);
      }
    }
    
    // Get updated user permissions
    const updatedUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { 
        role: { 
          include: { 
            role_permissions: { 
              include: { permission: true } 
            } 
          } 
        } 
      }
    });
    
    console.log(`\n✅ Updated user now has ${updatedUser.role.role_permissions.length} permissions`);
    
    // Show some of the permissions
    console.log('\nSample permissions:');
    updatedUser.role.role_permissions.slice(0, 10).forEach(rp => {
      console.log(`  - ${rp.permission.id}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addPermissions();
