import { Module, forwardRef } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QueueService } from './queue.service';
// import { QueueCleanupService } from './queue-cleanup.service';
import { EmailProcessor } from './processors/email.processor';
import { ReportProcessor } from './processors/report.processor';
import { DataProcessor } from './processors/data.processor';
import { ScheduledTaskProcessor } from './processors/scheduled-task.processor';
import { NotificationProcessor } from './processors/notification.processor';
import { ScheduledTaskModule } from '../scheduled-tasks/scheduled-task.module';

/**
 * Queue Consumer Module - Includes processors for consuming jobs
 * Used in Worker container - includes ALL processors
 */

@Module({
  imports: [
    ConfigModule,
    forwardRef(() => ScheduledTaskModule),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        connection: {
          host: configService.get('QUEUE_REDIS_HOST') || 'redis',
          port: configService.get('QUEUE_REDIS_PORT') || 6379,
          password: configService.get('QUEUE_REDIS_PASSWORD') || undefined,
          db: configService.get('QUEUE_REDIS_DB') || 1,
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue(
      { name: 'email' },
      { name: 'reports' },
      { name: 'data-processing' },
      { name: 'scheduled-tasks' },
      { name: 'notifications' },
    ),
  ],
  providers: [
    QueueService,
    /* QueueCleanupService, */ EmailProcessor,
    ReportProcessor,
    DataProcessor,
    ScheduledTaskProcessor,
    NotificationProcessor,
  ],
  exports: [QueueService, /* QueueCleanupService, */ BullModule],
})
export class QueueModule {}
