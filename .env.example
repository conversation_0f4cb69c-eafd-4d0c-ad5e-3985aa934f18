# ================================
# KB Tracker Backend Environment Variables
# Copy this file to .env and update the values
# ================================

# Application Configuration
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
APP_NAME="KB Tracker"

# Frontend Configuration
FRONTEND_URL=http://localhost:5173

# Database Configuration (PostgreSQL)
DATABASE_URL=********************************************/kb_tracker
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=kb_tracker
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Redis Configuration (for BullMQ)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://redis:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key-change-this-in-production

# Email Configuration (SMTP)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_SECURE=false
MAIL_USER=<EMAIL>
MAIL_PASS=your_app_password_here
MAIL_FROM="KB Tracker Support" <<EMAIL>>

# Note: For Gmail, you need to:
# 1. Enable 2-factor authentication on your Google account
# 2. Generate an "App Password" (not your regular password)
# 3. Use the App Password in MAIL_PASS field

# Queue Configuration
QUEUE_REDIS_HOST=redis
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_PASSWORD=
QUEUE_REDIS_DB=1

# Worker Configuration
WORKER_CONCURRENCY=5
WORKER_MAX_STALLED_COUNT=3
WORKER_STALLED_INTERVAL=30000

# Scheduler Configuration
SCHEDULER_ENABLED=true
SCHEDULER_TIMEZONE=UTC

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_DIR="uploads"
BASE_URL="http://localhost:3000"

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this-in-production

# API Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Africa's Talking Configuration
AFRICASTALKING_USERNAME=your-username
AFRICASTALKING_API_KEY=your-api-key
AFRICASTALKING_CLIENT_PREFIX=kb-tracker
