<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .content {
            background-color: #ffffff;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .reminder {
            background-color: #e8f5e8;
            padding: 15px;
            border-left: 4px solid #4caf50;
            margin: 15px 0;
            border-radius: 4px;
        }
        .details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .footer {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #666;
        }
        .time-highlight {
            color: #1976d2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔔 {{title}}</h1>
    </div>
    
    <div class="content">
        <p>Hello {{recipientName}},</p>
        
        <div class="reminder">
            <p><strong>{{message}}</strong></p>
        </div>
        
        {{#if data}}
        <div class="details">
            <h3>Follow-up Details:</h3>
            <ul>
                {{#if data.leadName}}
                <li><strong>Lead:</strong> {{data.leadName}}</li>
                {{/if}}
                {{#if data.followUpType}}
                <li><strong>Type:</strong> {{#if (eq data.followUpType 'call')}}Call{{else}}Visit{{/if}}</li>
                {{/if}}
                {{#if data.scheduledTime}}
                <li><strong>Scheduled Time:</strong> <span class="time-highlight">{{formatDate data.scheduledTime}}</span></li>
                {{/if}}
            </ul>
        </div>
        {{/if}}
        
        <p>This is a friendly reminder about your upcoming follow-up. Please prepare accordingly and ensure you have all necessary information ready.</p>
        
        <p>If you need to reschedule, please update the follow-up in the KB Tracker system.</p>
        
        <p>Best regards,<br>
        KB Tracker System</p>
    </div>
    
    <div class="footer">
        <p>This is an automated reminder from KB Tracker. Please do not reply to this email.</p>
        <p>Generated on {{timestamp}}</p>
    </div>
</body>
</html>
