# Africa's Talking Voice Integration

This document describes the implementation of Africa's Talking voice callback endpoints in the kb-tracker-backend application.

## Overview

The voice integration provides three main endpoints for Africa's Talking voice and WebRTC integration:

1. **POST /api/v1/voice/callback** - Handles inbound/outbound call notifications
2. **POST /api/v1/voice/events** - Receives call lifecycle events
3. **GET /api/v1/get-capability-token** - WebRTC capability token proxy endpoint

## Endpoints

### 1. Voice Callback Endpoint

**URL:** `POST /api/v1/voice/callback`

**Purpose:** Handle when Africa's Talking notifies you about an inbound or outbound call.

**Content-Type:** Returns `application/xml`

**Request Body Example:**

```json
{
  "sessionId": "ATVoice_12345678-1234-1234-1234-************",
  "from": "+************",
  "to": "+************",
  "direction": "inbound",
  "callStatus": "Ringing",
  "startTime": "2025-01-15T10:30:00.000Z"
}
```

**Response Example:**

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Dial record="true" sequential="true" phoneNumbers="" ringbackTone="ringback.wav" />
</Response>
```

### 2. Voice Events Endpoint

**URL:** `POST /api/v1/voice/events`

**Purpose:** Receive call lifecycle events (Ringing, Answered, Completed, Failed).

**Content-Type:** Returns `application/json`

**Request Body Example:**

```json
{
  "sessionId": "ATVoice_12345678-1234-1234-1234-************",
  "eventType": "Completed",
  "from": "+************",
  "to": "+************",
  "durationInSeconds": 120,
  "recordingUrl": "https://voice.africastalking.com/recordings/12345.mp3",
  "timestamp": "2025-01-15T10:32:00.000Z"
}
```

**Response Example:**

```json
{
  "status": "ok",
  "timestamp": "2025-01-15T10:32:00.000Z",
  "sessionId": "ATVoice_12345678-1234-1234-1234-************"
}
```

### 3. WebRTC Capability Token Endpoint

**URL:** `GET /api/v1/get-capability-token`

**Purpose:** Backend proxy to Africa's Talking WebRTC capability token API for frontend WebRTC integration.

**Content-Type:** Returns `application/json`

**Response Example:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "clientName": "kb-tracker-1704067800000",
  "expiresAt": 1704071400,
  "additionalData": {
    "ttl": 3600
  }
}
```

**Error Response (502 Bad Gateway):**

```json
{
  "message": "Failed to retrieve capability token from Africa's Talking",
  "statusCode": 502,
  "timestamp": "2025-01-15T10:30:00.000Z"
}
```

## Implementation Details

### File Structure

```
src/voice/
├── dto/
│   ├── voice-callback.dto.ts    # Request DTO for callback endpoint
│   ├── voice-event.dto.ts       # Request DTO for events endpoint
│   ├── voice-response.dto.ts    # Response DTOs
│   └── capability-token.dto.ts  # WebRTC capability token DTOs
├── voice.controller.ts          # Voice callback/events controller
├── webrtc.controller.ts         # WebRTC capability token controller
├── voice.service.ts             # Business logic for all voice operations
├── voice.module.ts              # NestJS module
├── voice.controller.spec.ts     # Voice controller tests
├── webrtc.controller.spec.ts    # WebRTC controller tests
└── voice.service.spec.ts        # Service tests
```

### Key Features

1. **Comprehensive Logging:** All incoming payloads are logged for monitoring and debugging
2. **Error Handling:** Graceful error handling with fallback responses
3. **Validation:** Request validation using class-validator decorators
4. **Documentation:** Full Swagger/OpenAPI documentation
5. **Testing:** Complete unit test coverage
6. **Modular Design:** Clean separation of concerns following NestJS patterns

### Sounds Directory

The `sounds/` directory contains audio files used for ringback tones:

- `sounds/ringback.wav` - Placeholder file for the ringback tone
- `sounds/README.md` - Documentation for audio file requirements

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Africa's Talking Configuration
AFRICASTALKING_USERNAME=your-username
AFRICASTALKING_API_KEY=your-api-key
AFRICASTALKING_CLIENT_PREFIX=kb-tracker
```

**Variable Descriptions:**

- `AFRICASTALKING_USERNAME`: Your Africa's Talking username
- `AFRICASTALKING_API_KEY`: Your Africa's Talking API key
- `AFRICASTALKING_CLIENT_PREFIX`: Prefix for WebRTC client names (defaults to 'kb-tracker')

### Africa's Talking Setup

1. Configure your Africa's Talking voice application to use these webhook URLs:
   - **Callback URL:** `https://your-domain.com/api/v1/voice/callback`
   - **Events URL:** `https://your-domain.com/api/v1/voice/events`

2. Ensure your server is accessible from the internet for Africa's Talking to send webhooks.

3. For WebRTC integration, the frontend can call `GET /api/v1/get-capability-token` to obtain tokens.

### Audio Files

Replace the placeholder `sounds/ringback.wav` with your actual ringback tone audio file:

- **Format:** WAV, MP3, or other supported formats
- **Duration:** 3-10 seconds (will loop if needed)
- **Quality:** Optimized for telephony (8kHz or 16kHz sample rate)

## Testing

### Unit Tests

Run the voice module tests:

```bash
npm test -- --testPathPattern=voice
```

### Integration Testing

Use the provided test scripts to verify endpoints:

```bash
# Start the server
npm run start:dev

# In another terminal, test voice callback/events endpoints
node test-voice-endpoints.js

# Test WebRTC capability token endpoint
node test-capability-token.js
```

### Manual Testing

You can test the endpoints manually using curl:

```bash
# Test voice callback
curl -X POST http://localhost:3000/api/v1/voice/callback \
  -H "Content-Type: application/json" \
  -d '{
    "sessionId": "ATVoice_test-123",
    "from": "+************",
    "to": "+************",
    "direction": "inbound",
    "callStatus": "Ringing"
  }'

# Test voice events
curl -X POST http://localhost:3000/api/v1/voice/events \
  -H "Content-Type: application/json" \
  -d '{
    "sessionId": "ATVoice_test-123",
    "eventType": "Answered",
    "from": "+************",
    "to": "+************"
  }'

# Test WebRTC capability token
curl -X GET http://localhost:3000/api/v1/get-capability-token
```

## Monitoring and Logs

All voice-related activities are logged using the application's logging system. Look for log entries with the `VoiceController` and `VoiceService` context.

Example log entries:

```
[VoiceController] Received voice callback request
[VoiceService] Received voice callback from Africa's Talking
[VoiceService] Call Session ID: ATVoice_12345678-1234-1234-1234-************
[VoiceService] From: +************
[VoiceService] To: +************
[VoiceService] Direction: inbound
[VoiceService] Call Status: Ringing
```

## Future Enhancements

Potential improvements that can be added:

1. **Database Integration:** Store call records in the database
2. **Activity Linking:** Link voice calls to existing lead activities
3. **Advanced Routing:** Implement complex call routing logic
4. **Call Analytics:** Generate reports on call metrics
5. **Notification Integration:** Trigger notifications for important call events
6. **Recording Management:** Automatically download and store call recordings

## Troubleshooting

### Common Issues

1. **XML Response Issues:** Ensure the callback endpoint returns valid XML with correct Content-Type
2. **Webhook Not Received:** Check that your server is accessible from the internet
3. **Audio File Issues:** Verify audio file format and accessibility
4. **Validation Errors:** Check that request payloads match the expected DTO structure

### Debug Mode

Enable debug logging to see detailed information about incoming requests and responses.

## Support

For issues related to:

- **Africa's Talking API:** Check their documentation and support channels
- **Application Integration:** Review the logs and test endpoints manually
- **Audio Files:** Ensure proper format and file permissions
