import { ApiProperty } from '@nestjs/swagger';

export class NotificationCountDto {
  @ApiProperty({
    description: 'Total number of unread notifications',
    example: 5,
  })
  unread_count: number;

  @ApiProperty({
    description: 'Total number of notifications',
    example: 25,
  })
  total_count: number;

  @ApiProperty({
    description: 'Count by notification type',
    example: {
      LEAD_ASSIGNED: 3,
      USER_CREATED: 1,
      ACTIVITY_CREATED: 1,
    },
  })
  by_type: Record<string, number>;

  @ApiProperty({
    description: 'Count by priority',
    example: {
      HIGH: 2,
      NORMAL: 3,
      LOW: 0,
      URGENT: 0,
    },
  })
  by_priority: Record<string, number>;
}
