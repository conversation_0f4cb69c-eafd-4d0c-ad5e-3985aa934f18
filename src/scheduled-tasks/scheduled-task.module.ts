import { Module, forwardRef } from '@nestjs/common';
import { ScheduledTaskService } from './scheduled-task.service';
import { ScheduledTaskController } from './scheduled-task.controller';
import { TaskExecutionService } from './task-execution.service';
import { BackgroundTaskService } from './background-task.service';
import { TaskHandlerRegistry } from './task-handlers/task-handler.registry';
import { ConsoleLogHandler } from './task-handlers/console-log.handler';
import { DailyNairobiTaskHandler } from './task-handlers/daily-nairobi-task.handler';
import { TargetCreationHandler } from './task-handlers/target-creation.handler';
import { IndividualTargetProgressHandler } from './task-handlers/individual-target-progress.handler';
import { DailyTargetProgressGenerationHandler } from './task-handlers/daily-target-progress-generation.handler';
import { FollowUpReminderHandler } from './task-handlers/follow-up-reminder.handler';
import { OverdueFollowupsNotificationHandler } from './task-handlers/overdue-followups-notification.handler';
import { OverdueReportsHandler } from './task-handlers/overdue-reports.handler';
import { OverdueTargetsReportsHandler } from './task-handlers/overdue-targets-reports.handler';
import { WeeklyConvertedLeadsReportsHandler } from './task-handlers/weekly-converted-leads-reports.handler';
import { MonthlyConvertedLeadsReportsHandler } from './task-handlers/monthly-converted-leads-reports.handler';
import { Overdue2by2by2ReportsHandler } from './task-handlers/overdue-2by2by2-reports.handler';
import { Overdue2by2by2NotificationsHandler } from './task-handlers/overdue-2by2by2-notifications.handler';
import { UpdateActivityCallDataHandler } from './task-handlers/update-activity-call-data.handler';
import { PrismaModule } from '../prisma/prisma.module';
import { VoiceModule } from '../voice/voice.module';
import { QueueProducerModule } from '../queue/queue-producer.module';
import { TargetProgressService } from '../targets/services/target-progress.service';
import { NotificationsModule } from '../notifications/notifications.module';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    PrismaModule,
    forwardRef(() => QueueProducerModule),
    NotificationsModule,
    CommonModule,
    VoiceModule,
  ],
  controllers: [ScheduledTaskController],
  providers: [
    ScheduledTaskService,
    TaskExecutionService,
    BackgroundTaskService,
    TaskHandlerRegistry,
    ConsoleLogHandler,
    DailyNairobiTaskHandler,
    TargetCreationHandler,
    IndividualTargetProgressHandler,
    DailyTargetProgressGenerationHandler,
    FollowUpReminderHandler,
    OverdueFollowupsNotificationHandler,
    OverdueReportsHandler,
    OverdueTargetsReportsHandler,
    WeeklyConvertedLeadsReportsHandler,
    MonthlyConvertedLeadsReportsHandler,
    Overdue2by2by2ReportsHandler,
    Overdue2by2by2NotificationsHandler,
    UpdateActivityCallDataHandler,
    TargetProgressService,
  ],
  exports: [
    ScheduledTaskService,
    TaskExecutionService,
    BackgroundTaskService,
    TaskHandlerRegistry,
  ],
})
export class ScheduledTaskModule {}
