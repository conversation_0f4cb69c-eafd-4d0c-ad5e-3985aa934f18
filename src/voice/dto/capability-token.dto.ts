import { ApiProperty } from '@nestjs/swagger';

export class CapabilityTokenResponseDto {
  @ApiProperty({
    description: 'The capability token for WebRTC',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  token: string;

  @ApiProperty({
    description: 'Client name used for the token',
    example: 'kb-tracker-1704067800000',
  })
  clientName: string;

  @ApiProperty({
    description: 'Token expiration timestamp',
    example: 1704071400,
  })
  lifeTimeSec?: number;

  @ApiProperty({
    description: 'Whether the token allows incoming calls',
    example: true,
  })
  outgoing?: boolean;
  @ApiProperty({
    description: 'Whether the token allows outgoing calls',
    example: true,
  })
  incoming?: boolean;
}

export class CapabilityTokenErrorDto {
  @ApiProperty({
    description: 'Error message',
    example: 'Failed to retrieve capability token',
  })
  message: string;

  @ApiProperty({
    description: 'HTTP status code',
    example: 502,
  })
  statusCode: number;

  @ApiProperty({
    description: 'Error timestamp',
    example: '2025-01-15T10:30:00.000Z',
  })
  timestamp: string;
}
