import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { FileStorageService } from '../common/services/file-storage.service';
import { ProfileResponseDto, UpdateProfileDto } from './dto/profile-response.dto';

/**
 * Service handling user profile operations
 */
@Injectable()
export class ProfileService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly fileStorageService: FileStorageService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Gets the profile of the logged-in user
   */
  async getMyProfile(userId: string): Promise<ProfileResponseDto> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        branch: {
          select: {
            name: true,
          },
        },
        role: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const baseUrl = this.configService.get<string>('BASE_URL') || 'http://localhost:3000';
    
    return {
      name: user.name,
      email: user.email,
      phone_number: user.phone_number,
      profile_photo: user.profile_photo_url 
        ? user.profile_photo_url.startsWith('http') 
          ? user.profile_photo_url 
          : `${baseUrl}${user.profile_photo_url}`
        : null,
      member_since: user.created_at.toISOString(),
      branch: user.branch?.name || 'Unknown Branch',
      role: user.role?.name || 'Unknown Role',
    };
  }

  /**
   * Updates the profile of the logged-in user
   */
  async updateMyProfile(
    userId: string,
    updateData: UpdateProfileDto,
    profilePhoto?: Express.Multer.File,
  ): Promise<ProfileResponseDto> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    let profilePhotoUrl = user.profile_photo_url;

    // Handle profile photo upload
    if (profilePhoto) {
      const savedFile = await this.fileStorageService.saveFile(
        profilePhoto,
        'profile-photos',
      );
      profilePhotoUrl = savedFile.url;
    }

    // Prepare update data
    const updatePayload: any = {};
    if (updateData.name !== undefined) updatePayload.name = updateData.name;
    if (updateData.email !== undefined) updatePayload.email = updateData.email;
    if (updateData.phone_number !== undefined) updatePayload.phone_number = updateData.phone_number;
    if (profilePhotoUrl !== user.profile_photo_url) updatePayload.profile_photo_url = profilePhotoUrl;

    // Update user
    const updatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: updatePayload,
      include: {
        branch: {
          select: {
            name: true,
          },
        },
        role: {
          select: {
            name: true,
          },
        },
      },
    });

    const baseUrl = this.configService.get<string>('BASE_URL') || 'http://localhost:3000';

    return {
      name: updatedUser.name,
      email: updatedUser.email,
      phone_number: updatedUser.phone_number,
      profile_photo: updatedUser.profile_photo_url 
        ? updatedUser.profile_photo_url.startsWith('http') 
          ? updatedUser.profile_photo_url 
          : `${baseUrl}${updatedUser.profile_photo_url}`
        : null,
      member_since: updatedUser.created_at.toISOString(),
      branch: updatedUser.branch?.name || 'Unknown Branch',
      role: updatedUser.role?.name || 'Unknown Role',
    };
  }
}
