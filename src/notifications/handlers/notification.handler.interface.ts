import { NotificationType, NotificationPriority } from '@prisma/client';

export interface NotificationContext {
  entityType: string;
  entityId: string;
  entityData?: Record<string, any>;
  triggerUserId?: string;
  additionalData?: Record<string, any>;
}

export interface NotificationHandler {
  /**
   * The notification type this handler processes
   */
  readonly type: NotificationType;

  /**
   * Check if this handler should process the given context
   */
  shouldHandle(context: NotificationContext): boolean;

  /**
   * Generate notification data for the given context
   */
  generateNotification(context: NotificationContext): Promise<{
    title: string;
    message: string;
    priority: NotificationPriority;
    data?: Record<string, any>;
    recipientIds: string[];
  }>;
}
