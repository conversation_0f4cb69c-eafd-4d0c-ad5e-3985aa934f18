import { Module } from '@nestjs/common';
import { SupportTicketsController } from './support-tickets.controller';
import { SupportTicketsService } from './support-tickets.service';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Support Tickets Module
 * 
 * This module handles support ticket functionality including:
 * - Creating support tickets
 * - Retrieving user's support tickets
 * - Updating support tickets
 * - Deleting support tickets
 * 
 * @module SupportTicketsModule
 */
@Module({
  imports: [PrismaModule],
  controllers: [SupportTicketsController],
  providers: [SupportTicketsService],
  exports: [SupportTicketsService],
})
export class SupportTicketsModule {}
