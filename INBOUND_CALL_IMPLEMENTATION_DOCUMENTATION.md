# Inbound Call Implementation Documentation

## Overview

This document provides comprehensive documentation of the inbound call functionality implementation using Africa's Talking Voice API. The implementation allows leads to call a shared number and be automatically routed to the specific user who previously made an unsuccessful call to them.

## Implementation Summary

The inbound call system has been fully implemented with the following components:

### 1. Database Schema Updates ✅

**Activity Model Enhancements:**
- Added `call_direction` enum field (INBOUND/OUTBOUND)
- Added `session_id` field for Africa's Talking session tracking
- Added `at_call_id` field for Africa's Talking call ID
- Added `recording_url` field for call recording storage
- Renamed `call_duration_minutes` to `call_duration_seconds` for better precision

**New Enum:**
```prisma
enum CallDirection {
  INBOUND
  OUTBOUND
}
```

### 2. Voice Module Structure ✅

Created a complete voice module with the following files:

#### `src/voice/voice.module.ts`
- Defines the VoiceModule with all necessary providers and controllers
- Imports PrismaModule for database access
- Exports VoiceService for use in other modules

#### `src/voice/voice.controller.ts`
- Handles webhook endpoints for Africa's Talking
- **Endpoints:**
  - `POST /api/v1/voice/inbound` - Handles inbound call webhooks
  - `POST /api/v1/voice/call-status` - Handles call status updates
  - `POST /api/v1/voice/recording` - Handles recording data
- Includes comprehensive logging and error handling
- Returns XML responses for call routing

#### `src/voice/voice.service.ts`
- **Core call routing logic:**
  - Finds leads by phone number with normalization
  - Detects recent missed calls (within 24 hours)
  - Routes calls to specific users or default agents
  - Handles error scenarios gracefully
- **Key methods:**
  - `handleInboundCall()` - Main call routing logic
  - `findLeadByPhone()` - Lead lookup with phone normalization
  - `findRecentMissedCall()` - Missed call detection
  - `getUserPhoneNumber()` - User phone number retrieval
  - `getDefaultAgentPhoneNumber()` - Default agent fallback

#### `src/voice/voice-xml.service.ts`
- **XML response generation for Africa's Talking:**
  - `generateDefaultResponse()` - Routes to default agent
  - `generateRouteToUserResponse()` - Routes to specific user
  - `generateRejectResponse()` - Rejects unknown numbers
  - `generateErrorResponse()` - Handles errors gracefully
  - `generateCustomResponse()` - Custom messages with optional routing
  - `generateHoldResponse()` - Hold music and routing
- All responses include call recording configuration

#### `src/voice/inbound-call.service.ts`
- **Activity management:**
  - `createInboundCallActivity()` - Creates inbound call activities
  - `updateCallStatus()` - Updates call status and duration
  - `updateRecording()` - Updates recording URLs
- **Helper methods:**
  - `findLeadByPhone()` - Lead lookup with normalization
  - `getDefaultUser()` - Default user assignment
- Includes comprehensive logging and error handling

### 3. DTOs for Voice Webhooks ✅

#### `src/voice/dto/inbound-call.dto.ts`
```typescript
export class InboundCallDto {
  from: string;                    // Caller's phone number
  to: string;                      // Called number
  sessionId: string;               // Africa's Talking session ID
  direction: string;               // 'inbound' or 'outbound'
  callSessionState: string;        // Call status
  durationInSeconds?: string;      // Call duration
  recordingUrl?: string;           // Recording URL
  callId?: string;                 // Africa's Talking call ID
  userId?: string;                 // User ID for the call
}
```

#### `src/voice/dto/call-status.dto.ts`
```typescript
export class CallStatusDto {
  sessionId: string;               // Session ID
  status: string;                  // Call status
  duration?: string;               // Call duration
  callId?: string;                 // Call ID
  hangupCause?: string;            // Hangup reason
  recordingUrl?: string;           // Recording URL
}
```

#### `src/voice/dto/recording.dto.ts`
```typescript
export class RecordingDto {
  sessionId: string;               // Session ID
  recordingUrl: string;            // Recording URL
  durationInSeconds?: string;      // Recording duration
  callId?: string;                 // Call ID
  fileSize?: string;               // File size
}
```

### 4. Call Routing Logic ✅

**The system implements the following routing logic:**

1. **Lead Identification:**
   - Normalizes incoming phone numbers (removes spaces, dashes, parentheses)
   - Searches for leads using partial phone number matching
   - Handles cases where no lead is found

2. **Missed Call Detection:**
   - Searches for recent outbound calls (within 24 hours) that were missed
   - Uses `call_direction: 'OUTBOUND'` and `call_status: 'missed'`
   - Orders by creation date to get the most recent missed call

3. **Call Routing:**
   - **If recent missed call found:** Routes to the user who made the missed call
   - **If no missed call but lead has assigned user:** Routes to assigned user
   - **If no assigned user:** Routes to default agent
   - **If no lead found:** Routes to default agent

4. **Error Handling:**
   - Graceful fallbacks for all scenarios
   - Comprehensive logging for debugging
   - XML error responses for Africa's Talking

### 5. Activity Management ✅

**Inbound Call Activity Creation:**
- Creates activity record when call is received
- Sets initial status to 'ringing'
- Associates with lead if found
- Assigns to appropriate user (lead's RM, assigned user, or default)

**Call Status Updates:**
- Maps Africa's Talking statuses to internal statuses:
  - `answered` → `success`
  - `no-answer`/`busy` → `missed`
  - `failed` → `failed`
  - `completed` → `completed`
- Updates call duration and recording URL
- Maintains session tracking

**Recording Management:**
- Stores recording URLs when available
- Updates activities with recording information
- Handles recording callbacks from Africa's Talking

### 6. Environment Configuration ✅

**Required Environment Variables:**
```env
WEBHOOK_BASE_URL=https://yourdomain.com/api/v1
DEFAULT_AGENT_PHONE=+************
AT_USERNAME=your_username
AT_API_KEY=your_api_key
```

**Webhook URL Structure:**
- Base URL includes the global prefix `/api/v1`
- Webhook endpoints:
  - `{WEBHOOK_BASE_URL}/voice/inbound`
  - `{WEBHOOK_BASE_URL}/voice/call-status`
  - `{WEBHOOK_BASE_URL}/voice/recording`

### 7. Integration with Existing System ✅

**Activity Model Integration:**
- Uses existing `Activity` model without modifications
- Leverages `performed_by_user_id` to track who should handle the call
- Maintains consistency with existing call activity structure
- Supports all existing activity features (attachments, follow-ups, etc.)

**User Management:**
- Integrates with existing user system
- Uses user phone numbers for call routing
- Maintains branch associations
- Supports role-based access control

**Lead Management:**
- Integrates with existing lead system
- Uses lead phone numbers for identification
- Maintains lead-user relationships
- Supports lead assignment logic

## API Endpoints

### 1. Inbound Call Webhook
```
POST /api/v1/voice/inbound
Content-Type: application/json

{
  "from": "+************",
  "to": "+************",
  "sessionId": "ATUid_1234567890abcdef",
  "direction": "inbound",
  "callSessionState": "ringing"
}
```

**Response:** XML for call routing

### 2. Call Status Webhook
```
POST /api/v1/voice/call-status
Content-Type: application/json

{
  "sessionId": "ATUid_1234567890abcdef",
  "status": "completed",
  "duration": "120",
  "recordingUrl": "https://api.africastalking.com/voice/recording/123456"
}
```

**Response:** `{ "message": "Call status updated successfully" }`

### 3. Recording Webhook
```
POST /api/v1/voice/recording
Content-Type: application/json

{
  "sessionId": "ATUid_1234567890abcdef",
  "recordingUrl": "https://api.africastalking.com/voice/recording/123456",
  "durationInSeconds": "120"
}
```

**Response:** `{ "message": "Recording URL saved successfully" }`

## Call Flow

### 1. Inbound Call Received
1. Lead calls the shared number
2. Africa's Talking sends webhook to `/voice/inbound`
3. System creates inbound call activity
4. System finds lead by phone number
5. System checks for recent missed calls
6. System generates XML response for routing

### 2. Call Routing
1. **If recent missed call found:**
   - Route to user who made the missed call
2. **If no missed call but lead assigned:**
   - Route to lead's assigned user
3. **If no assignment:**
   - Route to default agent
4. **If no lead found:**
   - Route to default agent

### 3. Call Status Updates
1. Africa's Talking sends status updates to `/voice/call-status`
2. System updates activity with new status
3. System updates call duration if provided
4. System updates recording URL if provided

### 4. Recording Management
1. Africa's Talking sends recording data to `/voice/recording`
2. System updates activity with recording URL
3. System stores recording metadata

## Testing Setup

### 1. Local Development with ngrok
```bash
# Install ngrok
npm install -g ngrok

# Start your application
npm run start:dev

# In another terminal, expose your local server
ngrok http 3000

# Use the ngrok URL for webhook configuration
# Example: https://abc123.ngrok.io/api/v1/voice/inbound
```

### 2. Environment Configuration
```env
WEBHOOK_BASE_URL=https://abc123.ngrok.io/api/v1
DEFAULT_AGENT_PHONE=+************
AT_USERNAME=your_username
AT_API_KEY=your_api_key
```

### 3. Africa's Talking Configuration
1. Set webhook URL to your ngrok URL + `/api/v1/voice/inbound`
2. Configure call recording settings
3. Set up your shared phone number
4. Test with actual phone calls

## Error Handling

### 1. Database Errors
- Graceful fallbacks for database connection issues
- Comprehensive error logging
- User-friendly error responses

### 2. Lead Not Found
- Routes to default agent
- Logs warning for monitoring
- Continues call processing

### 3. User Not Available
- Falls back to default agent
- Logs error for debugging
- Maintains call continuity

### 4. Africa's Talking Errors
- XML error responses
- Graceful degradation
- Comprehensive logging

## Monitoring and Logging

### 1. Comprehensive Logging
- All services include detailed logging
- Log levels: INFO, WARN, ERROR
- Structured logging for easy parsing

### 2. Key Metrics to Monitor
- Inbound call volume
- Call routing success rate
- Lead identification accuracy
- Missed call detection rate
- Recording success rate

### 3. Error Tracking
- All errors are logged with stack traces
- Webhook failures are tracked
- Database errors are monitored

## Security Considerations

### 1. Webhook Security
- Consider implementing signature verification
- Use HTTPS for all webhook endpoints
- Validate incoming data

### 2. Data Privacy
- Call recordings are stored securely
- Phone numbers are normalized and validated
- User data is protected

### 3. Rate Limiting
- Consider implementing rate limiting for webhooks
- Monitor for abuse patterns
- Implement proper authentication

## Future Enhancements

### 1. Advanced Routing
- Time-based routing (business hours)
- Skill-based routing
- Queue management

### 2. Analytics
- Call analytics dashboard
- Performance metrics
- User activity tracking

### 3. Integration
- CRM integration
- Calendar integration
- Notification systems

## Conclusion

The inbound call system has been successfully implemented with:

✅ **Complete voice module structure**
✅ **Comprehensive call routing logic**
✅ **Activity management system**
✅ **Webhook handling**
✅ **Error handling and logging**
✅ **Integration with existing system**
✅ **Environment configuration**
✅ **Testing setup**

The system is ready for deployment and testing with Africa's Talking Voice API. All components are properly integrated with the existing codebase and follow NestJS best practices.

## Next Steps

1. **Deploy the application** to your production environment
2. **Configure Africa's Talking** with your webhook URLs
3. **Test the system** with actual phone calls
4. **Monitor the logs** for any issues
5. **Fine-tune the routing logic** based on real-world usage

The implementation provides a solid foundation for inbound call management and can be extended with additional features as needed.
