import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { EmailService } from '../../common/services/email.service';
import { NotificationJobData } from '../queue.service';

@Processor('notifications')
export class NotificationProcessor extends WorkerHost {
  private readonly logger = new Logger(NotificationProcessor.name);

  constructor(private readonly emailService: EmailService) {
    super();
  }

  async process(job: Job<NotificationJobData>) {
    const { notificationId, type, notificationType, title, message, data } = job.data;
    
    this.logger.log(`Processing notification job ${job.id} for notification ${notificationId} (type: ${type})`);

    try {
      await job.updateProgress(10);

      switch (type) {
        case 'email':
          await this.processEmailNotification(job);
          break;
        case 'sms':
          await this.processSmsNotification(job);
          break;
        case 'push':
          await this.processPushNotification(job);
          break;
        default:
          throw new Error(`Unsupported notification type: ${type}`);
      }

      await job.updateProgress(100);
      this.logger.log(`Notification job ${job.id} completed successfully`);
      
      return { 
        success: true, 
        notificationId, 
        type, 
        processedAt: new Date().toISOString() 
      };

    } catch (error) {
      this.logger.error(
        `Failed to process notification job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async processEmailNotification(job: Job<NotificationJobData>) {
    const { recipientEmail, recipientName, notificationType, title, message, data } = job.data;
    
    await job.updateProgress(20);

    if (!recipientEmail) {
      throw new Error('Recipient email is required for email notifications');
    }

    // Determine email template based on notification type
    const template = this.getEmailTemplate(notificationType);
    const subject = this.getEmailSubject(notificationType, title);
    
    await job.updateProgress(40);

    // Prepare email context
    const context = {
      recipientName: recipientName || 'User',
      title,
      message,
      notificationType,
      data: data || {},
      timestamp: new Date().toISOString(),
      appName: 'KB Tracker',
    };

    await job.updateProgress(60);

    // Send email
    await this.emailService.sendEmail(
      recipientEmail,
      subject,
      template,
      context,
    );

    await job.updateProgress(80);
    this.logger.log(`Email notification sent to ${recipientEmail}`);
  }

  private async processSmsNotification(job: Job<NotificationJobData>) {
    const { recipientPhone, notificationType, title, message } = job.data;
    
    await job.updateProgress(20);

    if (!recipientPhone) {
      throw new Error('Recipient phone is required for SMS notifications');
    }

    // TODO: Implement SMS service integration
    // For now, just log the SMS notification
    this.logger.log(`SMS notification would be sent to ${recipientPhone}: ${title} - ${message}`);
    
    await job.updateProgress(80);
  }

  private async processPushNotification(job: Job<NotificationJobData>) {
    const { notificationType, title, message, data } = job.data;
    
    await job.updateProgress(20);

    // TODO: Implement push notification service integration
    // For now, just log the push notification
    this.logger.log(`Push notification would be sent: ${title} - ${message}`);
    
    await job.updateProgress(80);
  }

  private getEmailTemplate(notificationType: string): string {
    // Map notification types to email templates
    const templateMap: Record<string, string> = {
      LEAD_ASSIGNED: 'lead-assigned',
      LEAD_UPDATED: 'lead-updated',
      LEAD_CONVERTED: 'lead-converted',
      USER_CREATED: 'user-created',
      USER_UPDATED: 'user-updated',
      ACTIVITY_CREATED: 'activity-created',
      ACTIVITY_UPDATED: 'activity-updated',
      TARGET_ACHIEVED: 'target-achieved',
      TARGET_MISSED: 'target-missed',
      FOLLOW_UP_DUE: 'follow-up-due',
      FOLLOW_UP_OVERDUE: 'follow-up-overdue',
      SYSTEM_ANNOUNCEMENT: 'system-announcement',
      CUSTOM: 'custom-notification',
    };

    return templateMap[notificationType] || 'default-notification';
  }

  private getEmailSubject(notificationType: string, title: string): string {
    // Add app name prefix to email subjects
    return `[KB Tracker] ${title}`;
  }
}
