import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { TargetProgressService } from '../services/target-progress.service';
import { CreateTargetDto } from '../dto/create-target.dto';
import { isSameDay } from '../../common/utils/date.utils';

/**
 * Handler for daily frequency targets
 * Manages the creation of daily targets and their associated progress records
 */
@Injectable()
export class DailyTargetHandler {
  constructor(
    private readonly prisma: PrismaService,
    private readonly targetProgressService: TargetProgressService,
  ) {}

  /**
   * Handle creation of daily targets based on the provided data
   * @param createTargetDto - Data for creating targets
   * @param userBranchId - Branch ID of the logged-in user
   * @returns Promise<string[]> - Array of created target IDs
   */
  async handleDailyTargets(
    createTargetDto: CreateTargetDto,
    userBranchId: string,
  ): Promise<string[]> {
    const {
      metricType,
      targetValue,
      frequency,
      startDate,
      endDate,
      scope,
      assignTo,
      activity,
    } = createTargetDto;

    // Validate that this is a daily frequency target
    if (frequency !== 'daily') {
      throw new BadRequestException(
        'This handler only processes daily frequency targets',
      );
    }

    // Parse dates
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : null;

    // Create targets for each assignTo ID
    const targetsData = assignTo.map((id) => ({
      metric_type: metricType as 'Call' | 'Visit',
      target_value: targetValue,
      frequency: frequency as 'daily',
      activity: activity as 'LEADS' | 'CUSTOMER_SERVICE' | 'LOAN_ACTIVITIES',
      start_date: start,
      end_date: end, // Keep as null if endDate was null
      branch_id: userBranchId,
      ...(scope === 'role' ? { role_id: id } : { user_id: id }),
    }));

    // Create targets first
    const createdTargets: any[] = [];
    for (const targetData of targetsData) {
      const target = await this.prisma.target.create({
        data: {
          ...targetData,
          end_date: targetData.end_date || undefined, // Convert null to undefined for Prisma
        },
      });
      createdTargets.push(target);
    }

    // Note: TargetProgress records are now created via background tasks
    // This keeps the API non-blocking while ensuring progress records are created

    return createdTargets.map((t) => t.id);
  }

  /**
   * Validate role IDs exist in the database
   * @param roleIds - Array of role IDs to validate
   * @returns Promise<void>
   * @throws BadRequestException if any role is not found
   */
  async validateRoles(roleIds: string[]): Promise<void> {
    const roles = await this.prisma.role.findMany({
      where: {
        id: {
          in: roleIds,
        },
      },
      select: {
        id: true,
      },
    });

    const foundRoleIds = roles.map((role) => role.id);
    const missingRoleIds = roleIds.filter((id) => !foundRoleIds.includes(id));

    if (missingRoleIds.length > 0) {
      throw new BadRequestException(
        `The following role IDs were not found: ${missingRoleIds.join(', ')}`,
      );
    }
  }

  /**
   * Validate user IDs exist in the database
   * @param userIds - Array of user IDs to validate
   * @returns Promise<void>
   * @throws BadRequestException if any user is not found
   */
  async validateUsers(userIds: string[]): Promise<void> {
    const users = await this.prisma.user.findMany({
      where: {
        id: {
          in: userIds,
        },
      },
      select: {
        id: true,
      },
    });

    const foundUserIds = users.map((user) => user.id);
    const missingUserIds = userIds.filter((id) => !foundUserIds.includes(id));

    if (missingUserIds.length > 0) {
      throw new BadRequestException(
        `The following user IDs were not found: ${missingUserIds.join(', ')}`,
      );
    }
  }
}
