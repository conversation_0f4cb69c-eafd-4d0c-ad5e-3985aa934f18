import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';
import { EmailService } from '../../common/services/email.service';
import { isApplicable } from '../../common/utils/date.utils';
import { NotificationUtils } from '../../notifications/utils/notification.utils';
import { NotificationType } from '@prisma/client';

// Initialize pdfmake
const pdfMake = require('pdfmake/build/pdfmake');
const pdfFonts = require('pdfmake/build/vfs_fonts');

// Set fonts for pdfmake (pdfFonts contains the font data directly)
pdfMake.vfs = pdfFonts;

export interface OverdueTargetsReportsPayload {
  // Empty payload - uses current date
}

interface OverdueTargetProgress {
  id: string;
  achieved_count: number;
  target_value: number;
  is_achieved: boolean | null;
  user: {
    id: string;
    name: string;
    rm_code: string | null;
  };
  target: {
    id: string;
    metric_type: string;
    activity: string;
    branch: {
      id: string;
      name: string;
      region: {
        id: string;
        name: string;
      };
    };
  };
}

@Injectable()
export class OverdueTargetsReportsHandler implements TaskHandler {
  private readonly logger = new Logger(OverdueTargetsReportsHandler.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly emailService: EmailService,
    private readonly notificationUtils: NotificationUtils,
  ) {}

  getTaskType(): string {
    return 'overdue-targets-reports';
  }

  getDescription(): string {
    return 'Generates and sends PDF reports of overdue targets to users based on their permissions';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    return { isValid: true, errors: [] };
  }

  async handle(payload: OverdueTargetsReportsPayload, job: Job): Promise<any> {
    this.logger.log(
      'Processing overdue targets reports generation and distribution',
    );

    let reportsSent = 0;
    let overdueCount = 0;
    let usersProcessed = 0;

    try {
      await job.updateProgress(5);

      // Check if today is applicable for sending reports (not weekend or holiday)
      const today = new Date();
      const applicable = await isApplicable(today, this.prisma);

      if (!applicable) {
        this.logger.log(
          'Skipping overdue targets reports - today is a weekend or holiday',
        );

        return {
          success: true,
          overdueCount: 0,
          reportsSent: 0,
          usersProcessed: 0,
          skipped: true,
          reason: 'Weekend or holiday',
          processedAt: new Date().toISOString(),
        };
      }

      // Get today's date (DATE ONLY, no time)
      today.setHours(0, 0, 0, 0);

      const startOfDay = new Date(today);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(today);
      endOfDay.setHours(23, 59, 59, 999);

      // Find all overdue target progress records (for_date matches today and is_achieved is null or false and is_applicable is true)
      const overdueTargetProgress = await this.prisma.targetProgress.findMany({
        where: {
          for_date: {
            gte: startOfDay,
            lte: endOfDay,
          },
          is_applicable: true,
          OR: [{ is_achieved: false }, { is_achieved: null }],
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              rm_code: true,
            },
          },
          target: {
            select: {
              id: true,
              metric_type: true,
              activity: true,
              branch: {
                select: {
                  id: true,
                  name: true,
                  region: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: [
          { target: { branch: { name: 'asc' } } },
          { user: { name: 'asc' } },
          { for_date: 'desc' },
        ],
      });

      await job.updateProgress(15);

      if (overdueTargetProgress.length === 0) {
        this.logger.log('No overdue targets found for today');
        return { success: true, overdueCount: 0, reportsSent: 0 };
      }

      this.logger.log(`Found ${overdueTargetProgress.length} overdue targets`);

      await job.updateProgress(25);

      // Get users with report permissions
      const usersWithPermissions = await this.prisma.user.findMany({
        where: {
          role: {
            role_permissions: {
              some: {
                permission: {
                  id: {
                    in: [
                      'reports.overdue.targets.cs.all',
                      'reports.overdue.targets.leads.all',
                      'reports.overdue.targets.cs.branch',
                      'reports.overdue.targets.leads.branch',
                      'reports.overdue.targets.cs.region',
                      'reports.overdue.targets.leads.region',
                    ],
                  },
                },
              },
            },
          },
        },
        include: {
          branch: {
            include: {
              region: true,
            },
          },
          role: {
            include: {
              role_permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      await job.updateProgress(35);

      let reportsSent = 0;
      const progressStep = 60 / usersWithPermissions.length;

      for (const user of usersWithPermissions) {
        try {
          const userPermissions = user.role.role_permissions.map(
            (rp) => rp.permission.id,
          );

          // Process permissions with hierarchy: all > region > branch
          // Each user gets only one report per activity type based on their highest permission level
          const reportsToGenerate: Array<{
            targets: OverdueTargetProgress[];
            scope: string;
            type: string;
          }> = [];

          // Process Customer Service permissions with hierarchy
          if (userPermissions.includes('reports.overdue.targets.cs.all')) {
            // Highest priority: All branches
            const csTargets = overdueTargetProgress.filter(
              (tp) => tp.target.activity === 'CUSTOMER_SERVICE',
            );
            if (csTargets.length > 0) {
              reportsToGenerate.push({
                targets: csTargets,
                scope: 'All Branches',
                type: 'Customer Service',
              });
            }
          } else if (
            userPermissions.includes('reports.overdue.targets.cs.region')
          ) {
            // Medium priority: Region level
            const csRegionTargets = overdueTargetProgress.filter(
              (tp) =>
                tp.target.branch.region.id === user.branch.region.id &&
                tp.target.activity === 'CUSTOMER_SERVICE',
            );
            if (csRegionTargets.length > 0) {
              reportsToGenerate.push({
                targets: csRegionTargets,
                scope: user.branch.region.name,
                type: 'Customer Service',
              });
            }
          } else if (
            userPermissions.includes('reports.overdue.targets.cs.branch')
          ) {
            // Lowest priority: Branch level
            const csBranchTargets = overdueTargetProgress.filter(
              (tp) =>
                tp.target.branch.id === user.branch_id &&
                tp.target.activity === 'CUSTOMER_SERVICE',
            );
            if (csBranchTargets.length > 0) {
              reportsToGenerate.push({
                targets: csBranchTargets,
                scope: user.branch.name,
                type: 'Customer Service',
              });
            }
          }

          // Process LEADS permissions with hierarchy
          if (userPermissions.includes('reports.overdue.targets.leads.all')) {
            // Highest priority: All branches
            const leadsTargets = overdueTargetProgress.filter(
              (tp) => tp.target.activity === 'LEADS',
            );
            if (leadsTargets.length > 0) {
              reportsToGenerate.push({
                targets: leadsTargets,
                scope: 'All Branches',
                type: 'Leads',
              });
            }
          } else if (
            userPermissions.includes('reports.overdue.targets.leads.region')
          ) {
            // Medium priority: Region level
            const leadsRegionTargets = overdueTargetProgress.filter(
              (tp) =>
                tp.target.branch.region.id === user.branch.region.id &&
                tp.target.activity === 'LEADS',
            );
            if (leadsRegionTargets.length > 0) {
              reportsToGenerate.push({
                targets: leadsRegionTargets,
                scope: user.branch.region.name,
                type: 'Leads',
              });
            }
          } else if (
            userPermissions.includes('reports.overdue.targets.leads.branch')
          ) {
            // Lowest priority: Branch level
            const leadsBranchTargets = overdueTargetProgress.filter(
              (tp) =>
                tp.target.branch.id === user.branch_id &&
                tp.target.activity === 'LEADS',
            );
            if (leadsBranchTargets.length > 0) {
              reportsToGenerate.push({
                targets: leadsBranchTargets,
                scope: user.branch.name,
                type: 'Leads',
              });
            }
          }

          // Generate reports for each permission type
          if (reportsToGenerate.length > 0) {
            for (const report of reportsToGenerate) {
              try {
                await this.generateAndSendReport(
                  user,
                  report.targets,
                  report.scope,
                  report.type,
                );
                reportsSent++;
              } catch (reportError) {
                console.error(
                  `Overdue Targets Reports Handler: Failed to send report to ${user.email}:`,
                  reportError,
                );
                this.logger.error(
                  `Failed to send report to ${user.email}:`,
                  reportError,
                );
              }
            }
          }
        } catch (error) {
          this.logger.error(
            `Failed to generate report for user ${user.id}:`,
            error,
          );
        }

        await job.updateProgress(35 + reportsSent * progressStep);
      }

      await job.updateProgress(100);

      this.logger.log(
        `Overdue targets reports task completed. Sent ${reportsSent} reports`,
      );

      return {
        success: true,
        overdueCount: overdueTargetProgress.length,
        reportsSent,
        usersProcessed: usersWithPermissions.length,
        processedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to process overdue targets reports:', error);
      console.error('Overdue Targets Reports Handler: Error:', error);
      throw error;
    }
  }

  private async generateAndSendReport(
    user: any,
    overdueTargets: OverdueTargetProgress[],
    reportScope: string,
    reportType: string,
  ): Promise<void> {
    // Calculate statistics
    const totalOverdue = overdueTargets.length;

    // Group by branch for "all" and "region" reports, by agent for "branch" reports
    const isAllOrRegion =
      reportScope === 'All Branches' || reportScope !== user.branch.name;

    let groupedData: { [key: string]: OverdueTargetProgress[] } = {};

    if (isAllOrRegion) {
      // Group by branch
      groupedData = overdueTargets.reduce(
        (acc, tp) => {
          const key = tp.target.branch.name;
          if (!acc[key]) acc[key] = [];
          acc[key].push(tp);
          return acc;
        },
        {} as { [key: string]: OverdueTargetProgress[] },
      );
    } else {
      // Group by agent
      groupedData = overdueTargets.reduce(
        (acc, tp) => {
          const key = tp.user.name;
          if (!acc[key]) acc[key] = [];
          acc[key].push(tp);
          return acc;
        },
        {} as { [key: string]: OverdueTargetProgress[] },
      );
    }

    // Generate PDF content
    const pdfContent = this.generatePdfContent(
      reportScope,
      reportType,
      totalOverdue,
      groupedData,
      isAllOrRegion,
    );

    // Generate PDF buffer
    const pdfBuffer = await this.generatePdfBuffer(pdfContent);

    // Send email with PDF attachment
    await this.sendReportEmail(
      user,
      reportScope,
      reportType,
      totalOverdue,
      pdfBuffer,
    );
  }

  private generatePdfContent(
    reportScope: string,
    reportType: string,
    totalOverdue: number,
    groupedData: { [key: string]: OverdueTargetProgress[] },
    isAllOrRegion: boolean,
  ): any {
    const today = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    // Create table data
    const tableBody: any[][] = [
      [
        'Agent Name',
        'RM Code',
        'Type',
        'Achieved',
        ...(isAllOrRegion ? ['Branch'] : []),
      ],
    ];

    // Add rows grouped by branch or agent
    Object.keys(groupedData).forEach((groupKey) => {
      // Add group header
      const headerRow: any[] = [
        {
          text: groupKey,
          style: 'groupHeader',
          colSpan: isAllOrRegion ? 5 : 4,
        },
      ];
      // Add empty cells for the remaining columns
      for (let i = 1; i < (isAllOrRegion ? 5 : 4); i++) {
        headerRow.push({});
      }
      tableBody.push(headerRow);

      // Add targets for this group
      groupedData[groupKey].forEach((tp) => {
        const achievedText = `${tp.achieved_count}/${tp.target_value}`;
        const typeText = tp.target.metric_type === 'Call' ? 'Call' : 'Visit';

        tableBody.push([
          tp.user.name,
          tp.user.rm_code || 'N/A',
          typeText,
          achievedText,
          ...(isAllOrRegion ? [tp.target.branch.name] : []),
        ]);
      });
    });

    return {
      content: [
        // Header
        {
          text: `Overdue Targets Report - ${reportType}`,
          style: 'header',
          alignment: 'center',
          margin: [0, 0, 0, 20],
        },
        {
          columns: [
            { text: `Scope: ${reportScope}`, style: 'subheader' },
            { text: `Date: ${today}`, style: 'subheader', alignment: 'right' },
          ],
          margin: [0, 0, 0, 20],
        },

        // Summary Section
        {
          text: 'Summary',
          style: 'sectionHeader',
          margin: [0, 0, 0, 10],
        },
        {
          ul: [
            `Total overdue targets: ${totalOverdue}`,
            `Report type: ${reportType}`,
            `Scope: ${reportScope}`,
          ],
          margin: [0, 0, 0, 20],
        },

        // Table
        {
          table: {
            headerRows: 1,
            widths: isAllOrRegion
              ? ['*', 'auto', 'auto', 'auto', 'auto']
              : ['*', 'auto', 'auto', 'auto'],
            body: tableBody,
          },
          layout: 'lightHorizontalLines',
        },
      ],
      styles: {
        header: {
          fontSize: 18,
          bold: true,
        },
        subheader: {
          fontSize: 12,
          bold: true,
        },
        sectionHeader: {
          fontSize: 14,
          bold: true,
        },
        groupHeader: {
          fontSize: 12,
          bold: true,
          fillColor: '#f0f0f0',
        },
      },
      defaultStyle: {
        fontSize: 10,
      },
    };
  }

  private generatePdfBuffer(docDefinition: any): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        if (!pdfMake || typeof pdfMake.createPdf !== 'function') {
          throw new Error('pdfMake is not properly initialized');
        }

        const pdfDoc = pdfMake.createPdf(docDefinition);

        if (!pdfDoc || typeof pdfDoc.getBuffer !== 'function') {
          throw new Error('Failed to create PDF document');
        }

        pdfDoc.getBuffer((buffer: Buffer) => {
          if (!buffer) {
            reject(new Error('Failed to generate PDF buffer'));
          } else {
            resolve(buffer);
          }
        });
      } catch (error) {
        this.logger.error('PDF generation failed:', error);
        reject(error);
      }
    });
  }

  private async sendReportEmail(
    user: any,
    reportScope: string,
    reportType: string,
    totalOverdue: number,
    pdfBuffer: Buffer,
  ): Promise<void> {
    const subject = `Overdue Targets Report - ${reportType} (${reportScope})`;
    const message = `Hello ${user.name},\n\n${reportScope} currently has ${totalOverdue} overdue ${reportType.toLowerCase()} targets.\n\nPlease find the detailed report attached.\n\nRegards,\nKB Tracker`;

    // Send email with PDF attachment
    await this.emailService.sendEmailWithAttachments(
      user.email,
      subject,
      'generic-email',
      {
        title: subject,
        recipientName: user.name,
        message,
        appName: 'KB Tracker',
        timestamp: new Date().toLocaleString(),
      },
      [
        {
          filename: `overdue-targets-${reportType.toLowerCase().replace(/\s+/g, '-')}-${reportScope.toLowerCase().replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf',
        },
      ],
    );

    this.logger.log(
      `Sent overdue targets report to ${user.email} for ${reportScope} - ${reportType}`,
    );

    // Create notification
    const notificationMessage = `${totalOverdue} overdue ${reportType.toLowerCase()} targets found. Check email for full report.`;

    await this.notificationUtils.createNotification({
      type: NotificationType.CUSTOM,
      title: 'Overdue Targets Report',
      message: notificationMessage,
      recipient_id: user.id,
      priority: 'HIGH' as any,
      data: {
        reportType: 'overdue-targets',
        totalOverdue,
        targetType: reportType,
        reportScope,
      },
    });
  }
}
