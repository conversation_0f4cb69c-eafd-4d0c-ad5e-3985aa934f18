import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';
import { NotificationUtils } from '../../notifications/utils/notification.utils';
import { BackgroundTaskService } from '../background-task.service';

export interface FollowUpReminderPayload {
  followUpId: string;
}

@Injectable()
export class FollowUpReminderHandler implements TaskHandler {
  private readonly logger = new Logger(FollowUpReminderHandler.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly notificationUtils: NotificationUtils,
    private readonly backgroundTaskService: BackgroundTaskService,
  ) {}

  getTaskType(): string {
    return 'follow-up-reminder';
  }

  getDescription(): string {
    return 'Sends reminder notifications 2 hours before follow-up appointments';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payload) {
      errors.push('Payload is required');
      return { isValid: false, errors };
    }

    if (!payload.followUpId || typeof payload.followUpId !== 'string') {
      errors.push('followUpId is required and must be a string');
    }

    return { isValid: errors.length === 0, errors };
  }

  async handle(payload: FollowUpReminderPayload, job: Job): Promise<any> {
    this.logger.log(`Processing follow-up reminder for follow-up ID: ${payload.followUpId}`);

    try {
      await job.updateProgress(10);

      // Get the follow-up with related data
      const followUp = await this.prisma.followUp.findUnique({
        where: { id: payload.followUpId },
        include: {
          lead: {
            select: {
              customer_name: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!followUp) {
        this.logger.warn(`Follow-up with ID ${payload.followUpId} not found`);
        return { success: false, reason: 'Follow-up not found' };
      }

      await job.updateProgress(30);

      // Check if follow-up is still pending (not completed or canceled)
      if (followUp.date_completed || followUp.canceled_at) {
        this.logger.log(`Follow-up ${payload.followUpId} is already completed or canceled`);
        return { success: false, reason: 'Follow-up already completed or canceled' };
      }

      await job.updateProgress(50);

      // Validate timing: Check if we're within ±5 minutes of 2 hours before the follow-up
      const now = new Date();
      const followUpTime = new Date(followUp.for_date);
      const twoHoursBefore = new Date(followUpTime.getTime() - 2 * 60 * 60 * 1000);
      const timeDifference = Math.abs(now.getTime() - twoHoursBefore.getTime());
      const fiveMinutesInMs = 5 * 60 * 1000;

      if (timeDifference > fiveMinutesInMs) {
        // Follow-up was rescheduled, reschedule this reminder task
        this.logger.log(`Follow-up ${payload.followUpId} timing changed. Rescheduling reminder task.`);
        
        const newReminderTime = new Date(followUpTime.getTime() - 2 * 60 * 60 * 1000);
        
        // Schedule new reminder task
        await this.backgroundTaskService.schedule(
          'follow-up-reminder',
          { followUpId: payload.followUpId },
          newReminderTime,
          {
            name: `Follow-up Reminder - ${followUp.lead.customer_name}`,
            description: `Reminder for ${followUp.type} with ${followUp.lead.customer_name}`,
            priority: 8,
          }
        );

        return { 
          success: false, 
          reason: 'Follow-up rescheduled, new reminder task created',
          newReminderTime: newReminderTime.toISOString()
        };
      }

      await job.updateProgress(70);

      // Create the reminder notification
      const notification = await this.notificationUtils.createFollowUpReminderNotification(
        followUp.id,
        followUp.user_id,
        followUp.lead.customer_name || 'Unknown Lead',
        followUp.type,
        followUpTime
      );

      await job.updateProgress(90);

      this.logger.log(`Follow-up reminder notification sent for follow-up ${payload.followUpId}`);

      await job.updateProgress(100);

      return {
        success: true,
        followUpId: payload.followUpId,
        notificationId: notification?.id,
        leadName: followUp.lead.customer_name,
        followUpType: followUp.type,
        scheduledTime: followUpTime.toISOString(),
        userId: followUp.user_id,
      };

    } catch (error) {
      this.logger.error(`Failed to process follow-up reminder for ${payload.followUpId}:`, error);
      throw error;
    }
  }
}
