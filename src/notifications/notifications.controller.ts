import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Query,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { NotificationsService } from './notifications.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { NotificationResponseDto } from './dto/notification-response.dto';
import { NotificationFiltersDto } from './dto/notification-filters.dto';
import { NotificationCountDto } from './dto/notification-count.dto';
import { PaginatedResponseDto } from '../common/dto/pagination.dto';

@ApiTags('Notifications')
@Controller('notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  /**
   * Get notifications for the authenticated user
   */
  @Get()
  @ApiOperation({
    summary: 'Get user notifications',
    description: 'Retrieve paginated notifications for the authenticated user with optional filtering',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Notifications retrieved successfully',
    type: PaginatedResponseDto<NotificationResponseDto>,
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'type', required: false, enum: ['LEAD_ASSIGNED', 'USER_CREATED', 'ACTIVITY_CREATED', 'etc'], description: 'Filter by notification type' })
  @ApiQuery({ name: 'is_read', required: false, type: Boolean, description: 'Filter by read status' })
  @ApiQuery({ name: 'priority', required: false, enum: ['LOW', 'NORMAL', 'HIGH', 'URGENT'], description: 'Filter by priority' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search in title and message' })
  async getNotifications(
    @Request() req: any,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 20,
    @Query() filters: NotificationFiltersDto,
  ): Promise<PaginatedResponseDto<NotificationResponseDto>> {
    return this.notificationsService.findByUser(req.user.id, filters, page, limit);
  }

  /**
   * Get notification count for the authenticated user
   */
  @Get('count')
  @ApiOperation({
    summary: 'Get notification count',
    description: 'Get the count of unread and total notifications for the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Notification count retrieved successfully',
    type: NotificationCountDto,
  })
  async getNotificationCount(@Request() req: any): Promise<NotificationCountDto> {
    return this.notificationsService.getCount(req.user.id);
  }

  /**
   * Mark a specific notification as read
   */
  @Put(':id/read')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Mark notification as read',
    description: 'Mark a specific notification as read for the authenticated user',
  })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Notification marked as read successfully',
    type: NotificationResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Notification not found or access denied',
  })
  async markAsRead(
    @Param('id') notificationId: string,
    @Request() req: any,
  ): Promise<NotificationResponseDto> {
    return this.notificationsService.markAsRead(notificationId, req.user.id);
  }

  /**
   * Mark all notifications as read for the authenticated user
   */
  @Put('read-all')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Mark all notifications as read',
    description: 'Mark all unread notifications as read for the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'All notifications marked as read successfully',
    schema: {
      type: 'object',
      properties: {
        count: { type: 'number', description: 'Number of notifications marked as read' },
      },
    },
  })
  async markAllAsRead(@Request() req: any): Promise<{ count: number }> {
    return this.notificationsService.markAllAsRead(req.user.id);
  }

  /**
   * Delete a specific notification
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete notification',
    description: 'Delete a specific notification for the authenticated user',
  })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Notification deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Notification not found or access denied',
  })
  async deleteNotification(
    @Param('id') notificationId: string,
    @Request() req: any,
  ): Promise<void> {
    return this.notificationsService.delete(notificationId, req.user.id);
  }

  /**
   * Clean up expired notifications (Admin only)
   */
  @Post('cleanup')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Clean up expired notifications',
    description: 'Remove all expired notifications from the system (Admin only)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Expired notifications cleaned up successfully',
    schema: {
      type: 'object',
      properties: {
        count: { type: 'number', description: 'Number of expired notifications removed' },
      },
    },
  })
  async cleanupExpired(): Promise<{ count: number }> {
    return this.notificationsService.cleanupExpired();
  }
}
