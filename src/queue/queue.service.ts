import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

export interface EmailJobData {
  to: string;
  subject: string;
  template: string;
  context: Record<string, any>;
}

export interface ReportJobData {
  userId: string;
  reportType: string;
  filters: Record<string, any>;
  format: 'pdf' | 'excel' | 'csv';
}

export interface DataProcessingJobData {
  type: 'excel-import' | 'bulk-update' | 'data-migration';
  fileUrl?: string;
  data?: any[];
  userId: string;
  metadata?: Record<string, any>;
}

export interface ScheduledTaskJobData {
  taskId: string;
  type: string;
  payload: any;
  attemptNumber: number;
}

export interface NotificationJobData {
  notificationId: string;
  type: 'email' | 'sms' | 'push';
  recipientEmail?: string;
  recipientName?: string;
  recipientPhone?: string;
  notificationType: string;
  title: string;
  message: string;
  data?: Record<string, any>;
}

@Injectable()
export class QueueService {
  constructor(
    @InjectQueue('email') private emailQueue: Queue,
    @InjectQueue('reports') private reportsQueue: Queue,
    @InjectQueue('data-processing') private dataProcessingQueue: Queue,
    @InjectQueue('scheduled-tasks') private scheduledTasksQueue: Queue,
    @InjectQueue('notifications') private notificationsQueue: Queue,
  ) {}

  // Email Queue Methods
  async addEmailJob(data: EmailJobData, options?: any) {
    return this.emailQueue.add('send-email', data, {
      priority: options?.priority || 0,
      delay: options?.delay || 0,
      ...options,
    });
  }

  async addBulkEmailJob(emails: EmailJobData[], options?: any) {
    return this.emailQueue.addBulk(
      emails.map((email, index) => ({
        name: 'send-email',
        data: email,
        opts: {
          priority: options?.priority || 0,
          delay: (options?.delay || 0) + index * 1000, // Stagger emails
          ...options,
        },
      })),
    );
  }

  // Reports Queue Methods
  async addReportJob(data: ReportJobData, options?: any) {
    return this.reportsQueue.add('generate-report', data, {
      priority: options?.priority || 0,
      delay: options?.delay || 0,
      ...options,
    });
  }

  // Data Processing Queue Methods
  async addDataProcessingJob(data: DataProcessingJobData, options?: any) {
    return this.dataProcessingQueue.add('process-data', data, {
      priority: options?.priority || 0,
      delay: options?.delay || 0,
      ...options,
    });
  }

  // Scheduled Tasks Queue Methods
  async addScheduledTaskJob(data: ScheduledTaskJobData, options?: any) {
    return this.scheduledTasksQueue.add('process-scheduled-task', data, {
      priority: options?.priority || 0,
      delay: options?.delay || 0,
      attempts: options?.attempts || 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      ...options,
    });
  }

  async addExcelImportJob(
    fileUrl: string,
    userId: string,
    type: string,
    options?: any,
  ) {
    return this.addDataProcessingJob(
      {
        type: 'excel-import',
        fileUrl,
        userId,
        metadata: { importType: type },
      },
      options,
    );
  }

  // Notification Queue Methods
  async addNotificationJob(data: NotificationJobData, options?: any) {
    return this.notificationsQueue.add('process-notification', data, {
      priority: options?.priority || 0,
      delay: options?.delay || 0,
      attempts: options?.attempts || 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      ...options,
    });
  }

  // Queue Management Methods
  async getQueueStats() {
    const [emailStats, reportsStats, dataStats, scheduledTasksStats, notificationsStats] = await Promise.all([
      this.getQueueInfo(this.emailQueue),
      this.getQueueInfo(this.reportsQueue),
      this.getQueueInfo(this.dataProcessingQueue),
      this.getQueueInfo(this.scheduledTasksQueue),
      this.getQueueInfo(this.notificationsQueue),
    ]);

    return {
      email: emailStats,
      reports: reportsStats,
      dataProcessing: dataStats,
      scheduledTasks: scheduledTasksStats,
      notifications: notificationsStats,
    };
  }

  private async getQueueInfo(queue: Queue) {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
    ]);

    return {
      name: queue.name,
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
    };
  }

  async pauseQueue(queueName: string) {
    const queue = this.getQueueByName(queueName);
    if (queue) {
      await queue.pause();
    }
  }

  async resumeQueue(queueName: string) {
    const queue = this.getQueueByName(queueName);
    if (queue) {
      await queue.resume();
    }
  }

  private getQueueByName(name: string): Queue | null {
    switch (name) {
      case 'email':
        return this.emailQueue;
      case 'reports':
        return this.reportsQueue;
      case 'data-processing':
        return this.dataProcessingQueue;
      case 'scheduled-tasks':
        return this.scheduledTasksQueue;
      case 'notifications':
        return this.notificationsQueue;
      default:
        return null;
    }
  }
}
