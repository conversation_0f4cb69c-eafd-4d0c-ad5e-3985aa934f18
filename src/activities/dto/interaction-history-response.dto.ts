import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for interaction history response
 */
export class InteractionHistoryResponseDto {
  @ApiProperty({
    description: 'Type of interaction (call, visit, etc.)',
    example: 'call',
  })
  interaction_type: string;

  @ApiProperty({
    description: 'Type of activity (First Contact, Follow up, etc.)',
    example: 'First Contact',
  })
  activity_type: string;

  @ApiPropertyOptional({
    description: 'Next follow up date and time',
    example: '2025-07-29T13:05:09.620Z',
  })
  next_followup_date: string | null;

  @ApiPropertyOptional({
    description: 'Notes about the interaction',
    example: 'He was rude but went well',
  })
  notes: string | null;

  @ApiProperty({
    description: 'Name of the user who performed this activity',
    example: '<PERSON>ni',
  })
  performed_by: string;

  @ApiProperty({
    description: 'Date and time when the activity was created',
    example: '2025-07-29T13:05:09.620Z',
  })
  created_at: string;

  @ApiPropertyOptional({
    description: "Session ID from Africa's Talking voice call",
    example: 'ATVoice_12345678-1234-1234-1234-123456789012',
  })
  session_id: string | null;

  @ApiPropertyOptional({
    description: "Recording URL from Africa's Talking voice call",
    example:
      'https://jolly-heisenberg-meninsky.at-internal.com/a8941b2cf1d2235407ec7890ee1e2463.mp3',
  })
  recording_url: string | null;

  @ApiPropertyOptional({
    description: 'Call duration in seconds',
    example: 120,
  })
  call_duration_seconds: number | null;

  @ApiPropertyOptional({
    description: "Africa's Talking call ID",
    example: 'ATCall_12345678-1234-1234-1234-123456789012',
  })
  at_call_id: string | null;

  @ApiPropertyOptional({
    description: 'Call direction (inbound or outbound)',
    example: 'outbound',
  })
  call_direction: string | null;
}
