import { Is<PERSON>rra<PERSON>, IsS<PERSON>, A<PERSON>yNotEmpty, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for assigning permissions to a role
 *
 * This DTO is used for bulk permission assignment operations.
 * It validates that permission IDs are provided and follow the custom ID format.
 *
 * @class AssignPermissionsDto
 */
export class AssignPermissionsDto {
  @ApiProperty({
    description: 'Array of custom permission IDs to assign to the role',
    example: ['users.create', 'users.read', 'reports.generate'],
    type: [String],
  })
  @IsArray({ message: 'Permission IDs must be an array' })
  @ArrayNotEmpty({ message: 'At least one permission ID is required' })
  @IsString({ each: true, message: 'Each permission ID must be a string' })
  @Matches(/^[a-z0-9][a-z0-9]*(\.[a-z0-9][a-z0-9]*)*$/, {
    each: true,
    message:
      'Each permission ID must be lowercase, start with a letter, and use dots for hierarchy (e.g., "users.create")',
  })
  permissionIds: string[];
}

/**
 * Data Transfer Object for removing permissions from a role
 *
 * This DTO is used for bulk permission removal operations.
 * It validates that permission IDs are provided and follow the custom ID format.
 *
 * @class RemovePermissionsDto
 */
export class RemovePermissionsDto {
  @ApiProperty({
    description: 'Array of custom permission IDs to remove from the role',
    example: ['users.create', 'users.read', 'reports.generate'],
    type: [String],
  })
  @IsArray({ message: 'Permission IDs must be an array' })
  @ArrayNotEmpty({ message: 'At least one permission ID is required' })
  @IsString({ each: true, message: 'Each permission ID must be a string' })
  @Matches(/^[a-z0-9][a-z0-9]*(\.[a-z0-9][a-z0-9]*)*$/, {
    each: true,
    message:
      'Each permission ID must be lowercase, start with a letter, and use dots for hierarchy (e.g., "users.create")',
  })
  permissionIds: string[];
}
