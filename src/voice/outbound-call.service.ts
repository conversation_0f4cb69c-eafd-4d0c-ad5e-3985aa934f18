import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

export interface CreateOutboundCallDto {
  sessionId: string;
  clientDialedNumber: string;
}

export interface UpdateOutboundCallDto {
  recordingUrl?: string;
  dialDurationInSeconds?: number;
  lastBridgeHangupCause?: string;
}

@Injectable()
export class OutboundCallService {
  private readonly logger = new Logger(OutboundCallService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new outbound call record
   */
  async create(data: CreateOutboundCallDto) {
    this.logger.log(`Creating outbound call record for session: ${data.sessionId}`);
    
    try {
      const outboundCall = await this.prisma.outboundCall.create({
        data: {
          sessionId: data.sessionId,
          clientDialedNumber: data.clientDialedNumber,
        },
      });

      this.logger.log(`Created outbound call record with ID: ${outboundCall.id}`);
      return outboundCall;
    } catch (error) {
      this.logger.error(`Failed to create outbound call record: ${error.message}`);
      throw error;
    }
  }

  /**
   * Find outbound call by session ID
   */
  async findBySessionId(sessionId: string) {
    this.logger.log(`Finding outbound call by session ID: ${sessionId}`);
    
    try {
      const outboundCall = await this.prisma.outboundCall.findUnique({
        where: { sessionId },
      });

      if (!outboundCall) {
        this.logger.warn(`No outbound call found for session ID: ${sessionId}`);
        return null;
      }

      return outboundCall;
    } catch (error) {
      this.logger.error(`Failed to find outbound call: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update outbound call record
   */
  async update(sessionId: string, data: UpdateOutboundCallDto) {
    this.logger.log(`Updating outbound call for session: ${sessionId}`);
    
    try {
      const outboundCall = await this.prisma.outboundCall.update({
        where: { sessionId },
        data: {
          recordingUrl: data.recordingUrl,
          dialDurationInSeconds: data.dialDurationInSeconds,
          lastBridgeHangupCause: data.lastBridgeHangupCause,
        },
      });

      this.logger.log(`Updated outbound call record with ID: ${outboundCall.id}`);
      return outboundCall;
    } catch (error) {
      if (error.code === 'P2025') {
        this.logger.warn(`No outbound call found to update for session ID: ${sessionId}`);
        throw new NotFoundException(`Outbound call not found for session: ${sessionId}`);
      }
      this.logger.error(`Failed to update outbound call record: ${error.message}`);
      throw error;
    }
  }

  /**
   * Find latest outbound call by client dialed number (comparing last 8 digits)
   */
  async findLatestByPhoneNumber(phoneNumber: string) {
    this.logger.log(`Finding latest outbound call by phone number: ${phoneNumber}`);
    
    // Extract last 8 digits from the phone number
    const last8Digits = phoneNumber.replace(/\D/g, '').slice(-8);
    
    if (last8Digits.length < 8) {
      this.logger.warn(`Phone number ${phoneNumber} has less than 8 digits`);
      return null;
    }

    try {
      // Find outbound calls where the last 8 digits of clientDialedNumber match
      const outboundCalls = await this.prisma.outboundCall.findMany({
        orderBy: { created_at: 'desc' },
      });

      // Filter by last 8 digits comparison
      const matchingCall = outboundCalls.find(call => {
        const callLast8Digits = call.clientDialedNumber.replace(/\D/g, '').slice(-8);
        return callLast8Digits === last8Digits;
      });

      if (matchingCall) {
        this.logger.log(`Found matching outbound call: ${matchingCall.id} for phone: ${phoneNumber}`);
      } else {
        this.logger.log(`No matching outbound call found for phone: ${phoneNumber}`);
      }

      return matchingCall || null;
    } catch (error) {
      this.logger.error(`Failed to find outbound call by phone number: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete outbound call record
   */
  async delete(id: string) {
    this.logger.log(`Deleting outbound call record: ${id}`);
    
    try {
      await this.prisma.outboundCall.delete({
        where: { id },
      });

      this.logger.log(`Deleted outbound call record: ${id}`);
    } catch (error) {
      if (error.code === 'P2025') {
        this.logger.warn(`No outbound call found to delete with ID: ${id}`);
        throw new NotFoundException(`Outbound call not found: ${id}`);
      }
      this.logger.error(`Failed to delete outbound call record: ${error.message}`);
      throw error;
    }
  }

  /**
   * Helper method to check if a phone number is a proper number (starts with +)
   */
  isProperPhoneNumber(phoneNumber: string): boolean {
    if (!phoneNumber) return false;
    return phoneNumber.startsWith('+') && phoneNumber.length > 5;
  }
}
