import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsDateString } from 'class-validator';

export enum CallDirection {
  INBOUND = 'Inbound',
  OUTBOUND = 'Outbound',
}

export enum CallSessionState {
  RINGING = 'Ringing',
  ACTIVE = 'Active',
  DIALING = 'Dialing',
  BRIDGED = 'Bridged',
  COMPLETED = 'Completed',
  DIAL_COMPLETED = 'DialCompleted',
}

export class VoiceCallbackDto {
  @ApiProperty({
    description: 'Current state of the call session',
    example: 'Ringing',
    required: false,
  })
  @IsOptional()
  @IsString()
  callSessionState?: string;

  @ApiProperty({
    description: 'Direction of the call',
    example: 'Inbound',
    required: false,
  })
  @IsOptional()
  @IsString()
  direction?: string;

  @ApiProperty({
    description: 'Country code of the caller',
    example: '-1',
    required: false,
  })
  @IsOptional()
  @IsString()
  callerCountryCode?: string;

  @ApiProperty({
    description: 'Duration of the call in seconds',
    example: '8',
    required: false,
  })
  @IsOptional()
  @IsString()
  durationInSeconds?: string;

  @ApiProperty({
    description: 'Cost amount for the call',
    example: '0.3666666666666666575',
    required: false,
  })
  @IsOptional()
  @IsString()
  amount?: string;

  @ApiProperty({
    description: 'Caller number or identifier',
    example: 'kb_tracker.kbtracker-1757149460516',
    required: false,
  })
  @IsOptional()
  @IsString()
  callerNumber?: string;

  @ApiProperty({
    description: 'Session ID for the call',
    example: 'ATVId_d534417f8ea423cb143dbdb0e5f54ef1',
    required: false,
  })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiProperty({
    description: 'Number dialed by the client',
    example: '+254729165447',
    required: false,
  })
  @IsOptional()
  @IsString()
  clientDialedNumber?: string;

  @ApiProperty({
    description: 'Destination number for the call',
    example: '+254711082660',
    required: false,
  })
  @IsOptional()
  @IsString()
  destinationNumber?: string;

  @ApiProperty({
    description: 'Carrier name of the caller',
    example: 'None',
    required: false,
  })
  @IsOptional()
  @IsString()
  callerCarrierName?: string;

  @ApiProperty({
    description: 'Status of the call',
    example: 'Success',
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({
    description: 'Start time of the call',
    example: '2025-09-06 09:04:24',
    required: false,
  })
  @IsOptional()
  @IsString()
  callStartTime?: string;

  @ApiProperty({
    description: 'Whether the call is active (1) or completed (0)',
    example: '1',
    required: false,
  })
  @IsOptional()
  @IsString()
  isActive?: string;

  @ApiProperty({
    description: 'Currency code for the call cost',
    example: 'KES',
    required: false,
  })
  @IsOptional()
  @IsString()
  currencyCode?: string;

  @ApiProperty({
    description: 'Start time of the dial',
    example: '2025-09-06 09:04:34',
    required: false,
  })
  @IsOptional()
  @IsString()
  dialStartTime?: string;

  @ApiProperty({
    description: 'Duration of the dial in seconds',
    example: '8',
    required: false,
  })
  @IsOptional()
  @IsString()
  dialDurationInSeconds?: string;

  @ApiProperty({
    description: 'Recording URL for the call',
    example:
      'https://jolly-heisenberg-meninsky.at-internal.com/a8941b2cf1d2235407ec7890ee1e2463.mp3',
    required: false,
  })
  @IsOptional()
  @IsString()
  recordingUrl?: string;

  @ApiProperty({
    description: 'Last bridge hangup cause',
    example: 'NO_ANSWER',
    required: false,
  })
  @IsOptional()
  @IsString()
  lastBridgeHangupCause?: string;

  @ApiProperty({
    description: 'Hangup cause for the call',
    example: 'USER_BUSY',
    required: false,
  })
  @IsOptional()
  @IsString()
  hangupCause?: string;

  @ApiProperty({
    description: 'Destination numbers for dialing',
    example: '+254729165447',
    required: false,
  })
  @IsOptional()
  @IsString()
  dialDestinationNumbers?: string;

  @ApiProperty({
    description: 'Single destination number for dialing',
    example: '+254729165447',
    required: false,
  })
  @IsOptional()
  @IsString()
  dialDestinationNumber?: string;
}
