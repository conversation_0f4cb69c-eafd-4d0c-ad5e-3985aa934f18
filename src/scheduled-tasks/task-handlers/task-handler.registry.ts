import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { ConsoleLogHandler } from './console-log.handler';
import { DailyNairobiTaskHandler } from './daily-nairobi-task.handler';
import { TargetCreationHandler } from './target-creation.handler';
import { IndividualTargetProgressHandler } from './individual-target-progress.handler';
import { DailyTargetProgressGenerationHandler } from './daily-target-progress-generation.handler';
import { FollowUpReminderHandler } from './follow-up-reminder.handler';
import { OverdueFollowupsNotificationHandler } from './overdue-followups-notification.handler';
import { OverdueReportsHandler } from './overdue-reports.handler';
import { OverdueTargetsReportsHandler } from './overdue-targets-reports.handler';
import { QueueService } from '../../queue/queue.service';
import { MonthlyConvertedLeadsReportsHandler } from './monthly-converted-leads-reports.handler';
import { WeeklyConvertedLeadsReportsHandler } from './weekly-converted-leads-reports.handler';
import { Overdue2by2by2ReportsHandler } from './overdue-2by2by2-reports.handler';
import { Overdue2by2by2NotificationsHandler } from './overdue-2by2by2-notifications.handler';
import { UpdateActivityCallDataHandler } from './update-activity-call-data.handler';

@Injectable()
export class TaskHandlerRegistry {
  private readonly logger = new Logger(TaskHandlerRegistry.name);
  private readonly handlers = new Map<string, TaskHandler>();

  constructor(
    private readonly queueService: QueueService,
    private readonly consoleLogHandler: ConsoleLogHandler,
    private readonly dailyNairobiTaskHandler: DailyNairobiTaskHandler,
    private readonly targetCreationHandler: TargetCreationHandler,
    private readonly individualTargetProgressHandler: IndividualTargetProgressHandler,
    private readonly dailyTargetProgressGenerationHandler: DailyTargetProgressGenerationHandler,
    private readonly followUpReminderHandler: FollowUpReminderHandler,
    private readonly weeklyConvertedLeadsReportsHandler: WeeklyConvertedLeadsReportsHandler,
    private readonly monthlyConvertedLeadsReportsHandler: MonthlyConvertedLeadsReportsHandler,
    private readonly overdueFollowupsNotificationHandler: OverdueFollowupsNotificationHandler,
    private readonly overdueReportsHandler: OverdueReportsHandler,
    private readonly overdueTargetsReportsHandler: OverdueTargetsReportsHandler,
    private readonly overdue2by2by2ReportsHandler: Overdue2by2by2ReportsHandler,
    private readonly overdue2by2by2NotificationsHandler: Overdue2by2by2NotificationsHandler,
    private readonly updateActivityCallDataHandler: UpdateActivityCallDataHandler,
  ) {
    this.registerHandlers();
  }

  private registerHandlers() {
    // Register all task handlers
    this.registerHandler(this.consoleLogHandler);
    this.registerHandler(this.dailyNairobiTaskHandler);
    this.registerHandler(this.targetCreationHandler);
    this.registerHandler(this.individualTargetProgressHandler);
    this.registerHandler(this.dailyTargetProgressGenerationHandler);
    this.registerHandler(this.followUpReminderHandler);
    this.registerHandler(this.overdueFollowupsNotificationHandler);
    this.registerHandler(this.overdueReportsHandler);
    this.registerHandler(this.overdueTargetsReportsHandler);
    this.registerHandler(this.weeklyConvertedLeadsReportsHandler);
    this.registerHandler(this.monthlyConvertedLeadsReportsHandler);
    this.registerHandler(this.overdue2by2by2ReportsHandler);
    this.registerHandler(this.overdue2by2by2NotificationsHandler);
    this.registerHandler(this.updateActivityCallDataHandler);

    this.logger.log(`Registered ${this.handlers.size} task handlers`);
  }

  private registerHandler(handler: TaskHandler) {
    const taskType = handler.getTaskType();
    this.handlers.set(taskType, handler);
    this.logger.log(`Registered handler for task type: ${taskType}`);
  }

  /**
   * Get a handler for a specific task type
   */
  getHandler(taskType: string): TaskHandler | null {
    return this.handlers.get(taskType) || null;
  }

  /**
   * Get all registered task types
   */
  getRegisteredTaskTypes(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * Get all handlers with their descriptions
   */
  getAllHandlers(): Array<{ type: string; description: string }> {
    return Array.from(this.handlers.entries()).map(([type, handler]) => ({
      type,
      description: handler.getDescription(),
    }));
  }

  /**
   * Validate payload for a specific task type
   */
  validatePayload(
    taskType: string,
    payload: any,
  ): { isValid: boolean; errors: string[] } {
    const handler = this.getHandler(taskType);

    if (!handler) {
      return {
        isValid: false,
        errors: [`No handler found for task type: ${taskType}`],
      };
    }

    return handler.validatePayload(payload);
  }

  /**
   * Execute a task using the appropriate handler
   */
  async executeTask(taskType: string, payload: any, job: Job): Promise<any> {
    const handler = this.getHandler(taskType);

    if (!handler) {
      // Fall back to legacy handlers for built-in types
      return this.executeLegacyTask(taskType, payload, job);
    }

    this.logger.log(`Executing task ${taskType} using registered handler`);
    return handler.handle(payload, job);
  }

  /**
   * Legacy task execution for built-in types (send-email, generate-report, etc.)
   */
  private async executeLegacyTask(
    taskType: string,
    payload: any,
    job: Job,
  ): Promise<any> {
    this.logger.log(`Executing legacy task type: ${taskType}`);

    switch (taskType) {
      case 'send-email':
        return this.processSendEmailTask(payload, job);
      case 'generate-report':
        return this.processGenerateReportTask(payload, job);
      case 'data-processing':
        return this.processDataProcessingTask(payload, job);
      case 'cleanup':
        return this.processCleanupTask(payload, job);
      case 'notification':
        return this.processNotificationTask(payload, job);
      case 'test-failure':
        return this.processTestFailureTask(payload, job);
      default:
        throw new Error(`Unknown task type: ${taskType}`);
    }
  }

  private async processSendEmailTask(payload: any, job: Job): Promise<any> {
    this.logger.log('Processing send email task');

    // Add email job to the email queue
    const emailJob = await this.queueService.addEmailJob({
      to: payload.to,
      subject: payload.subject,
      template: payload.template,
      context: payload.context,
    });

    await job.updateProgress(50);

    return {
      type: 'send-email',
      emailJobId: emailJob.id,
      recipient: payload.to,
      subject: payload.subject,
    };
  }

  private async processGenerateReportTask(
    payload: any,
    job: Job,
  ): Promise<any> {
    this.logger.log('Processing generate report task');

    // Add report job to the reports queue
    const reportJob = await this.queueService.addReportJob({
      userId: payload.userId,
      reportType: payload.reportType,
      filters: payload.filters,
      format: payload.format,
    });

    await job.updateProgress(50);

    return {
      type: 'generate-report',
      reportJobId: reportJob.id,
      reportType: payload.reportType,
      userId: payload.userId,
    };
  }

  private async processDataProcessingTask(
    payload: any,
    job: Job,
  ): Promise<any> {
    this.logger.log('Processing data processing task');

    // Add data processing job to the data-processing queue
    const dataJob = await this.queueService.addDataProcessingJob({
      type: payload.type,
      fileUrl: payload.fileUrl,
      data: payload.data,
      userId: payload.userId,
      metadata: payload.metadata,
    });

    await job.updateProgress(50);

    return {
      type: 'data-processing',
      dataJobId: dataJob.id,
      processingType: payload.type,
      userId: payload.userId,
    };
  }

  private async processCleanupTask(payload: any, job: Job): Promise<any> {
    this.logger.log('Processing cleanup task');

    await job.updateProgress(30);

    const cleanupType = payload.cleanupType;
    let result: any = {};

    switch (cleanupType) {
      case 'old-logs':
        result = { cleanedLogs: 0, message: 'Log cleanup completed' };
        break;
      case 'temp-files':
        result = { cleanedFiles: 0, message: 'Temp file cleanup completed' };
        break;
      case 'expired-tokens':
        result = { cleanedTokens: 0, message: 'Token cleanup completed' };
        break;
      case 'system-maintenance':
        result = {
          cleanedLogs: 10,
          cleanedFiles: 25,
          cleanedTokens: 5,
          message: 'System maintenance completed',
        };
        break;
      default:
        throw new Error(`Unknown cleanup type: ${cleanupType}`);
    }

    await job.updateProgress(80);

    return {
      type: 'cleanup',
      cleanupType,
      result,
    };
  }

  private async processNotificationTask(payload: any, job: Job): Promise<any> {
    this.logger.log('Processing notification task');

    await job.updateProgress(30);

    const notificationType = payload.notificationType;
    let result: any = {};

    switch (notificationType) {
      case 'system-alert':
        result = { sent: true, message: 'System alert sent' };
        break;
      case 'reminder':
        result = { sent: true, message: 'Reminder sent' };
        break;
      case 'status-update':
        result = { sent: true, message: 'Status update sent' };
        break;
      default:
        throw new Error(`Unknown notification type: ${notificationType}`);
    }

    await job.updateProgress(80);

    return {
      type: 'notification',
      notificationType,
      result,
    };
  }

  private async processTestFailureTask(payload: any, job: Job): Promise<any> {
    this.logger.log('Processing test failure task');

    await job.updateProgress(50);

    if (payload.shouldFail) {
      throw new Error('This task is designed to fail for testing purposes');
    }

    return {
      type: 'test-failure',
      message: 'Task completed successfully (shouldFail was false)',
    };
  }
}
