import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';

/**
 * Custom pipe for support ticket ID validation
 * Validates ticket ID format: ST-YYYY-XXXX
 */
@Injectable()
export class TicketIdPipe implements PipeTransform<string, string> {
  transform(value: string): string {
    if (!value) {
      throw new BadRequestException('Ticket ID is required');
    }

    // Validate ticket ID format: ST-YYYY-XXXX
    const ticketIdRegex = /^ST-\d{4}-\d{4}$/;
    if (!ticketIdRegex.test(value)) {
      throw new BadRequestException('Invalid ticket ID format. Expected format: ST-YYYY-XXXX');
    }

    return value;
  }
}
