import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';
import { NotificationUtils } from '../../notifications/utils/notification.utils';

export interface OverdueFollowupsNotificationPayload {
  // Empty payload - uses current date
}

@Injectable()
export class OverdueFollowupsNotificationHandler implements TaskHandler {
  private readonly logger = new Logger(
    OverdueFollowupsNotificationHandler.name,
  );

  constructor(
    private readonly prisma: PrismaService,
    private readonly notificationUtils: NotificationUtils,
  ) {}

  getTaskType(): string {
    return 'overdue-followups-notification';
  }

  getDescription(): string {
    return 'Sends notifications to users about their overdue follow-ups daily at 4pm';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    // No specific validation needed for this payload
    return { isValid: true, errors: [] };
  }

  async handle(
    payload: OverdueFollowupsNotificationPayload,
    job: Job,
  ): Promise<any> {
    this.logger.log('Processing overdue follow-ups notifications');

    try {
      await job.updateProgress(10);

      // Find all overdue follow-ups (date_completed is null and for_date is before current time)
      const now = new Date();

      const overdueFollowUps = await this.prisma.followUp.findMany({
        where: {
          date_completed: null,
          canceled_at: null,
          for_date: {
            lt: now, // Before current time
          },
        },
        include: {
          lead: {
            select: {
              customer_name: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          for_date: 'desc',
        },
      });

      await job.updateProgress(40);

      if (overdueFollowUps.length === 0) {
        this.logger.log('No overdue follow-ups found');
        return { success: true, overdueCount: 0, notificationsSent: 0 };
      }

      this.logger.log(`Found ${overdueFollowUps.length} overdue follow-ups`);

      await job.updateProgress(60);

      // Group overdue follow-ups by user
      const overdueByUser = new Map<string, any[]>();

      for (const followUp of overdueFollowUps) {
        const userId = followUp.user_id;
        if (!overdueByUser.has(userId)) {
          overdueByUser.set(userId, []);
        }
        overdueByUser.get(userId)!.push(followUp);
      }

      await job.updateProgress(80);

      // Send notifications to each user
      let notificationsSent = 0;
      const progressStep = 15 / overdueByUser.size; // Remaining 15% divided by number of users

      for (const [userId, userOverdueFollowUps] of Array.from(
        overdueByUser.entries(),
      )) {
        try {
          // Send individual notifications for each overdue follow-up
          for (const followUp of userOverdueFollowUps) {
            await this.notificationUtils.createOverdueFollowUpNotification(
              userId,
              followUp.lead.customer_name || 'Unknown Lead',
              followUp.type,
              new Date(followUp.for_date),
            );
            notificationsSent++;
          }

          this.logger.log(
            `Sent ${userOverdueFollowUps.length} overdue notifications to user ${userId}`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to send overdue notifications to user ${userId}:`,
            error,
          );
        }

        await job.updateProgress(
          80 + (notificationsSent / overdueFollowUps.length) * 15,
        );
      }

      await job.updateProgress(100);

      this.logger.log(
        `Overdue follow-ups notification task completed. Sent ${notificationsSent} notifications for ${overdueFollowUps.length} overdue follow-ups`,
      );

      return {
        success: true,
        overdueCount: overdueFollowUps.length,
        notificationsSent,
        usersNotified: overdueByUser.size,
        processedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        'Failed to process overdue follow-ups notifications:',
        error,
      );
      throw error;
    }
  }
}
