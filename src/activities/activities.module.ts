import { Module } from '@nestjs/common';
import { ActivitiesController } from './activities.controller';
import { ActivitiesService } from './activities.service';
import { PrismaModule } from '../prisma/prisma.module';
import { CommonModule } from '../common/common.module';
import { TargetsModule } from '../targets/targets.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { ScheduledTaskModule } from '../scheduled-tasks/scheduled-task.module';

/**
 * Module for handling activity-related functionality
 * Provides operations for creating and managing activities
 */
@Module({
  imports: [PrismaModule, CommonModule, TargetsModule, NotificationsModule, ScheduledTaskModule],
  controllers: [ActivitiesController],
  providers: [ActivitiesService],
  exports: [ActivitiesService],
})
export class ActivitiesModule {}
