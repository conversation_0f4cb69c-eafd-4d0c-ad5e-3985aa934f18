import { Injectable, Logger, BadGatewayException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { VoiceXmlService } from './voice-xml.service';
import { InboundCallDto } from './dto/inbound-call.dto';
import axios from 'axios';
import { VoiceCallbackDto } from './dto/voice-callback.dto';
import { VoiceEventDto } from './dto/voice-event.dto';
import {
  VoiceCallbackResponseDto,
  VoiceEventResponseDto,
} from './dto/voice-response.dto';
import { CapabilityTokenResponseDto } from './dto/capability-token.dto';
import { OutboundCallService } from './outbound-call.service';

@Injectable()
export class VoiceService {
  private readonly logger = new Logger(VoiceService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly outboundCallService: OutboundCallService,
    private readonly prisma: PrismaService,
    private readonly voiceXmlService: VoiceXmlService,
  ) {}

  /**
   * Handle voice callback from Africa's Talking
   * This is called when there's an inbound or outbound call
   */
  async handleVoiceCallback(
    callbackData: VoiceCallbackDto,
  ): Promise<VoiceCallbackResponseDto | {}> {
    console.log("callback from Africa's Talking:", callbackData);

    // Check if callerNumber is a proper number (starts with + and has proper format)
    const isProperNumber = this.outboundCallService.isProperPhoneNumber(
      callbackData.callerNumber || '',
    );
    // HANDLES OUTBOUND CALLS
    if (!isProperNumber) {
      // Handle outbound call logic
      await this.handleOutboundCallLogic(callbackData);

      // Generate XML response for Africa's Talking
      const xmlResponse = this.generateVoiceXmlResponse(
        callbackData.clientDialedNumber,
      );

      return {
        xml: xmlResponse,
      };
    }
    // HANDLES INBOUND CALLS
    else {
      this.logger.log(
        'CallerNumber is a proper number, returning empty object',
      );
      // Return empty object for proper phone numbers
      return {};
    }
  }

  /**
   * Handle outbound call logic based on isActive field
   */
  private async handleOutboundCallLogic(
    callbackData: VoiceCallbackDto,
  ): Promise<void> {
    const {
      sessionId,
      clientDialedNumber,
      isActive,
      recordingUrl,
      dialDurationInSeconds,
      lastBridgeHangupCause,
    } = callbackData;

    if (!sessionId) {
      this.logger.warn('No sessionId provided in callback data');
      return;
    }

    if (isActive === '1') {
      this.logger.log('isActive is 1, creating outbound call record');

      if (!clientDialedNumber) {
        this.logger.warn(
          'No clientDialedNumber provided for outbound call creation',
        );
        return;
      }

      try {
        await this.outboundCallService.create({
          sessionId,
          clientDialedNumber,
        });
        this.logger.log('Successfully created outbound call record');
      } catch (error) {
        this.logger.error(
          `Failed to create outbound call record: ${error.message}`,
        );
      }
    } else if (isActive === '0') {
      this.logger.log('isActive is 0, updating outbound call record');

      try {
        // Find and update the outbound call record
        const existingCall =
          await this.outboundCallService.findBySessionId(sessionId);

        if (!existingCall) {
          this.logger.warn(
            `No outbound call found for session ID: ${sessionId}`,
          );
          return;
        }

        // Update with available fields
        const updateData: any = {};
        if (recordingUrl) updateData.recordingUrl = recordingUrl;
        if (dialDurationInSeconds)
          updateData.dialDurationInSeconds = parseInt(dialDurationInSeconds);
        if (lastBridgeHangupCause)
          updateData.lastBridgeHangupCause = lastBridgeHangupCause;

        await this.outboundCallService.update(sessionId, updateData);
        this.logger.log('Successfully updated outbound call record');

        // Check for SMS notification conditions
        if (
          lastBridgeHangupCause &&
          (lastBridgeHangupCause === 'NO_ANSWER' ||
            lastBridgeHangupCause === 'USER_BUSY')
        ) {
          // TODO: Send SMS to user to inform them KB Bank was trying to reach them
          this.logger.log(
            `SMS notification needed for hangup cause: ${lastBridgeHangupCause}`,
          );
          this.logger.log(
            'TODO: Implement SMS notification to inform user that KB Bank was trying to reach them',
          );
        }
      } catch (error) {
        this.logger.error(
          `Failed to update outbound call record: ${error.message}`,
        );
      }
    }
  }

  /**
   * Handle voice events from Africa's Talking
   * This receives call lifecycle events (Ringing, Answered, Completed, Failed)
   */
  async handleVoiceEvent(eventData: any): Promise<any> {
    this.logger.log("Received voice event from Africa's Talking");
    console.log("event from Africa's Talking:", eventData);
    this.logger.log('Event payload:', JSON.stringify(eventData, null, 2));

    // Log the event details
    if (eventData.sessionId) {
      this.logger.log(`Event Session ID: ${eventData.sessionId}`);
    }
    if (eventData.eventType) {
      this.logger.log(`Event Type: ${eventData.eventType}`);
    }
    if (eventData.from) {
      this.logger.log(`From: ${eventData.from}`);
    }
    if (eventData.to) {
      this.logger.log(`To: ${eventData.to}`);
    }
    if (eventData.durationInSeconds) {
      this.logger.log(`Duration: ${eventData.durationInSeconds} seconds`);
    }
    if (eventData.recordingUrl) {
      this.logger.log(`Recording URL: ${eventData.recordingUrl}`);
    }

    // TODO: Here you can add logic to:
    // - Store call records in database
    // - Update call status
    // - Trigger notifications
    // - Update activity records if this is related to lead calls

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      sessionId: eventData.sessionId,
    };
  }

  /**
   * Generate XML response for Africa's Talking voice callback
   * This tells Africa's Talking what to do with the call
   */
  private generateVoiceXmlResponse(clientDialedNumber): string {
    // For now, we'll use a simple dial response with recording enabled
    // and a ringback tone from the sounds folder
    const xmlResponse = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Dial record="true" sequential="true" phoneNumbers="${clientDialedNumber}" ringbackTone="https://5bdad1eb3aa6.ngrok-free.app/uploads/sounds/ringback.wav" />
</Response>`;

    this.logger.log('Generated XML response:', xmlResponse);
    return xmlResponse;
  }

  /**
   * Get WebRTC capability token from Africa's Talking
   * This acts as a proxy to Africa's Talking WebRTC API
   */
  async getCapabilityToken(): Promise<CapabilityTokenResponseDto> {
    this.logger.log("Requesting WebRTC capability token from Africa's Talking");

    // Get credentials from environment variables
    const username = this.configService.get<string>('AFRICASTALKING_USERNAME');
    const apiKey = this.configService.get<string>('AFRICASTALKING_API_KEY');
    const clientPrefix =
      this.configService.get<string>('AFRICASTALKING_CLIENT_PREFIX') ||
      'kb-tracker';

    if (!username || !apiKey) {
      this.logger.error("Africa's Talking credentials not configured");
      throw new BadGatewayException(
        "Africa's Talking credentials not configured",
      );
    }

    // Generate unique client name using prefix and timestamp
    const timestamp = Date.now();
    const clientName = `${clientPrefix}-${timestamp}`;

    this.logger.log(`Generated client name: ${clientName}`);

    try {
      // Make request to Africa's Talking WebRTC capability token API
      const response = await axios.post(
        'https://webrtc.africastalking.com/capability-token/request',
        {
          clientName: clientName,
          phoneNumber: '+254711082660',
          username: username,
          // expire: 17800,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            apiKey: apiKey,
          },
          timeout: 10000, // 10 second timeout
        },
      );

      this.logger.log(
        "Successfully retrieved capability token from Africa's Talkin",
      );
      this.logger.log('Response status:', response.status);

      // Return the response data to the frontend
      return {
        token: response.data.token,
        clientName: clientName,
        lifeTimeSec: response.data.lifeTimeSec,
        outgoing: response.data.outgoing,
        incoming: response.data.incoming,
      };
    } catch (error) {
      this.logger.error(
        "Failed to retrieve capability token from Africa's Talking:",
        error.message,
      );

      if (error.response) {
        this.logger.error('Response status:', error.response.status);
        this.logger.error(
          'Response data:',
          JSON.stringify(error.response.data, null, 2),
        );
      }

      // Return a 502 Bad Gateway error as requested
      throw new BadGatewayException(
        "Failed to retrieve capability token from Africa's Talking",
      );
    }
  }

  /**
   * Handle inbound call and determine routing
   * @param callData - Inbound call data from Africa's Talking
   * @returns XML response for call routing
   */
  async handleInboundCall(callData: InboundCallDto): Promise<string> {
    this.logger.log(
      `Handling inbound call from ${callData.callerNumber} to ${callData.destinationNumber}`,
    );

    try {
      const phoneNumber = callData.callerNumber;

      // Find lead by phone number
      const lead = await this.findLeadByPhone(phoneNumber);

      if (!lead) {
        this.logger.log(
          `No lead found for phone number ${phoneNumber}, routing to default agent`,
        );
        return this.voiceXmlService.generateDefaultResponse();
      }

      this.logger.log(`Found lead ${lead.id} for phone number ${phoneNumber}`);

      // Check for recent missed calls (within last 24 hours)
      const missedCall = await this.findRecentMissedCall(lead.id);

      if (missedCall) {
        this.logger.log(
          `Found recent missed call for lead ${lead.id}, routing to user ${missedCall.performed_by_user_id}`,
        );
        // Route to the user who made the missed call
        const userPhone = await this.getUserPhoneNumber(
          missedCall.performed_by_user_id,
        );
        if (userPhone) {
          return this.voiceXmlService.generateRouteToUserResponse(userPhone);
        }
      }

      // Route to lead's RM user (rm_user_id) if no missed call history
      if (lead.rm_user_id) {
        this.logger.log(
          `Routing to RM user ${lead.rm_user_id} for lead ${lead.id}`,
        );
        const rmUserPhone = await this.getUserPhoneNumber(lead.rm_user_id);
        if (rmUserPhone) {
          return this.voiceXmlService.generateRouteToUserResponse(rmUserPhone);
        } else {
          this.logger.warn(
            `RM user ${lead.rm_user_id} has no phone number, falling back to default agent`,
          );
        }
      }

      // Fallback to assigned user if no RM user
      if (lead.assigned_user) {
        this.logger.log(
          `Routing to assigned user ${lead.assigned_user} for lead ${lead.id}`,
        );
        const assignedUserPhone = await this.getUserPhoneNumber(
          lead.assigned_user,
        );
        if (assignedUserPhone) {
          return this.voiceXmlService.generateRouteToUserResponse(
            assignedUserPhone,
          );
        } else {
          this.logger.warn(
            `Assigned user ${lead.assigned_user} has no phone number, falling back to default agent`,
          );
        }
      }

      this.logger.log(
        `No RM user or assigned user for lead ${lead.id}, routing to default agent`,
      );
      return this.voiceXmlService.generateDefaultResponse();
    } catch (error) {
      this.logger.error(
        `Error handling inbound call: ${error.message}`,
        error.stack,
      );
      return this.voiceXmlService.generateErrorResponse();
    }
  }

  /**
   * Find lead by phone number with normalization
   * @param phoneNumber - Phone number to search for
   * @returns Lead or null
   */
  private async findLeadByPhone(phoneNumber: string) {
    try {
      // Normalize phone number (remove spaces, dashes, etc.)
      const normalizedPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');

      // Search for lead by phone number
      const lead = await this.prisma.lead.findFirst({
        where: {
          phone_number: {
            contains: normalizedPhone,
          },
        },
        include: {
          rm_user: {
            select: {
              id: true,
              name: true,
              phone_number: true,
            },
          },
        },
      });

      return lead;
    } catch (error) {
      this.logger.error(
        `Error finding lead by phone ${phoneNumber}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Find recent missed call for a lead
   * @param leadId - Lead ID to search for
   * @returns Recent missed call activity or null
   */
  private async findRecentMissedCall(leadId: string) {
    try {
      // Find missed calls within the last 24 hours
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

      const missedCall = await this.prisma.activity.findFirst({
        where: {
          lead_id: leadId,
          interaction_type: 'call',
          call_status: 'missed',
          call_direction: 'OUTBOUND', // Look for outbound calls that were missed
          created_at: {
            gte: twentyFourHoursAgo,
          },
        },
        orderBy: {
          created_at: 'desc',
        },
        select: {
          id: true,
          performed_by_user_id: true,
          created_at: true,
        },
      });

      return missedCall;
    } catch (error) {
      this.logger.error(
        `Error finding recent missed call for lead ${leadId}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Get user phone number by user ID
   * @param userId - User ID
   * @returns Phone number or null
   */
  async getUserPhoneNumber(userId: string): Promise<string | null> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { phone_number: true },
      });

      return user?.phone_number || null;
    } catch (error) {
      this.logger.error(
        `Error getting phone number for user ${userId}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Get default agent phone number
   * @returns Default agent phone number
   */
  async getDefaultAgentPhoneNumber(): Promise<string> {
    try {
      // Get default agent phone from environment
      const defaultPhone = process.env.DEFAULT_AGENT_PHONE;
      if (defaultPhone) {
        return defaultPhone;
      }

      // Fallback: get first available user's phone
      const user = await this.prisma.user.findFirst({
        where: {
          phone_number: {
            not: null,
          },
        },
        select: { phone_number: true },
      });

      return user?.phone_number || '+254700000000'; // Ultimate fallback
    } catch (error) {
      this.logger.error(`Error getting default agent phone: ${error.message}`);
      return '+254700000000'; // Ultimate fallback
    }
  }
}
