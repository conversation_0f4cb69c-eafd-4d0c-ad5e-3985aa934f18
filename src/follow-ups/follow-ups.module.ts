import { Module } from '@nestjs/common';
import { FollowUpsController } from './follow-ups.controller';
import { FollowUpsService } from './follow-ups.service';
import { PrismaModule } from '../prisma/prisma.module';
import { CommonModule } from '../common/common.module';
import { TargetsModule } from '../targets/targets.module';
import { ScheduledTaskModule } from '../scheduled-tasks/scheduled-task.module';

/**
 * Follow-ups Module
 *
 * This module handles all follow-up related functionality including:
 * - Retrieving follow-ups based on permissions and date filters
 * - Creating activities from follow-ups
 * - Rescheduling follow-ups
 * - Getting interaction history and lead details
 *
 * @module FollowUpsModule
 */
@Module({
  imports: [PrismaModule, CommonModule, TargetsModule, ScheduledTaskModule],
  controllers: [FollowUpsController],
  providers: [FollowUpsService],
  exports: [FollowUpsService],
})
export class FollowUpsModule {}
