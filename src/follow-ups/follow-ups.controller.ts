import {
  Controller,
  Get,
  Post,
  Patch,
  Param,
  Query,
  Body,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
  Request,
  HttpCode,
  HttpStatus,
  BadRequestException,
  ParseUUIDPipe,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiParam,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { FollowUpsService } from './follow-ups.service';
import {
  FollowUpResponseDto,
  RescheduleFollowUpDto,
  RescheduleResponseDto,
  LeadDetailsResponseDto,
} from './dto/follow-up-response.dto';
import { InteractionHistoryResponseDto } from '../activities/dto/interaction-history-response.dto';

@ApiTags('follow-ups')
@Controller('follow-ups')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class FollowUpsController {
  constructor(private readonly followUpsService: FollowUpsService) {}

  /**
   * Gets follow-ups based on type (today or upcoming)
   * GET /follow-ups?type=today
   * GET /follow-ups?type=upcoming
   */
  @Get()
  @ApiOperation({
    summary: 'Get follow-ups by type',
    description: `
      Retrieves follow-ups based on the specified type:
      - 'today': Returns today's follow-ups and overdue ones (overdue first, sorted by date ASC)
      - 'upcoming': Returns future follow-ups (sorted by closest date)
      
      Permission hierarchy:
      1. followups.all.branches: See all follow-ups
      2. followups.my.region: See follow-ups from user's region
      3. followups.my.branch: See follow-ups from user's branch
      4. Default: See only own follow-ups
    `,
  })
  @ApiQuery({
    name: 'type',
    required: true,
    enum: ['today', 'upcoming', 'completed', 'overdue', 'canceled'],
    description: 'Type of follow-ups to retrieve',
    example: 'today',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Follow-ups retrieved successfully',
    type: [FollowUpResponseDto],
  })
  @ApiBadRequestResponse({
    description:
      'Invalid type parameter. Must be "today", "upcoming", "completed", "overdue", or "canceled"',
  })
  async getFollowUps(
    @Query('type') type: string,
    @Request() req: any,
  ): Promise<FollowUpResponseDto[]> {
    if (
      !['today', 'upcoming', 'completed', 'overdue', 'canceled'].includes(type)
    ) {
      throw new BadRequestException(
        'Type must be "today", "upcoming", "completed", "overdue", or "canceled"',
      );
    }

    return this.followUpsService.getFollowUps(
      type as 'today' | 'upcoming' | 'completed' | 'overdue' | 'canceled',
      req.user.id,
      req.user.permissions || [],
      req.user.branch_id,
    );
  }

  /**
   * Gets follow-up counts by type
   * GET /follow-ups/count
   */
  @Get('count')
  @ApiOperation({
    summary: 'Get follow-up counts',
    description: `
      Returns counts for today, upcoming, and completed follow-ups.
      Counts are filtered based on user permissions:
      1. followups.all.branches: Count all follow-ups
      2. followups.my.branch: Count follow-ups from user's branch
      3. Default: Count only own follow-ups
    `,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Follow-up counts retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        today_count: { type: 'number', example: 5 },
        upcoming_count: { type: 'number', example: 12 },
        completed_count: { type: 'number', example: 8 },
        overdue_count: { type: 'number', example: 3 },
        canceled_count: { type: 'number', example: 2 },
      },
    },
  })
  async getFollowUpCounts(@Request() req: any): Promise<{
    today_count: number;
    upcoming_count: number;
    completed_count: number;
    overdue_count: number;
    canceled_count: number;
  }> {
    return this.followUpsService.getFollowUpCounts(
      req.user.id,
      req.user.permissions || [],
      req.user.branch_id,
    );
  }

  /**
   * Creates an activity (visit or call) from a follow-up
   * POST /follow-ups/:id/make-visit-or-call
   */
  @Post(':id/make-visit-or-call')
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create activity from follow-up',
    description: `
      Creates a new activity (visit or call) from a follow-up and optionally marks it as completed.
      If call_status or visit_status is "Success", the follow-up date_completed is updated to now.
    `,
  })
  @ApiParam({
    name: 'id',
    description: 'Follow-up ID',
    example: 'ba8b3058-baf4-4058-8412-48c8253d0177',
  })
  @ApiBody({
    description: 'Activity data with optional file attachments',
    schema: {
      type: 'object',
      properties: {
        purpose_id: {
          type: 'string',
          example: 'ba8b3058-baf4-4058-8412-48c8253d0177',
          description: 'Purpose ID for the activity',
        },
        notes: {
          type: 'string',
          example: 'Customer was interested in loan products',
          description: 'Optional notes for the activity',
        },
        call_status: {
          type: 'string',
          enum: ['Success', 'Declined'],
          example: 'Success',
          description: 'Optional call status',
        },
        visit_status: {
          type: 'string',
          enum: ['Successful', 'Declined'],
          example: 'Successful',
          description: 'Optional visit status',
        },
        attachments: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'Optional file attachments',
        },
      },
      required: ['purpose_id'],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Activity created successfully from follow-up',
  })
  @ApiNotFoundResponse({
    description: 'Follow-up or purpose not found',
  })
  async makeVisitOrCall(
    @Param('id', ParseUUIDPipe) followUpId: string,
    @Body() dto: any, // Use any for form data
    @UploadedFiles() attachments: Express.Multer.File[] = [],
    @Request() req: any,
  ): Promise<any> {
    return this.followUpsService.makeVisitOrCall(
      followUpId,
      dto,
      attachments,
      req.user.id,
    );
  }

  /**
   * Cancels a follow-up
   * PATCH /follow-ups/:id/cancel
   */
  @Patch(':id/cancel')
  @ApiOperation({
    summary: 'Cancel follow-up',
    description:
      'Marks a follow-up as canceled by setting the canceled_at field to current datetime',
  })
  @ApiParam({
    name: 'id',
    description: 'Follow-up ID',
    example: 'ba8b3058-baf4-4058-8412-48c8253d0177',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Follow-up canceled successfully',
    schema: {
      type: 'object',
      properties: {
        canceled_at: { type: 'string', example: '2025-08-31T14:30:00.000Z' },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Follow-up not found',
  })
  async cancelFollowUp(
    @Param('id', ParseUUIDPipe) followUpId: string,
  ): Promise<{ canceled_at: string }> {
    return this.followUpsService.cancelFollowUp(followUpId);
  }

  /**
   * Reschedules a follow-up
   * PATCH /follow-ups/:id/reschedule
   */
  @Patch(':id/reschedule')
  @ApiOperation({
    summary: 'Reschedule follow-up',
    description: 'Updates the for_date field of a follow-up to a new date',
  })
  @ApiParam({
    name: 'id',
    description: 'Follow-up ID',
    example: 'ba8b3058-baf4-4058-8412-48c8253d0177',
  })
  @ApiBody({
    type: RescheduleFollowUpDto,
    description: 'New date for the follow-up',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Follow-up rescheduled successfully',
    type: RescheduleResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Follow-up not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid date format',
  })
  async rescheduleFollowUp(
    @Param('id', ParseUUIDPipe) followUpId: string,
    @Body() dto: any, // Use any for form data
  ): Promise<RescheduleResponseDto> {
    return this.followUpsService.rescheduleFollowUp(followUpId, dto);
  }

  /**
   * Gets interaction history for a follow-up
   * GET /follow-ups/:id/interaction-history
   */
  @Get(':id/interaction-history')
  @ApiOperation({
    summary: 'Get interaction history for follow-up',
    description:
      'Retrieves all interaction history for the lead associated with this follow-up',
  })
  @ApiParam({
    name: 'id',
    description: 'Follow-up ID',
    example: 'ba8b3058-baf4-4058-8412-48c8253d0177',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Interaction history retrieved successfully',
    type: [InteractionHistoryResponseDto],
  })
  @ApiNotFoundResponse({
    description: 'Follow-up not found',
  })
  async getInteractionHistory(
    @Param('id', ParseUUIDPipe) followUpId: string,
  ): Promise<InteractionHistoryResponseDto[]> {
    return this.followUpsService.getInteractionHistory(followUpId);
  }

  /**
   * Gets lead details for a follow-up
   * GET /follow-ups/:id/lead-details
   */
  @Get(':id/lead-details')
  @ApiOperation({
    summary: 'Get lead details for follow-up',
    description:
      'Retrieves detailed information about the lead associated with this follow-up',
  })
  @ApiParam({
    name: 'id',
    description: 'Follow-up ID',
    example: 'ba8b3058-baf4-4058-8412-48c8253d0177',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lead details retrieved successfully',
    type: LeadDetailsResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Follow-up or lead not found',
  })
  async getLeadDetails(
    @Param('id', ParseUUIDPipe) followUpId: string,
  ): Promise<LeadDetailsResponseDto> {
    return this.followUpsService.getLeadDetails(followUpId);
  }
}
