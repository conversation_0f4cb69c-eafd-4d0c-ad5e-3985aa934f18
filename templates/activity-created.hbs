<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .content {
            background-color: #ffffff;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .highlight {
            background-color: #fff3e0;
            padding: 15px;
            border-left: 4px solid #ff9800;
            margin: 15px 0;
        }
        .footer {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{title}}</h1>
    </div>
    
    <div class="content">
        <p>Hello,</p>
        
        <p>{{message}}</p>
        
        <div class="highlight">
            <h3>Activity Details:</h3>
            <ul>
                <li><strong>Activity Type:</strong> {{data.activityType}}</li>
                <li><strong>Interaction Type:</strong> {{data.interactionType}}</li>
                <li><strong>Customer:</strong> {{data.customerName}}</li>
                <li><strong>Purpose:</strong> {{data.purpose}}</li>
                <li><strong>Performed By:</strong> {{data.performedBy}} ({{data.performedByRmCode}})</li>
                <li><strong>Branch:</strong> {{data.branchName}}</li>
                {{#if data.callStatus}}
                <li><strong>Call Status:</strong> {{data.callStatus}}</li>
                {{/if}}
                {{#if data.visitStatus}}
                <li><strong>Visit Status:</strong> {{data.visitStatus}}</li>
                {{/if}}
                {{#if data.callDurationMinutes}}
                <li><strong>Call Duration:</strong> {{data.callDurationMinutes}} minutes</li>
                {{/if}}
                {{#if data.nextFollowupDate}}
                <li><strong>Next Follow-up:</strong> {{data.nextFollowupDate}}</li>
                {{/if}}
            </ul>
        </div>
        
        {{#if data.notes}}
        <div class="highlight">
            <h3>Notes:</h3>
            <p>{{data.notes}}</p>
        </div>
        {{/if}}
        
        <p>Please log into the KB Tracker system to view the full activity details.</p>
        
        <p>Best regards,<br>
        KB Tracker System</p>
    </div>
    
    <div class="footer">
        <p>This is an automated notification from KB Tracker. Please do not reply to this email.</p>
        <p>Generated on {{timestamp}}</p>
    </div>
</body>
</html>
