import { Injectable, NotFoundException, ConflictException, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { NotificationTriggerService } from '../notifications/notification-trigger.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { AssignRoleDto, TransferBranchDto, UpdateLastLoginDto } from './dto/user-role-assignment.dto';
import { ChangePasswordDto, ResetPasswordDto } from './dto/change-password.dto';
import { PaginationDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { ExcelUploadResponseDto } from '../leads/dto/create-lead-new.dto';
import { Prisma } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { Express } from 'express';

/**
 * Service responsible for user-related operations
 * 
 * This service handles all CRUD operations for users, including:
 * - Creating new users with role and branch validation
 * - Retrieving users with pagination and search
 * - Updating user information
 * - Deleting users with dependency checks
 * - Managing user relationships with roles and branches
 * 
 * @class UsersService
 */
@Injectable()
export class UsersService {
  private readonly saltRounds = 12; // Number of salt rounds for bcrypt

  constructor(
    private readonly prisma: PrismaService,
    private readonly notificationTriggerService: NotificationTriggerService,
  ) {}

  /**
   * Creates a new user with role and branch validation
   *
   * @param createUserDto - Data for creating the user
   * @param addedById - ID of the user who is adding this user (optional)
   * @returns Promise<UserResponseDto> - The created user with related data
   * @throws ConflictException if email already exists
   * @throws NotFoundException if role or branch doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async create(createUserDto: CreateUserDto, addedById: string): Promise<UserResponseDto> {
    try {
      // Check if email already exists
      const existingUser = await this.prisma.user.findFirst({
        where: {
          email: {
            equals: createUserDto.email,
            mode: 'insensitive',
          },
        },
      });

      if (existingUser) {
        throw new ConflictException(`User with email '${createUserDto.email}' already exists`);
      }

      // Validate that role exists
      const role = await this.prisma.role.findUnique({
        where: { id: createUserDto.role_id },
      });

      if (!role) {
        throw new NotFoundException(`Role with ID '${createUserDto.role_id}' not found`);
      }

      // Validate that branch exists
      const branch = await this.prisma.branch.findUnique({
        where: { id: createUserDto.branch_id },
        include: { region: true },
      });

      if (!branch) {
        throw new NotFoundException(`Branch with ID '${createUserDto.branch_id}' not found`);
      }

      // Hash the password before saving
      const hashedPassword = await bcrypt.hash(createUserDto.password, this.saltRounds);

      // Prepare user data with added_by field
      const userData = {
        ...createUserDto,
        password: hashedPassword,
        added_by: addedById, // Set the added_by field to the ID of the user who is adding this user
      };

      // Create the user with hashed password
      const user = await this.prisma.user.create({
        data: userData,
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          added_by_user: true,
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      const response = this.transformToResponseDto(user);

      // Trigger notification for new user creation
      try {
        await this.notificationTriggerService.triggerUserCreated(
          user.id,
          addedById,
        );
      } catch (error) {
        // Log error but don't fail the user creation
        console.error('Failed to trigger user created notification:', error);
      }

      return response;

    } catch (error) {
      if (error instanceof ConflictException || error instanceof NotFoundException) {
        throw error;
      }
      
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error creating user:', error);
      throw new BadRequestException('Failed to create user');
    }
  }

  /**
   * Retrieves a paginated list of users with search functionality and RBAC filtering
   * 
   * @param paginationDto - Pagination and search parameters
   * @param user - Optional user object with permissions for RBAC filtering
   * @returns Promise<PaginatedResponseDto<UserResponseDto>> - Paginated list of users
   * @throws BadRequestException if database operation fails
   */
  async findAll(
    paginationDto: PaginationDto, 
    user?: {
      id: string;
      branch_id: string;
      permissions: string[];
    }
  ): Promise<PaginatedResponseDto<UserResponseDto>> {
    try {
      const { page = 1, limit = 10, search } = paginationDto;
      const skip = (page - 1) * limit;

      // Build where clause for search functionality
      const whereClause: Prisma.UserWhereInput = {
        // Add search filtering if search term is provided
        ...(search && {
          OR: [
            {
              name: {
                contains: search,
                mode: 'insensitive',
              },
            },
            {
              email: {
                contains: search,
                mode: 'insensitive',
              },
            },
            {
              rm_code: {
                contains: search,
                mode: 'insensitive',
              },
            },
            {
              role: {
                name: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            },
            {
              branch: {
                name: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            },
          ],
        }),
      };

      // Apply RBAC filtering based on user permissions
      if (user && user.permissions) {
        const canViewAllUsers = user.permissions.includes('view.all.users');
        const canViewRegionUsers = user.permissions.includes('view.region.users');
        const canViewBranchUsers = user.permissions.includes('view.branch.users');

        if (!canViewAllUsers && !canViewRegionUsers && !canViewBranchUsers) {
          // User has no permission to view users
          throw new BadRequestException('Insufficient permissions to view users');
        }

        if (canViewAllUsers) {
          // User can see all users - no additional filtering needed
        } else if (canViewRegionUsers) {
          // User can see users in their region
          const userBranch = await this.prisma.branch.findUnique({
            where: { id: user.branch_id },
            select: { region_id: true },
          });
          if (!userBranch?.region_id) {
            throw new BadRequestException('User branch has no region configured');
          }
          whereClause.branch = { region_id: userBranch.region_id };
        } else if (canViewBranchUsers) {
          // User can see users in their branch
          whereClause.branch_id = user.branch_id;
        }
      }

      // Execute both count and data queries in parallel for better performance
      const [users, total] = await Promise.all([
        this.prisma.user.findMany({
          where: whereClause,
          skip,
          take: limit,
          include: {
            role: true,
            branch: {
              include: {
                region: true,
              },
            },
            added_by_user: true,
            _count: {
              select: {
                general_activities: true,
                leads: true,
                loan_activities: true,
                scheduled_visits: true,
                targets: true,
              },
            },
          },
          orderBy: {
            name: 'asc',
          },
        }),
        this.prisma.user.count({
          where: whereClause,
        }),
      ]);

      // Transform data to response DTOs
      const data = users.map(user => this.transformToResponseDto(user));

      const totalPages = Math.ceil(total / limit);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      return {
        data,
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasNextPage,
          hasPreviousPage,
        },
      };

    } catch (error) {
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error fetching users:', error);
      throw new BadRequestException('Failed to fetch users');
    }
  }

  /**
   * Retrieves a single user by ID
   * 
   * @param id - User UUID
   * @returns Promise<UserResponseDto> - The user with related data
   * @throws NotFoundException if user doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async findOne(id: string): Promise<UserResponseDto> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          added_by_user: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      return this.transformToResponseDto(user);

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }
      
      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }
      
      console.error('Error fetching user:', error);
      throw new BadRequestException('Failed to fetch user');
    }
  }

  /**
   * Updates an existing user
   *
   * @param id - User UUID
   * @param updateUserDto - Data for updating the user
   * @returns Promise<UserResponseDto> - The updated user with related data
   * @throws NotFoundException if user, role, or branch doesn't exist
   * @throws ConflictException if email already exists for another user
   * @throws BadRequestException if database operation fails
   */
  async update(id: string, updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
    try {
      // Check if user exists
      const existingUser = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!existingUser) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // If email is being updated, check for conflicts
      if (updateUserDto.email && updateUserDto.email !== existingUser.email) {
        const conflictingUser = await this.prisma.user.findFirst({
          where: {
            email: {
              equals: updateUserDto.email,
              mode: 'insensitive',
            },
            id: {
              not: id, // Exclude current user from conflict check
            },
          },
        });

        if (conflictingUser) {
          throw new ConflictException(`User with email '${updateUserDto.email}' already exists`);
        }
      }

      // Validate role if being updated
      if (updateUserDto.role_id) {
        const role = await this.prisma.role.findUnique({
          where: { id: updateUserDto.role_id },
        });

        if (!role) {
          throw new NotFoundException(`Role with ID '${updateUserDto.role_id}' not found`);
        }
      }

      // Validate branch if being updated
      if (updateUserDto.branch_id) {
        const branch = await this.prisma.branch.findUnique({
          where: { id: updateUserDto.branch_id },
        });

        if (!branch) {
          throw new NotFoundException(`Branch with ID '${updateUserDto.branch_id}' not found`);
        }
      }

      // Prepare update data with password hashing if password is being updated
      const updateData = { ...updateUserDto };
      if (updateUserDto.password) {
        updateData.password = await bcrypt.hash(updateUserDto.password, this.saltRounds);
      }

      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: updateData,
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          added_by_user: true,
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      return this.transformToResponseDto(updatedUser);

    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error updating user:', error);
      throw new BadRequestException('Failed to update user');
    }
  }

  /**
   * Deletes a user by ID
   *
   * @param id - User UUID
   * @returns Promise<void>
   * @throws NotFoundException if user doesn't exist
   * @throws ConflictException if user has associated data that prevents deletion
   * @throws BadRequestException if database operation fails
   */
  async remove(id: string): Promise<void> {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id },
        include: {
          added_by_user: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
              hitlist_calls: true,
              hitlist_assignments: true,
              hitlist_uploads: true,
            },
          },
        },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // Check if user has associated data that prevents deletion
      const hasAssociatedData =
        user._count.general_activities > 0 ||
        user._count.leads > 0 ||
        user._count.loan_activities > 0 ||
        user._count.scheduled_visits > 0 ||
        user._count.targets > 0 ||
        user._count.hitlist_calls > 0 ||
        user._count.hitlist_assignments > 0 ||
        user._count.hitlist_uploads > 0;

      if (hasAssociatedData) {
        throw new ConflictException(
          `Cannot delete user '${user.name}' because they have associated data. ` +
          `Please reassign or remove their activities, leads, and other data first.`
        );
      }

      await this.prisma.user.delete({
        where: { id },
      });

    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error deleting user:', error);
      throw new BadRequestException('Failed to delete user');
    }
  }

  /**
   * Assigns a role to a user
   *
   * @param id - User UUID
   * @param assignRoleDto - Role assignment data
   * @returns Promise<UserResponseDto> - The updated user with new role
   * @throws NotFoundException if user or role doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async assignRole(id: string, assignRoleDto: AssignRoleDto): Promise<UserResponseDto> {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // Validate that role exists
      const role = await this.prisma.role.findUnique({
        where: { id: assignRoleDto.role_id },
      });

      if (!role) {
        throw new NotFoundException(`Role with ID '${assignRoleDto.role_id}' not found`);
      }

      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: { role_id: assignRoleDto.role_id },
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          added_by_user: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      return this.transformToResponseDto(updatedUser);

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error assigning role to user:', error);
      throw new BadRequestException('Failed to assign role to user');
    }
  }

  /**
   * Transfers a user to a different branch
   *
   * @param id - User UUID
   * @param transferBranchDto - Branch transfer data
   * @returns Promise<UserResponseDto> - The updated user with new branch
   * @throws NotFoundException if user or branch doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async transferBranch(id: string, transferBranchDto: TransferBranchDto): Promise<UserResponseDto> {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // Validate that branch exists
      const branch = await this.prisma.branch.findUnique({
        where: { id: transferBranchDto.branch_id },
        include: { region: true },
      });

      if (!branch) {
        throw new NotFoundException(`Branch with ID '${transferBranchDto.branch_id}' not found`);
      }

      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: { branch_id: transferBranchDto.branch_id },
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          added_by_user: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      return this.transformToResponseDto(updatedUser);

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error transferring user to branch:', error);
      throw new BadRequestException('Failed to transfer user to branch');
    }
  }

  /**
   * Updates the last login timestamp for a user
   *
   * @param id - User UUID
   * @param updateLastLoginDto - Last login data
   * @returns Promise<void>
   * @throws NotFoundException if user doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async updateLastLogin(id: string, updateLastLoginDto: UpdateLastLoginDto): Promise<void> {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      await this.prisma.user.update({
        where: { id },
        data: { last_login: updateLastLoginDto.last_login },
      });

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error updating user last login:', error);
      throw new BadRequestException('Failed to update user last login');
    }
  }

  /**
   * Changes a user's password after verifying the current password
   *
   * @param id - User UUID
   * @param changePasswordDto - Password change data
   * @returns Promise<void>
   * @throws NotFoundException if user doesn't exist
   * @throws UnauthorizedException if current password is incorrect
   * @throws BadRequestException if database operation fails
   */
  async changePassword(id: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    try {
      // Check if user exists and get current password
      const user = await this.prisma.user.findUnique({
        where: { id },
        select: { id: true, password: true },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // Check if user has a password set
      if (!user.password) {
        throw new UnauthorizedException('User does not have a password set. Please contact an administrator.');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new UnauthorizedException('Current password is incorrect');
      }

      // Hash new password and update
      const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, this.saltRounds);

      await this.prisma.user.update({
        where: { id },
        data: { password: hashedNewPassword },
      });

    } catch (error) {
      if (error instanceof NotFoundException || error instanceof UnauthorizedException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error changing user password:', error);
      throw new BadRequestException('Failed to change password');
    }
  }

  /**
   * Resets a user's password (admin operation, no current password verification)
   *
   * @param id - User UUID
   * @param resetPasswordDto - Password reset data
   * @returns Promise<void>
   * @throws NotFoundException if user doesn't exist
   * @throws BadRequestException if database operation fails
   */
  async resetPassword(id: string, resetPasswordDto: ResetPasswordDto): Promise<void> {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id },
        select: { id: true },
      });

      if (!user) {
        throw new NotFoundException(`User with ID '${id}' not found`);
      }

      // Hash new password and update
      const hashedNewPassword = await bcrypt.hash(resetPasswordDto.newPassword, this.saltRounds);

      await this.prisma.user.update({
        where: { id },
        data: { password: hashedNewPassword },
      });

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error resetting user password:', error);
      throw new BadRequestException('Failed to reset password');
    }
  }

  /**
   * Verifies a user's password (for authentication)
   *
   * @param email - User email
   * @param password - Plain text password
   * @returns Promise<UserResponseDto | null> - User data if password is correct, null otherwise
   * @throws BadRequestException if database operation fails
   */
  async verifyPassword(email: string, password: string): Promise<UserResponseDto | null> {
    try {
      const user = await this.prisma.user.findFirst({
        where: {
          email: {
            equals: email,
            mode: 'insensitive',
          },
        },
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          added_by_user: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          _count: {
            select: {
              general_activities: true,
              leads: true,
              loan_activities: true,
              scheduled_visits: true,
              targets: true,
            },
          },
        },
      });

      if (!user) {
        return null;
      }

      // Check if user has a password set
      if (!user.password) {
        return null;
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return null;
      }

      return this.transformToResponseDto(user);

    } catch (error) {
      // Handle database connection errors
      if (error.code === 'P1001' || error.message?.includes("Can't reach database server")) {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      // Handle other Prisma errors
      if (error.code && error.code.startsWith('P')) {
        throw new BadRequestException(`Database error: ${error.message}`);
      }

      // Handle Prisma initialization errors
      if (error.constructor.name === 'PrismaClientInitializationError') {
        throw new BadRequestException('Database connection failed. Please try again later.');
      }

      console.error('Error verifying user password:', error);
      throw new BadRequestException('Failed to verify password');
    }
  }

  /**
   * Transforms a Prisma user object to UserResponseDto
   *
   * @param user - Prisma user object with includes
   * @returns UserResponseDto - Transformed response object
   */
  private transformToResponseDto(user: any): UserResponseDto {
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      phone_number: user.phone_number,
      rm_code: user.rm_code,
      last_login: user.last_login,
      created_at: user.created_at,
      updated_at: user.updated_at,
      added_by: user.added_by || undefined,
      added_by_user: user.added_by_user ? {
        id: user.added_by_user.id,
        name: user.added_by_user.name,
        email: user.added_by_user.email,
        rm_code: user.added_by_user.rm_code,
      } : undefined,
      role: {
        id: user.role.id,
        name: user.role.name,
        description: user.role.description,
      },
      branch: {
        id: user.branch.id,
        name: user.branch.name,
        region: {
          id: user.branch.region.id,
          name: user.branch.region.name,
        },
      },
      generalActivitiesCount: user._count.general_activities,
      leadsCount: user._count.leads,
      loanActivitiesCount: user._count.loan_activities,
      scheduledVisitsCount: user._count.scheduled_visits,
      targetsCount: user._count.targets,
    };
  }

  /**
   * Exports users to Excel file based on user permissions
   * Creates a comprehensive Excel file with user data and relationships
   * Implements the same RBAC logic as findAll
   * @param search - Optional search term to filter users
   * @param user - Optional user object with permissions for RBAC filtering
   * @returns Buffer containing the Excel file
   */
  async exportUsersToExcel(
    search?: string,
    user?: {
      id: string;
      branch_id: string;
      permissions: string[];
    }
  ): Promise<Buffer> {
    // Build where clause for search
    const whereClause: any = search ? {
      OR: [
        {
          name: {
            contains: search,
            mode: 'insensitive' as const,
          },
        },
        {
          email: {
            contains: search,
            mode: 'insensitive' as const,
          },
        },
        {
          rm_code: {
            contains: search,
            mode: 'insensitive' as const,
          },
        },
        {
          role: {
            name: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
        },
        {
          branch: {
            name: {
              contains: search,
              mode: 'insensitive' as const,
            },
          },
        },
      ],
    } : {};

    // Apply RBAC filtering based on user permissions
    if (user && user.permissions) {
      const canViewAllUsers = user.permissions.includes('view.all.users');
      const canViewRegionUsers = user.permissions.includes('view.region.users');
      const canViewBranchUsers = user.permissions.includes('view.branch.users');

      if (!canViewAllUsers && !canViewRegionUsers && !canViewBranchUsers) {
        // User has no permission to export users
        throw new BadRequestException('Insufficient permissions to export users');
      }

      if (canViewAllUsers) {
        // User can see all users - no additional filtering needed
      } else if (canViewRegionUsers) {
        // User can see users in their region
        const userBranch = await this.prisma.branch.findUnique({
          where: { id: user.branch_id },
          select: { region_id: true },
        });
        if (!userBranch?.region_id) {
          throw new BadRequestException('User branch has no region configured');
        }
        whereClause.branch = { region_id: userBranch.region_id };
      } else if (canViewBranchUsers) {
        // User can see users in their branch
        whereClause.branch_id = user.branch_id;
      }
    }

    // Fetch users with comprehensive data
    const users = await this.prisma.user.findMany({
      where: whereClause,
      include: {
        role: true,
        branch: {
          include: {
            region: true,
          },
        },
        added_by_user: {
          select: {
            id: true,
            name: true,
            email: true,
            rm_code: true,
          },
        },
        _count: {
          select: {
            general_activities: true,
            leads: true,
            loan_activities: true,
            scheduled_visits: true,
            targets: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Users Export');

    // Define headers - only the requested columns
    const headers = [
      'Name',
      'Role',
      'RM Code',
      'Branch',
      'Email',
      'Phone Number',
    ];

    // Add headers to worksheet
    worksheet.addRow(headers);

    // Style headers
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // Add data rows - using array format to match headers order
    users.forEach((user) => {
      worksheet.addRow([
        user.name,                           // Name
        user.role?.name || '',               // Role
        user.rm_code,                        // RM Code
        user.branch?.name || '',             // Branch
        user.email,                          // Email
        user.phone_number || '',             // Phone Number
      ]);
    });

    // Auto-fit columns
    worksheet.columns.forEach((column) => {
      if (column.header) {
        column.width = Math.max(column.header.toString().length + 15, 15);
      }
    });

    // Add summary information at the bottom
    const summaryStartRow = users.length + 3;
    worksheet.getCell(`A${summaryStartRow}`).value = 'Export Summary:';
    worksheet.getCell(`A${summaryStartRow}`).font = { bold: true };
    worksheet.getCell(`A${summaryStartRow + 1}`).value = `Total Users: ${users.length}`;

    // Format date as '12 Aug 2025'
    const exportDate = new Date().toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
    worksheet.getCell(`A${summaryStartRow + 2}`).value = `Export Date: ${exportDate}`;
    worksheet.getCell(`A${summaryStartRow + 3}`).value = `Search Filter: ${search || 'None'}`;

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  /**
   * Creates users from an uploaded Excel file
   * Parses Excel file, maps columns to user fields, and creates users using bulk creation logic
   * @param file - The uploaded Excel file
   */
  async createFromExcel(file: Express.Multer.File): Promise<ExcelUploadResponseDto> {
    try {
      // Parse Excel file
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];

      if (!sheetName) {
        throw new BadRequestException('Excel file contains no sheets');
      }

      const worksheet = workbook.Sheets[sheetName];
      const rawData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
      }) as any[][];

      if (rawData.length === 0) {
        throw new BadRequestException('Excel file is empty');
      }

      // Extract headers and data rows
      const headers = rawData[0] as string[];
      const dataRows = rawData.slice(1) as any[][];

      if (dataRows.length === 0) {
        throw new BadRequestException('Excel file contains no data rows');
      }

      // Map Excel columns to user fields
      const columnMappings = this.mapExcelColumns(headers);

      // Debug: Log headers and mappings
      console.log('Excel headers:', headers);
      console.log('Column mappings:', columnMappings);

      // Convert Excel rows to user objects
      const { users, parseErrors } = await this.parseExcelRows(dataRows, headers, columnMappings);

      // Debug: Log parsing results
      console.log('Parsed users:', users.length);
      console.log('Parse errors:', parseErrors.length);
      if (parseErrors.length > 0) {
        console.log('Parse errors details:', parseErrors);
      }

      if (users.length === 0) {
        const errorMessage = parseErrors.length > 0
          ? `No valid users found. Errors: ${parseErrors.map(e => `Row ${e.index}: ${e.error}`).join('; ')}`
          : 'No valid users found in Excel file. Please check column headers match expected format.';
        throw new BadRequestException(errorMessage);
      }

      // Create users in bulk
      const results = await this.createUsersInBulk(users);

      const totalErrors = results.totalFailed + parseErrors.length;

      return {
        success: results.totalCreated > 0,
        message: `Successfully processed ${dataRows.length} rows from Excel file. Created ${results.totalCreated} users, ${totalErrors} failed.`,
        totalRows: dataRows.length + 1, // +1 for header row
        processedRows: dataRows.length,
        successfulCreations: results.totalCreated,
        failedCreations: totalErrors,
        createdLeads: results.createdUsers, // Using createdLeads property name for compatibility
        errors: [
          ...results.errors.map(err => ({
            row: err.index,
            error: err.error,
            data: err.userData,
          })),
          ...parseErrors.map(err => ({
            row: err.index,
            error: err.error,
            data: err.userData,
          })),
        ],
        columnMappings: columnMappings,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Failed to process Excel file: ${error.message}`);
    }
  }

  /**
   * Maps Excel column headers to user field names
   * @param headers - Array of Excel column headers
   * @returns Object mapping Excel columns to user fields
   */
  private mapExcelColumns(headers: string[]): Record<string, string> {
    const mappings: Record<string, string> = {};

    headers.forEach((header, index) => {
      if (!header || typeof header !== 'string') {
        return; // Skip invalid headers
      }

      const normalizedHeader = header.toLowerCase().trim().replace(/[^a-z0-9]/g, '');

      // More flexible matching patterns
      if (normalizedHeader.includes('name') || normalizedHeader === 'user' || normalizedHeader === 'fullname') {
        mappings[index] = 'name';
      } else if (normalizedHeader.includes('email') || normalizedHeader === 'mail') {
        mappings[index] = 'email';
      } else if (normalizedHeader.includes('phone') || normalizedHeader.includes('mobile') || normalizedHeader.includes('tel')) {
        mappings[index] = 'phone_number';
      } else if (normalizedHeader.includes('password') || normalizedHeader === 'pwd' || normalizedHeader === 'pass') {
        mappings[index] = 'password';
      } else if (normalizedHeader.includes('rmcode') || normalizedHeader.includes('code') || normalizedHeader === 'rm') {
        mappings[index] = 'rm_code';
      } else if (normalizedHeader.includes('role') || normalizedHeader === 'position') {
        mappings[index] = 'role_name';
      } else if (normalizedHeader.includes('branch') || normalizedHeader.includes('office') || normalizedHeader.includes('location')) {
        mappings[index] = 'branch_name';
      }
    });

    return mappings;
  }

  /**
   * Parses Excel rows into user objects
   * @param dataRows - Array of Excel data rows
   * @param headers - Array of Excel headers
   * @param columnMappings - Mapping of column indices to field names
   * @returns Object containing parsed users and errors
   */
  private async parseExcelRows(
    dataRows: any[][],
    headers: string[],
    columnMappings: Record<string, string>,
  ): Promise<{
    users: any[];
    parseErrors: Array<{ index: number; error: string; userData: any }>;
  }> {
    const users: any[] = [];
    const parseErrors: Array<{ index: number; error: string; userData: any }> = [];

    // Load existing roles and branches for matching, plus defaults
    const [existingRoles, existingBranches, defaultRole, defaultBranch] = await Promise.all([
      this.prisma.role.findMany({
        select: { id: true, name: true },
      }),
      this.prisma.branch.findMany({
        select: { id: true, name: true },
      }),
      this.prisma.role.findFirst({
        select: { id: true, name: true },
      }),
      this.prisma.branch.findFirst({
        select: { id: true, name: true },
      }),
    ]);

    for (let i = 0; i < dataRows.length; i++) {
      const row = dataRows[i];
      const userData: any = {};

      try {
        // Map row data to user fields
        Object.entries(columnMappings).forEach(([colIndex, fieldName]) => {
          const cellValue = row[parseInt(colIndex)];
          if (cellValue !== undefined && cellValue !== null && cellValue !== '') {
            userData[fieldName] = cellValue.toString().trim();
          }
        });

        // Validate required fields
        if (!userData.name) {
          throw new Error('Name is required');
        }
        if (!userData.email) {
          throw new Error('Email is required');
        }
        if (!userData.password) {
          throw new Error('Password is required');
        }
        // Generate RM code if not provided
        if (!userData.rm_code) {
          userData.rm_code = await this.generateUniqueRmCode();
        }

        // Find role by name or use default
        let roleId: string | undefined;
        if (userData.role_name) {
          const role = existingRoles.find(r =>
            r.name.toLowerCase() === userData.role_name.toLowerCase()
          );
          if (role) {
            roleId = role.id;
          } else {
            console.warn(`Role '${userData.role_name}' not found, using default role`);
            roleId = defaultRole?.id;
          }
        } else {
          roleId = defaultRole?.id;
        }

        // Find branch by name or use default
        let branchId: string | undefined;
        if (userData.branch_name) {
          const branch = existingBranches.find(b =>
            b.name.toLowerCase() === userData.branch_name.toLowerCase()
          );
          if (branch) {
            branchId = branch.id;
          } else {
            console.warn(`Branch '${userData.branch_name}' not found, using default branch`);
            branchId = defaultBranch?.id;
          }
        } else {
          branchId = defaultBranch?.id;
        }

        // Validate that we have role and branch
        if (!roleId) {
          throw new Error('No role specified and no default role available');
        }
        if (!branchId) {
          throw new Error('No branch specified and no default branch available');
        }

        // Create user object
        const user = {
          name: userData.name,
          email: userData.email.toLowerCase(),
          phone_number: userData.phone_number || null,
          password: userData.password,
          rm_code: userData.rm_code,
          role_id: roleId,
          branch_id: branchId,
        };

        users.push(user);
      } catch (error) {
        parseErrors.push({
          index: i + 2, // +2 because Excel is 1-indexed and we skip header row
          error: error.message,
          userData,
        });
      }
    }

    return { users, parseErrors };
  }

  /**
   * Creates users in bulk with error handling
   * @param users - Array of user objects to create
   * @returns Object containing creation results
   */
  private async createUsersInBulk(users: any[]): Promise<{
    totalCreated: number;
    totalFailed: number;
    createdUsers: UserResponseDto[];
    errors: Array<{ index: number; error: string; userData: any }>;
  }> {
    const results = {
      totalCreated: 0,
      totalFailed: 0,
      createdUsers: [] as UserResponseDto[],
      errors: [] as Array<{ index: number; error: string; userData: any }>,
    };

    // Process users in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < users.length; i += batchSize) {
      const batch = users.slice(i, i + batchSize);

      for (let j = 0; j < batch.length; j++) {
        const user = batch[j];
        const userIndex = i + j;

        try {
          // Hash password before creating user
          const hashedPassword = await bcrypt.hash(user.password, this.saltRounds);

          const createdUser = await this.prisma.user.create({
            data: {
              ...user,
              password: hashedPassword,
            },
            include: {
              role: true,
              branch: {
                include: {
                  region: true,
                },
              },
              added_by_user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  rm_code: true,
                },
              },
              _count: {
                select: {
                  general_activities: true,
                  leads: true,
                  loan_activities: true,
                  scheduled_visits: true,
                  targets: true,
                },
              },
            },
          });

          results.createdUsers.push(this.transformToResponseDto(createdUser));
          results.totalCreated++;
        } catch (error) {
          results.totalFailed++;
          let errorMessage = 'Unknown error occurred';

          if (error.code === 'P2002') {
            if (error.meta?.target?.includes('email')) {
              errorMessage = `Email '${user.email}' already exists`;
            } else if (error.meta?.target?.includes('rm_code')) {
              errorMessage = `RM code '${user.rm_code}' already exists`;
            } else {
              errorMessage = 'Duplicate entry found';
            }
          } else {
            errorMessage = error.message || 'Failed to create user';
          }

          results.errors.push({
            index: userIndex + 2, // +2 for Excel row numbering
            error: errorMessage,
            userData: user,
          });
        }
      }
    }

    return results;
  }

  /**
   * Generates a unique RM code
   * @returns Promise<string> - A unique RM code
   */
  private async generateUniqueRmCode(): Promise<string> {
    let rmCode: string;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 100;

    while (!isUnique && attempts < maxAttempts) {
      // Generate RM code in format RM001, RM002, etc.
      const number = Math.floor(Math.random() * 9999) + 1;
      rmCode = `RM${number.toString().padStart(3, '0')}`;

      // Check if this RM code already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { rm_code: rmCode },
        select: { id: true },
      });

      if (!existingUser) {
        isUnique = true;
        return rmCode;
      }

      attempts++;
    }

    // If we couldn't generate a unique code, use timestamp
    const timestamp = Date.now().toString().slice(-6);
    return `RM${timestamp}`;
  }
}
