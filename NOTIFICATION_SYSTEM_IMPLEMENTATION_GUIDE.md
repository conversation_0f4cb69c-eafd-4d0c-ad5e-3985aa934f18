# Notification System Implementation Guide

## Overview

This document outlines the comprehensive notification system implemented for the KB Tracker backend. The system provides real-time notifications for various events in the application, with support for in-app notifications, email notifications, and a scalable architecture.

## Architecture

### Core Components

1. **Database Schema** - Notification and preference models
2. **Notification Service** - Core business logic for notifications
3. **Queue System** - Asynchronous processing using BullMQ
4. **Notification Handlers** - Event-specific notification logic
5. **Email Templates** - Handlebars templates for email notifications
6. **API Endpoints** - RESTful endpoints for notification management

### System Flow

```
Event Trigger → Notification Handler → Notification Service → Queue → Email/In-App
```

## Database Schema

### Models Added

#### Notification Model
```prisma
model Notification {
  id                String                @id @default(uuid())
  type              NotificationType
  title             String
  message           String
  data              Json?                 // Additional data
  recipient_id      String                // User who receives notification
  sender_id         String?               // User who triggered notification
  entity_type       String?               // Type of related entity
  entity_id         String?               // ID of related entity
  is_read           Boolean               @default(false)
  read_at           DateTime?
  priority          NotificationPriority  @default(NORMAL)
  expires_at        DateTime?             // Optional expiration
  created_at        DateTime              @default(now())
  updated_at        DateTime              @updatedAt

  recipient         User                  @relation("NotificationRecipient")
  sender            User?                 @relation("NotificationSender")
}
```

#### NotificationPreference Model
```prisma
model NotificationPreference {
  id                String                @id @default(uuid())
  user_id           String
  notification_type NotificationType
  enabled           Boolean               @default(true)
  email_enabled     Boolean               @default(true)
  in_app_enabled    Boolean               @default(true)
  created_at        DateTime              @default(now())
  updated_at        DateTime              @updatedAt

  user              User                  @relation(fields: [user_id], references: [id])
}
```

### Enums Added

```prisma
enum NotificationType {
  LEAD_ASSIGNED
  LEAD_UPDATED
  LEAD_CONVERTED
  USER_CREATED
  USER_UPDATED
  ACTIVITY_CREATED
  ACTIVITY_UPDATED
  TARGET_ACHIEVED
  TARGET_MISSED
  FOLLOW_UP_DUE
  FOLLOW_UP_OVERDUE
  SYSTEM_ANNOUNCEMENT
  CUSTOM
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}
```

## API Endpoints

### Notification Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/notifications` | Get user notifications with filtering |
| GET | `/notifications/count` | Get notification count for user |
| PUT | `/notifications/:id/read` | Mark notification as read |
| PUT | `/notifications/read-all` | Mark all notifications as read |
| DELETE | `/notifications/:id` | Delete notification |
| POST | `/notifications/cleanup` | Clean up expired notifications |

### Query Parameters

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20)
- `type` - Filter by notification type
- `is_read` - Filter by read status
- `priority` - Filter by priority
- `search` - Search in title and message
- `created_after` - Filter by creation date
- `created_before` - Filter by creation date

## Notification Types Implemented

### 1. Lead Assignment Notifications
- **Trigger**: When a lead is assigned to a user
- **Recipients**: Assigned user
- **Data**: Lead details, customer info, assigner info

### 2. User Creation Notifications
- **Trigger**: When a new user is created
- **Recipients**: Admin users and user creator
- **Data**: New user details, role, branch info

### 3. Activity Creation Notifications
- **Trigger**: When a new activity is created
- **Recipients**: RM user, assigned user, branch managers
- **Data**: Activity details, customer info, performer info

## Queue System Integration

### Notification Queue
- **Queue Name**: `notifications`
- **Job Type**: `process-notification`
- **Processing**: Email, SMS, Push notifications

### Queue Configuration
```typescript
async addNotificationJob(data: NotificationJobData, options?: any) {
  return this.notificationsQueue.add('process-notification', data, {
    priority: options?.priority || 0,
    delay: options?.delay || 0,
    attempts: options?.attempts || 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
    ...options,
  });
}
```

## Email Templates

### Template Structure
- **Location**: `templates/` directory
- **Format**: Handlebars (.hbs)
- **Templates Created**:
  - `lead-assigned.hbs`
  - `user-created.hbs`
  - `activity-created.hbs`
  - `default-notification.hbs`

### Template Features
- Responsive design
- Branded styling
- Dynamic content
- Conditional sections
- Footer with timestamp

## Notification Handlers

### Handler Interface
```typescript
export interface NotificationHandler {
  readonly type: NotificationType;
  shouldHandle(context: NotificationContext): boolean;
  generateNotification(context: NotificationContext): Promise<{
    title: string;
    message: string;
    priority: NotificationPriority;
    data?: Record<string, any>;
    recipientIds: string[];
  }>;
}
```

### Implemented Handlers
1. **LeadAssignedHandler** - Handles lead assignment notifications
2. **UserCreatedHandler** - Handles user creation notifications
3. **ActivityCreatedHandler** - Handles activity creation notifications

## Integration Points

### Services Modified
1. **LeadsService** - Triggers notifications on lead assignment
2. **UsersService** - Triggers notifications on user creation
3. **ActivitiesService** - Triggers notifications on activity creation

### Modules Updated
1. **LeadsModule** - Imports NotificationsModule
2. **UsersModule** - Imports NotificationsModule
3. **ActivitiesModule** - Imports NotificationsModule
4. **AppModule** - Includes NotificationsModule

## Permissions

### Added Permissions
- `notifications.view` - View notifications
- `notifications.manage` - Manage notifications
- `notifications.send` - Send notifications

## Usage Examples

### Creating a Notification
```typescript
await this.notificationTriggerService.triggerLeadAssigned(
  leadId,
  assignedUserId,
  triggerUserId
);
```

### Getting User Notifications
```typescript
const notifications = await this.notificationsService.findByUser(
  userId,
  { type: NotificationType.LEAD_ASSIGNED, is_read: false },
  1,
  20
);
```

### Marking as Read
```typescript
await this.notificationsService.markAsRead(notificationId, userId);
```

## Frontend Integration

### Notification Counter
The system provides a count endpoint for the bell icon:
```typescript
GET /notifications/count
Response: {
  unread_count: 5,
  total_count: 25,
  by_type: { LEAD_ASSIGNED: 3, USER_CREATED: 1 },
  by_priority: { HIGH: 2, NORMAL: 3 }
}
```

### Real-time Updates
Consider implementing WebSocket connections for real-time notification updates.

## Configuration

### Environment Variables
- `QUEUE_REDIS_HOST` - Redis host for queue
- `QUEUE_REDIS_PORT` - Redis port for queue
- `QUEUE_REDIS_PASSWORD` - Redis password
- `QUEUE_REDIS_DB` - Redis database number

### Email Configuration
- `MAIL_HOST` - SMTP host
- `MAIL_PORT` - SMTP port
- `MAIL_USER` - SMTP username
- `MAIL_PASS` - SMTP password
- `MAIL_FROM` - From email address

## Scalability Features

### Performance Optimizations
1. **Database Indexes** - Optimized for common queries
2. **Queue Processing** - Asynchronous notification delivery
3. **Batch Operations** - Bulk notification creation
4. **Expiration Handling** - Automatic cleanup of expired notifications

### Monitoring
- Queue statistics via `/queue/stats` endpoint
- Notification delivery tracking
- Error logging and handling

## Security Considerations

### Access Control
- User can only access their own notifications
- Admin endpoints require appropriate permissions
- Sensitive data redaction in logs

### Data Privacy
- Notification preferences per user
- Optional email notifications
- Expiration dates for notifications

## Future Enhancements

### Planned Features
1. **Push Notifications** - Mobile app integration
2. **SMS Notifications** - SMS service integration
3. **Notification Scheduling** - Delayed notifications
4. **Rich Notifications** - Action buttons, images
5. **Notification Analytics** - Delivery metrics, engagement

### Extensibility
- Easy addition of new notification types
- Custom notification handlers
- Template customization
- Multi-language support

## Testing

### Test Scenarios
1. **Notification Creation** - Various event types
2. **Email Delivery** - Template rendering and sending
3. **Queue Processing** - Job processing and retry logic
4. **Permission Checks** - Access control validation
5. **Cleanup Operations** - Expired notification removal

## Deployment

### Prerequisites
1. Run database migration: `npx prisma migrate dev`
2. Update environment variables
3. Ensure Redis is running for queue processing
4. Configure SMTP settings for email notifications

### Production Considerations
1. **Redis Clustering** - For high availability
2. **Email Service** - Reliable SMTP provider
3. **Monitoring** - Queue and notification metrics
4. **Backup Strategy** - Notification data backup

## Troubleshooting

### Common Issues
1. **Queue Not Processing** - Check Redis connection
2. **Email Not Sending** - Verify SMTP configuration
3. **Notifications Not Created** - Check handler logic
4. **Permission Errors** - Verify user permissions

### Debugging
- Enable debug logging for notification service
- Check queue statistics for processing issues
- Monitor email delivery logs
- Review notification creation logs

## Conclusion

The notification system provides a robust, scalable solution for real-time user notifications in the KB Tracker application. It follows best practices for:

- **Separation of Concerns** - Clear separation between triggers, handlers, and delivery
- **Scalability** - Queue-based processing and database optimization
- **Maintainability** - Modular design and clear interfaces
- **Extensibility** - Easy addition of new notification types
- **Reliability** - Error handling and retry mechanisms

The system is ready for production use and can be easily extended to support additional notification types and delivery methods as needed.
