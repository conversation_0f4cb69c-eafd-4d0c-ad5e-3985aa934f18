const axios = require('axios');

async function testCapabilityTokenEndpoint() {
  const baseUrl = 'http://localhost:3000/api/v1';
  
  console.log('🧪 Testing WebRTC Capability Token Endpoint...\n');

  try {
    console.log('🔑 Testing GET /get-capability-token');
    const response = await axios.get(`${baseUrl}/get-capability-token`, {
      timeout: 15000, // 15 second timeout
    });
    
    console.log('✅ Capability token response:');
    console.log('Status:', response.status);
    console.log('Content-Type:', response.headers['content-type']);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    console.log('');

    // Validate response structure
    const data = response.data;
    if (data.token && data.clientName) {
      console.log('✅ Response structure is valid');
      console.log(`📱 Client Name: ${data.clientName}`);
      console.log(`🔐 Token Length: ${data.token.length} characters`);
      if (data.expiresAt) {
        const expiryDate = new Date(data.expiresAt * 1000);
        console.log(`⏰ Token Expires: ${expiryDate.toISOString()}`);
      }
    } else {
      console.log('❌ Response structure is invalid - missing required fields');
    }

    console.log('🎉 Capability token endpoint is working correctly!');

  } catch (error) {
    console.error('❌ Error testing capability token endpoint:', error.message);
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 502) {
        console.log('\n💡 This is expected if Africa\'s Talking credentials are not configured.');
        console.log('To fix this, add the following to your .env file:');
        console.log('AFRICASTALKING_USERNAME=your-username');
        console.log('AFRICASTALKING_API_KEY=your-api-key');
        console.log('AFRICASTALKING_CLIENT_PREFIX=kb-tracker');
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server is not running. Please start the server first with: npm run start:dev');
    }
  }
}

// Check if server is running first
async function checkServer() {
  try {
    const response = await axios.get('http://localhost:3000/api/v1/health');
    console.log('✅ Server is running');
    return true;
  } catch (error) {
    console.log('❌ Server is not running. Please start the server first with: npm run start:dev');
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await testCapabilityTokenEndpoint();
  }
}

main();
