import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for follow-up response
 */
export class FollowUpResponseDto {
  @ApiProperty({
    description: 'Follow-up ID',
    example: 'ba8b3058-baf4-4058-8412-48c8253d0177',
  })
  id: string;

  @ApiProperty({
    description: 'Customer name from lead',
    example: 'Ken Kenneth',
  })
  customer_name: string;

  @ApiPropertyOptional({
    description: 'Phone number from lead',
    example: '0712345678',
  })
  phone_number: string | null;

  @ApiProperty({
    description: 'Follow-up date',
    example: '2025-08-31T10:00:00.000Z',
  })
  followup_date: string;

  @ApiPropertyOptional({
    description: 'Date when follow-up was completed',
    example: '2025-08-31T14:30:00.000Z',
  })
  date_completed: string | null;

  @ApiProperty({
    description: 'Type of follow-up based on parent activity interaction type',
    example: 'call',
  })
  type: string;

  @ApiProperty({
    description: 'Status of the follow-up',
    example: 'Medium',
    enum: ['Medium', 'Overdue'],
  })
  status: string;
}

/**
 * Data Transfer Object for creating a visit or call from follow-up
 */
export class MakeVisitOrCallDto {
  @ApiProperty({
    description: 'Purpose ID for the activity',
    example: 'ba8b3058-baf4-4058-8412-48c8253d0177',
  })
  purpose_id: string;

  @ApiPropertyOptional({
    description: 'Notes for the activity',
    example: 'Customer was interested in loan products',
  })
  notes?: string;

  @ApiPropertyOptional({
    description: 'Call status',
    example: 'Success',
    enum: ['Success', 'Declined'],
  })
  call_status?: string;

  @ApiPropertyOptional({
    description: 'Visit status',
    example: 'Successful',
    enum: ['Successful', 'Declined'],
  })
  visit_status?: string;
}

/**
 * Data Transfer Object for rescheduling follow-up
 */
export class RescheduleFollowUpDto {
  @ApiProperty({
    description: 'New date for the follow-up',
    example: '2025-09-15T10:00:00.000Z',
  })
  new_date: string;
}

/**
 * Data Transfer Object for reschedule response
 */
export class RescheduleResponseDto {
  @ApiProperty({
    description: 'Updated follow-up date',
    example: '2025-09-15T10:00:00.000Z',
  })
  for_date: string;
}

/**
 * Data Transfer Object for lead details response
 */
export class LeadDetailsResponseDto {
  @ApiProperty({
    description: 'Lead ID',
    example: 'ba8b3058-baf4-4058-8412-48c8253d0177',
  })
  id: string;

  @ApiProperty({
    description: 'Customer name',
    example: 'Ken Kenneth',
  })
  customer_name: string;

  @ApiProperty({
    description: 'Lead status',
    example: 'Warm',
  })
  lead_status: string;

  @ApiProperty({
    description: 'Phone number',
    example: '+************',
  })
  phone_number: string;

  @ApiProperty({
    description: 'Type of lead',
    example: 'New',
  })
  type_of_lead: string;

  @ApiPropertyOptional({
    description: 'Account number',
    example: null,
  })
  account_number: string | null;

  @ApiPropertyOptional({
    description: 'Account number assigned date',
    example: null,
  })
  account_number_assigned_at: string | null;

  @ApiProperty({
    description: 'Customer category',
    type: 'object',
    properties: {
      id: { type: 'string', example: 'e151432d-0b70-4ef2-92da-096da18d2c85' },
      name: { type: 'string', example: 'Employed' },
    },
  })
  customer_category: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'ISIC sector',
    type: 'object',
    properties: {
      id: { type: 'string', example: 'b47d038d-3e2d-4b8b-af96-47e4e1b3bac1' },
      name: { type: 'string', example: 'Information Technology' },
    },
  })
  isic_sector: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Branch information',
    type: 'object',
    properties: {
      id: { type: 'string', example: '16d7f77e-d048-4162-8415-45957f177df1' },
      name: { type: 'string', example: ' Kikuyu Branch' },
      region: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            example: '3d3ad6c9-0492-42c8-a312-baa01b6df03c',
          },
          name: { type: 'string', example: 'Central Region' },
        },
      },
    },
  })
  branch: {
    id: string;
    name: string;
    region: {
      id: string;
      name: string;
    };
  };

  @ApiProperty({
    description: 'RM user information',
    type: 'object',
    properties: {
      id: { type: 'string', example: 'e86a0350-11c1-4882-b461-83a209d7e277' },
      name: { type: 'string', example: 'Samuel Maiko' },
      email: { type: 'string', example: '<EMAIL>' },
      rm_code: { type: 'string', example: 'RM8660' },
    },
  })
  rm_user: {
    id: string;
    name: string;
    email: string;
    rm_code: string;
  };

  @ApiProperty({
    description: 'Employer information',
    type: 'object',
    properties: {
      id: { type: 'string', example: 'adedaeae-a7b9-42b9-9ed0-c5d671950cca' },
      name: { type: 'string', example: 'ABC Corporation' },
    },
  })
  employer: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Anchor information',
    type: 'object',
    properties: {
      id: { type: 'string', example: 'f64d8676-a981-4b22-8ca0-b80ba609989a' },
      name: { type: 'string', example: 'ABC Corporation' },
      email: { type: 'string', example: '<EMAIL>' },
      phone_number: { type: 'string', example: '0712345678' },
    },
  })
  anchor: {
    id: string;
    name: string;
    email: string;
    phone_number: string;
  };

  @ApiProperty({
    description: 'Contact persons',
    type: 'array',
    items: { type: 'object' },
    example: [],
  })
  contact_persons: any[];

  @ApiProperty({
    description: 'Creation date',
    example: '2025-08-30T23:38:57.452Z',
  })
  created_at: string;
}
