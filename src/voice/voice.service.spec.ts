import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { BadGatewayException } from '@nestjs/common';
import { VoiceService } from './voice.service';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;
import { VoiceCallbackDto, CallDirection } from './dto/voice-callback.dto';
import { VoiceEventDto, VoiceEventType } from './dto/voice-event.dto';

describe('VoiceService', () => {
  let service: VoiceService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VoiceService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<VoiceService>(VoiceService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('handleVoiceCallback', () => {
    it('should process inbound call callback and return XML response', async () => {
      const callbackData: VoiceCallbackDto = {
        sessionId: 'ATVoice_12345678-1234-1234-1234-123456789012',
        callerNumber: 'kb_tracker.kbtracker-1757149460516',
        clientDialedNumber: '+254700000001',
        direction: CallDirection.INBOUND,
        callSessionState: 'Ringing',
        callStartTime: '2025-01-15T10:30:00.000Z',
      };

      const result = await service.handleVoiceCallback(callbackData);

      expect(result.xml).toContain('<?xml version="1.0" encoding="UTF-8"?>');
      expect(result.xml).toContain('<Response>');
      expect(result.xml).toContain('<Dial');
      expect(result.xml).toContain('record="true"');
      expect(result.xml).toContain('ringbackTone="ringback.wav"');
      expect(result.xml).toContain('</Response>');
    });

    it('should process outbound call callback and return XML response', async () => {
      const callbackData: VoiceCallbackDto = {
        sessionId: 'ATVoice_87654321-4321-4321-4321-210987654321',
        from: '+254700000001',
        to: '+254700000000',
        direction: CallDirection.OUTBOUND,
        callStatus: CallStatus.RINGING,
        startTime: '2025-01-15T10:30:00.000Z',
      };

      const result = await service.handleVoiceCallback(callbackData);

      expect(result.xml).toContain('<?xml version="1.0" encoding="UTF-8"?>');
      expect(result.xml).toContain('<Response>');
      expect(result.xml).toContain('<Dial');
      expect(result.xml).toContain('record="true"');
      expect(result.xml).toContain('</Response>');
    });

    it('should handle callback with minimal data', async () => {
      const callbackData: VoiceCallbackDto = {
        sessionId: 'ATVoice_minimal-data',
      };

      const result = await service.handleVoiceCallback(callbackData);

      expect(result.xml).toContain('<?xml version="1.0" encoding="UTF-8"?>');
      expect(result.xml).toContain('<Response>');
      expect(result.xml).toContain('</Response>');
    });
  });

  describe('handleVoiceEvent', () => {
    it('should process ringing event', async () => {
      const eventData: VoiceEventDto = {
        sessionId: 'ATVoice_12345678-1234-1234-1234-123456789012',
        eventType: VoiceEventType.RINGING,
        from: '+254700000000',
        to: '+254700000001',
        timestamp: '2025-01-15T10:30:00.000Z',
      };

      const result = await service.handleVoiceEvent(eventData);

      expect(result.status).toBe('ok');
      expect(result.timestamp).toBeDefined();
      expect(result.sessionId).toBe(eventData.sessionId);
      expect(new Date(result.timestamp)).toBeInstanceOf(Date);
    });

    it('should process answered event', async () => {
      const eventData: VoiceEventDto = {
        sessionId: 'ATVoice_12345678-1234-1234-1234-123456789012',
        eventType: VoiceEventType.ANSWERED,
        from: '+254700000000',
        to: '+254700000001',
        timestamp: '2025-01-15T10:30:15.000Z',
      };

      const result = await service.handleVoiceEvent(eventData);

      expect(result.status).toBe('ok');
      expect(result.timestamp).toBeDefined();
      expect(result.sessionId).toBe(eventData.sessionId);
    });

    it('should process completed event with duration and recording', async () => {
      const eventData: VoiceEventDto = {
        sessionId: 'ATVoice_12345678-1234-1234-1234-123456789012',
        eventType: VoiceEventType.COMPLETED,
        from: '+254700000000',
        to: '+254700000001',
        durationInSeconds: 120,
        recordingUrl: 'https://voice.africastalking.com/recordings/12345.mp3',
        timestamp: '2025-01-15T10:32:00.000Z',
      };

      const result = await service.handleVoiceEvent(eventData);

      expect(result.status).toBe('ok');
      expect(result.timestamp).toBeDefined();
      expect(result.sessionId).toBe(eventData.sessionId);
    });

    it('should process failed event', async () => {
      const eventData: VoiceEventDto = {
        sessionId: 'ATVoice_12345678-1234-1234-1234-123456789012',
        eventType: VoiceEventType.FAILED,
        from: '+254700000000',
        to: '+254700000001',
        timestamp: '2025-01-15T10:30:05.000Z',
        eventData: { hangupCause: 'NO_ANSWER' },
      };

      const result = await service.handleVoiceEvent(eventData);

      expect(result.status).toBe('ok');
      expect(result.timestamp).toBeDefined();
      expect(result.sessionId).toBe(eventData.sessionId);
    });

    it('should handle event with minimal data', async () => {
      const eventData: VoiceEventDto = {
        sessionId: 'ATVoice_minimal-event',
      };

      const result = await service.handleVoiceEvent(eventData);

      expect(result.status).toBe('ok');
      expect(result.timestamp).toBeDefined();
      expect(result.sessionId).toBe(eventData.sessionId);
    });
  });

  describe('getCapabilityToken', () => {
    beforeEach(() => {
      mockConfigService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'AFRICASTALKING_USERNAME':
            return 'test-username';
          case 'AFRICASTALKING_API_KEY':
            return 'test-api-key';
          case 'AFRICASTALKING_CLIENT_PREFIX':
            return 'kb-tracker';
          default:
            return undefined;
        }
      });
    });

    it('should successfully retrieve capability token', async () => {
      const mockResponse = {
        status: 200,
        data: {
          token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          expiresAt: 1704071400,
          ttl: 3600,
        },
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      const result = await service.getCapabilityToken();

      expect(result.token).toBe(mockResponse.data.token);
      expect(result.clientName).toMatch(/^kb-tracker-\d+$/);
      expect(result.expiresAt).toBe(mockResponse.data.expiresAt);
      expect(result.additionalData).toEqual(mockResponse.data);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://webrtc.africastalking.com/capability-token/request',
        expect.objectContaining({
          clientName: expect.stringMatching(/^kb-tracker-\d+$/),
        }),
        expect.objectContaining({
          headers: {
            'Content-Type': 'application/json',
            apiKey: 'test-api-key',
            username: 'test-username',
          },
          timeout: 10000,
        }),
      );
    });

    it('should throw BadGatewayException when credentials are missing', async () => {
      mockConfigService.get.mockImplementation((key: string) => {
        if (key === 'AFRICASTALKING_USERNAME') return undefined;
        if (key === 'AFRICASTALKING_API_KEY') return 'test-api-key';
        if (key === 'AFRICASTALKING_CLIENT_PREFIX') return 'kb-tracker';
        return undefined;
      });

      await expect(service.getCapabilityToken()).rejects.toThrow(
        BadGatewayException,
      );
      await expect(service.getCapabilityToken()).rejects.toThrow(
        "Africa's Talking credentials not configured",
      );
    });

    it('should throw BadGatewayException when API request fails', async () => {
      const mockError = {
        message: 'Network Error',
        response: {
          status: 401,
          data: { error: 'Unauthorized' },
        },
      };

      mockedAxios.post.mockRejectedValue(mockError);

      await expect(service.getCapabilityToken()).rejects.toThrow(
        BadGatewayException,
      );
      await expect(service.getCapabilityToken()).rejects.toThrow(
        "Failed to retrieve capability token from Africa's Talking",
      );
    });

    it('should use default client prefix when not configured', async () => {
      mockConfigService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'AFRICASTALKING_USERNAME':
            return 'test-username';
          case 'AFRICASTALKING_API_KEY':
            return 'test-api-key';
          case 'AFRICASTALKING_CLIENT_PREFIX':
            return undefined; // Not configured
          default:
            return undefined;
        }
      });

      const mockResponse = {
        status: 200,
        data: {
          token: 'test-token',
          expiresAt: 1704071400,
        },
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      const result = await service.getCapabilityToken();

      expect(result.clientName).toMatch(/^kb-tracker-\d+$/);
    });
  });
});
