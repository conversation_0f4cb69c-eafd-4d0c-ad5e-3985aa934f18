import { Controller, Get, HttpCode, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { VoiceService } from './voice.service';
import {
  CapabilityTokenResponseDto,
  CapabilityTokenErrorDto,
} from './dto/capability-token.dto';

/**
 * Controller handling WebRTC capability token endpoint
 * Provides proxy endpoint for Africa's Talking WebRTC API
 */
@ApiTags('WebRTC')
@Controller()
export class WebRTCController {
  private readonly logger = new Logger(WebRTCController.name);

  constructor(private readonly voiceService: VoiceService) {}

  /**
   * Get WebRTC capability token from Africa's Talking
   * GET /get-capability-token
   *
   * This endpoint acts as a backend proxy to Africa's Talking WebRTC capability token API.
   * It reads credentials from environment variables and returns the token to the frontend.
   */
  @Get('get-capability-token')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "Get WebRTC capability token from Africa's Talking",
    description:
      "Backend proxy endpoint that retrieves a WebRTC capability token from Africa's Talking. The backend reads credentials from environment variables, constructs a unique client name, and makes the request to Africa's Talking on behalf of the frontend.",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Capability token retrieved successfully',
    type: CapabilityTokenResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_GATEWAY,
    description: "Failed to retrieve capability token from Africa's Talking",
    type: CapabilityTokenErrorDto,
  })
  async getCapabilityToken(): Promise<CapabilityTokenResponseDto> {
    this.logger.log('Received request for WebRTC capability token');

    try {
      const tokenData = await this.voiceService.getCapabilityToken();
      this.logger.log('Successfully retrieved and returned capability token');
      return tokenData;
    } catch (error) {
      console.log('here is the error:', error);
      this.logger.error('Error retrieving capability token:', error.message);
      throw error; // Re-throw the BadGatewayException from the service
    }
  }
}
