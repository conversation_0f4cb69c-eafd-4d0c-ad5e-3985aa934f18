import { Module } from '@nestjs/common';
import { CustomerServiceHitlistController } from './customer-service-hitlist.controller';
import { CustomerServiceHitlistService } from './customer-service-hitlist.service';
import { PrismaModule } from '../prisma/prisma.module';
import { TargetsModule } from '../targets/targets.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { ScheduledTaskModule } from '../scheduled-tasks/scheduled-task.module';

@Module({
  imports: [
    PrismaModule,
    TargetsModule,
    NotificationsModule,
    ScheduledTaskModule,
  ],
  controllers: [CustomerServiceHitlistController],
  providers: [CustomerServiceHitlistService],
  exports: [CustomerServiceHitlistService],
})
export class CustomerServiceHitlistModule {}
