const { PrismaClient } = require('@prisma/client');
const { startOfMonth, endOfMonth } = require('date-fns');

const prisma = new PrismaClient();

async function testHandlerDirectly() {
  try {
    console.log('=== TESTING HANDLER LOGIC DIRECTLY ===');
    
    const today = new Date();
    const monthStart = startOfMonth(today);
    const monthEnd = endOfMonth(today);
    
    console.log(`Month period: ${monthStart.toDateString()} to ${monthEnd.toDateString()}`);
    
    // Get converted leads
    const convertedLeads = await prisma.lead.findMany({
      where: {
        account_number: { not: null },
        account_number_assigned_at: {
          gte: monthStart,
          lte: monthEnd,
        },
      },
      include: {
        branch: {
          include: {
            region: true,
          },
        },
        account_number_assigned_by_user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: [{ branch: { name: 'asc' } }, { account_number_assigned_at: 'desc' }],
    });
    
    console.log(`\nFound ${convertedLeads.length} converted leads:`);
    convertedLeads.forEach(lead => {
      console.log(`  - ${lead.customer_name} (${lead.account_number}) - Branch: ${lead.branch?.name} (${lead.branch?.id}) - Region: ${lead.branch?.region?.name} (${lead.branch?.region?.id})`);
    });
    
    // Get new leads
    const newLeads = await prisma.lead.findMany({
      where: {
        created_at: {
          gte: monthStart,
          lte: monthEnd,
        },
      },
      include: {
        branch: {
          include: {
            region: true,
          },
        },
        rm_user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: [{ branch: { name: 'asc' } }, { created_at: 'desc' }],
    });
    
    console.log(`\nFound ${newLeads.length} new leads:`);
    newLeads.forEach(lead => {
      console.log(`  - ${lead.customer_name} - Branch: ${lead.branch?.name} (${lead.branch?.id}) - Region: ${lead.branch?.region?.name} (${lead.branch?.region?.id})`);
    });
    
    // Get users with permissions
    const usersWithPermissions = await prisma.user.findMany({
      where: {
        role: {
          role_permissions: {
            some: {
              permission: {
                id: {
                  in: [
                    'reports.converted.leads.all.monthly',
                    'reports.converted.leads.region.monthly',
                    'reports.converted.leads.branch.monthly',
                  ],
                },
              },
            },
          },
        },
      },
      include: {
        branch: {
          include: {
            region: true,
          },
        },
        role: {
          include: {
            role_permissions: {
              include: {
                permission: {
                  select: {
                    id: true,
                  },
                },
              },
            },
          },
        },
      },
    });
    
    console.log(`\nFound ${usersWithPermissions.length} users with permissions:`);
    
    // Process each user
    for (const user of usersWithPermissions) {
      const userPermissions = user.role.role_permissions.map(rp => rp.permission.id);
      
      let reportScope = '';
      let filteredConvertedLeads = [];
      let filteredNewLeads = [];
      
      console.log(`\n=== PROCESSING USER: ${user.name} (${user.email}) ===`);
      console.log(`Branch: ${user.branch?.name} (${user.branch?.id})`);
      console.log(`Region: ${user.branch?.region?.name} (${user.branch?.region?.id})`);
      console.log(`Permissions: ${userPermissions.filter(p => p.includes('converted.leads')).join(', ')}`);
      
      // Apply permission hierarchy: all > region > branch
      if (userPermissions.includes('reports.converted.leads.all.monthly')) {
        reportScope = 'All Branches';
        filteredConvertedLeads = convertedLeads;
        filteredNewLeads = newLeads;
        console.log(`✅ Using ALL permission - scope: ${reportScope}`);
      } else if (userPermissions.includes('reports.converted.leads.region.monthly')) {
        reportScope = `${user.branch.region.name}`;
        filteredConvertedLeads = convertedLeads.filter(
          (lead) => lead.branch.region.id === user.branch.region.id,
        );
        filteredNewLeads = newLeads.filter(
          (lead) => lead.branch.region.id === user.branch.region.id,
        );
        console.log(`✅ Using REGION permission - scope: ${reportScope}`);
      } else if (userPermissions.includes('reports.converted.leads.branch.monthly')) {
        reportScope = `${user.branch.name}`;
        filteredConvertedLeads = convertedLeads.filter(
          (lead) => lead.branch.id === user.branch.id,
        );
        filteredNewLeads = newLeads.filter(
          (lead) => lead.branch.id === user.branch.id,
        );
        console.log(`✅ Using BRANCH permission - scope: ${reportScope}`);
      }
      
      console.log(`Filtered Converted Leads: ${filteredConvertedLeads.length}`);
      console.log(`Filtered New Leads: ${filteredNewLeads.length}`);
      
      if (filteredConvertedLeads.length > 0) {
        console.log(`Converted Leads Details:`);
        filteredConvertedLeads.forEach(lead => {
          console.log(`  - ${lead.customer_name} (${lead.account_number}) - Branch: ${lead.branch?.name} (${lead.branch?.id})`);
        });
      }
      
      if (filteredNewLeads.length > 0) {
        console.log(`New Leads Details:`);
        filteredNewLeads.forEach(lead => {
          console.log(`  - ${lead.customer_name} - Branch: ${lead.branch?.name} (${lead.branch?.id})`);
        });
      }
      
      if (filteredConvertedLeads.length > 0 || filteredNewLeads.length > 0) {
        console.log(`✅ WOULD SEND REPORT to ${user.name} for ${reportScope}`);
      } else {
        console.log(`⚠️  NO DATA - would not send report to ${user.name}`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testHandlerDirectly();
