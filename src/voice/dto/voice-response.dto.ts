import { ApiProperty } from '@nestjs/swagger';

export class VoiceCallbackResponseDto {
  @ApiProperty({
    description: 'XML response for Africa\'s Talking voice callback',
    example: '<?xml version="1.0" encoding="UTF-8"?><Response><Dial record="true" sequential="true" phoneNumbers="" ringbackTone="" /></Response>',
  })
  xml: string;
}

export class VoiceEventResponseDto {
  @ApiProperty({
    description: 'Status of the event processing',
    example: 'ok',
  })
  status: string;

  @ApiProperty({
    description: 'Timestamp when the event was processed',
    example: '2025-01-15T10:30:00.000Z',
  })
  timestamp: string;

  @ApiProperty({
    description: 'Session ID that was processed',
    example: 'ATVoice_12345678-1234-1234-1234-123456789012',
  })
  sessionId?: string;
}
