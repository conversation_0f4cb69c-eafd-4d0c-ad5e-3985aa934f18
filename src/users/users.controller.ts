import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Res,
  Header,
  UseGuards,
  Request,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express, Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiUnauthorizedResponse,
  ApiParam,
  ApiQuery,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import {
  AssignRoleDto,
  TransferBranchDto,
  UpdateLastLoginDto,
} from './dto/user-role-assignment.dto';
import { ChangePasswordDto, ResetPasswordDto } from './dto/change-password.dto';
import {
  PaginationDto,
  PaginatedResponseDto,
  UserPaginationDto,
} from '../common/dto/pagination.dto';
import { ExcelUploadResponseDto } from '../leads/dto/create-lead-new.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

/**
 * Controller responsible for user-related HTTP endpoints
 *
 * This controller provides RESTful API endpoints for managing users,
 * including CRUD operations with comprehensive validation and error handling.
 * All endpoints follow REST conventions and include detailed API documentation.
 *
 * @controller users
 */
@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * Creates a new user
   * POST /users
   */
  @Post()
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new user',
    description:
      'Creates a new user with role and branch assignment. Validates that the email is unique, and that the specified role and branch exist in the system.',
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: UserResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or database connection failed',
  })
  @ApiConflictResponse({ description: 'Email already exists' })
  @ApiNotFoundResponse({ description: 'Role or branch not found' })
  async create(
    @Body(ValidationPipe) createUserDto: CreateUserDto,
    @Request() req: any,
  ): Promise<UserResponseDto> {
    return this.usersService.create(createUserDto, req.user.id);
  }

  /**
   * Creates users from an uploaded Excel file
   * POST /users/upload-excel
   */
  @Post('upload-excel')
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.(xlsx|xls)$/)) {
          return callback(
            new BadRequestException(
              'Only Excel files (.xlsx, .xls) are allowed',
            ),
            false,
          );
        }
        callback(null, true);
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
      },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Excel file containing user data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description:
            'Excel file (.xlsx or .xls) with user data. Expected columns: Name, Email, Password, Phone, RM Code, Role, Branch',
        },
      },
      required: ['file'],
    },
  })
  @ApiOperation({
    summary: 'Create users from Excel file',
    description:
      'Creates multiple users from an uploaded Excel file. The Excel file should contain columns for name, email, phone_number, password, rm_code, role_name, and branch_name. Passwords will be hashed before storage. Role and branch names will be matched to existing records.',
  })
  @ApiResponse({
    status: 201,
    description: 'Users created successfully from Excel file',
    type: ExcelUploadResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid file format, file too large, or parsing errors',
  })
  @ApiConflictResponse({
    description: 'Some users could not be created due to conflicts',
  })
  async uploadExcel(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<ExcelUploadResponseDto> {
    console.log('Upload request received');
    console.log('File received:', !!file);

    if (!file) {
      console.log('No file in request');
      throw new BadRequestException(
        'No file uploaded. Please ensure the file is sent with field name "file"',
      );
    }

    console.log('File details:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
    });

    return this.usersService.createFromExcel(file);
  }

  /**
   * Exports users to Excel file with RBAC filtering
   * GET /users/export
   */
  @Get('export')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @ApiOperation({
    summary: 'Export users to Excel file with RBAC filtering',
    description:
      'Exports users to an Excel file (.xlsx) based on user permissions and sends it as a downloadable file. Access is controlled by user permissions: view.all.users (all users), view.region.users (users in same region), or view.branch.users (users in same branch). The file includes comprehensive user data with role and branch information.',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Optional search term to filter users before export',
    example: 'john',
  })
  @ApiResponse({
    status: 200,
    description: 'Excel file generated and downloaded successfully',
    headers: {
      'Content-Type': {
        description: 'MIME type of the Excel file',
        schema: {
          type: 'string',
          example:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        },
      },
      'Content-Disposition': {
        description: 'Attachment header with filename',
        schema: {
          type: 'string',
          example: 'attachment; filename="users-export-2024-01-15.xlsx"',
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description:
      'Database connection failed, insufficient permissions, or invalid search parameters',
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions to export users',
  })
  async exportUsers(
    @Res() res: Response,
    @Request() req: any,
    @Query('search') search?: string,
  ): Promise<void> {
    const buffer = await this.usersService.exportUsersToExcel(search, req.user);

    // Generate filename with current date
    const date = new Date().toISOString().split('T')[0];
    const filename = `users-export-${date}.xlsx`;

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', buffer.length);

    // Send the Excel file
    res.end(buffer);
  }

  /**
   * Retrieves a paginated list of users
   * GET /users
   */
  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all users with pagination and RBAC filtering',
    description:
      'Retrieves a paginated list of users with optional search functionality and RBAC filtering. Access is controlled by user permissions: view.all.users (all users), view.region.users (users in same region), or view.branch.users (users in same branch). Search can be performed across user name, email, RM code, role name, and branch name.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page (default: 10, max: 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description:
      'Search term to filter users by name, email, RM code, role, or branch',
    example: 'john',
  })
  @ApiQuery({
    name: 'useBranch',
    required: false,
    description: "If true, filter users by logged-in user's branch only",
    example: 'true',
  })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    type: 'object',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/PaginatedResponseDto' },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: '#/components/schemas/UserResponseDto' },
            },
          },
        },
      ],
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid query parameters or database connection failed',
  })
  async findAll(
    @Query(ValidationPipe) userPaginationDto: UserPaginationDto,
    @Request() req?: any,
  ): Promise<PaginatedResponseDto<UserResponseDto>> {
    const branchId =
      userPaginationDto.useBranch === 'true' && req?.user?.branch_id
        ? req.user.branch_id
        : undefined;
    return this.usersService.findAll(userPaginationDto, branchId);
  }

  /**
   * Retrieves a single user by ID
   * GET /users/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get user by ID',
    description:
      'Retrieves a single user by their UUID, including related role and branch information, as well as activity counts.',
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'User retrieved successfully',
    type: UserResponseDto,
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  @ApiBadRequestResponse({
    description: 'Invalid UUID format or database connection failed',
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<UserResponseDto> {
    return this.usersService.findOne(id);
  }

  /**
   * Updates an existing user
   * PATCH /users/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update user',
    description:
      'Updates an existing user. Validates email uniqueness if email is being changed, and validates that role and branch exist if they are being updated.',
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: UserResponseDto,
  })
  @ApiNotFoundResponse({ description: 'User, role, or branch not found' })
  @ApiBadRequestResponse({
    description: 'Invalid input data or database connection failed',
  })
  @ApiConflictResponse({ description: 'Email already exists for another user' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateUserDto: UpdateUserDto,
  ): Promise<UserResponseDto> {
    return this.usersService.update(id, updateUserDto);
  }

  /**
   * Deletes a user
   * DELETE /users/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete user',
    description:
      'Deletes a user by ID. Prevents deletion if the user has associated activities, leads, or other data that would be orphaned.',
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 204,
    description: 'User deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  @ApiBadRequestResponse({
    description: 'Invalid UUID format or database connection failed',
  })
  @ApiConflictResponse({
    description: 'User has associated data and cannot be deleted',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.usersService.remove(id);
  }

  /**
   * Assigns a role to a user
   * POST /users/:id/assign-role
   */
  @Post(':id/assign-role')
  @ApiOperation({
    summary: 'Assign role to user',
    description:
      'Assigns a specific role to a user. This is a specialized endpoint for role management that validates the role exists before assignment.',
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Role assigned successfully',
    type: UserResponseDto,
  })
  @ApiNotFoundResponse({ description: 'User or role not found' })
  @ApiBadRequestResponse({
    description: 'Invalid input data or database connection failed',
  })
  async assignRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) assignRoleDto: AssignRoleDto,
  ): Promise<UserResponseDto> {
    return this.usersService.assignRole(id, assignRoleDto);
  }

  /**
   * Transfers a user to a different branch
   * POST /users/:id/transfer-branch
   */
  @Post(':id/transfer-branch')
  @ApiOperation({
    summary: 'Transfer user to different branch',
    description:
      'Transfers a user to a different branch. This is useful for organizational changes and user relocations.',
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 200,
    description: 'User transferred successfully',
    type: UserResponseDto,
  })
  @ApiNotFoundResponse({ description: 'User or branch not found' })
  @ApiBadRequestResponse({
    description: 'Invalid input data or database connection failed',
  })
  async transferBranch(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) transferBranchDto: TransferBranchDto,
  ): Promise<UserResponseDto> {
    return this.usersService.transferBranch(id, transferBranchDto);
  }

  /**
   * Updates the last login timestamp for a user
   * PATCH /users/:id/last-login
   */
  @Patch(':id/last-login')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Update user last login',
    description:
      'Updates the last login timestamp for a user. This endpoint is typically called by authentication middleware to track user activity.',
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Last login updated successfully',
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  @ApiBadRequestResponse({
    description: 'Invalid input data or database connection failed',
  })
  async updateLastLogin(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateLastLoginDto: UpdateLastLoginDto,
  ): Promise<void> {
    return this.usersService.updateLastLogin(id, updateLastLoginDto);
  }

  /**
   * Changes a user's password
   * PATCH /users/:id/change-password
   */
  @Patch(':id/change-password')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Change user password',
    description:
      "Changes a user's password after verifying the current password. This endpoint requires the current password for security verification.",
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Password changed successfully',
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  @ApiBadRequestResponse({
    description: 'Invalid input data or database connection failed',
  })
  @ApiUnauthorizedResponse({ description: 'Current password is incorrect' })
  async changePassword(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) changePasswordDto: ChangePasswordDto,
  ): Promise<void> {
    return this.usersService.changePassword(id, changePasswordDto);
  }

  /**
   * Resets a user's password (admin operation)
   * PATCH /users/:id/reset-password
   */
  @Patch(':id/reset-password')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Reset user password (admin operation)',
    description:
      "Resets a user's password without requiring the current password. This is an administrative operation that should be restricted to authorized personnel.",
  })
  @ApiParam({
    name: 'id',
    description: 'User UUID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Password reset successfully',
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  @ApiBadRequestResponse({
    description: 'Invalid input data or database connection failed',
  })
  async resetPassword(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) resetPasswordDto: ResetPasswordDto,
  ): Promise<void> {
    return this.usersService.resetPassword(id, resetPasswordDto);
  }
}
