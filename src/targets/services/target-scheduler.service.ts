import { Injectable, Logger } from '@nestjs/common';
import { BackgroundTaskService } from '../../scheduled-tasks/background-task.service';

@Injectable()
export class TargetSchedulerService {
  private readonly logger = new Logger(TargetSchedulerService.name);

  constructor(
    private readonly backgroundTaskService: BackgroundTaskService,
  ) {}

  /**
   * Sets up the daily target progress generation task to run at 1 AM every day
   * This method is called from the seed file
   */
  async setupDailyTargetProgressGeneration() {
    try {
      this.logger.log('Setting up daily target progress generation task...');

      // Cron expression for 1:00 AM daily (Africa/Nairobi timezone)
      // Since Nairobi is UTC+3, 1 AM EAT = 10 PM UTC (22:00)
      const cronExpression = '0 22 * * *'; // 10 PM UTC = 1 AM EAT

      const task = await this.backgroundTaskService.scheduleRecurring(
        'daily-target-progress-generation',
        {
          // No specific payload needed, will use today's date by default
        },
        cronExpression,
        {
          name: 'Daily Target Progress Generation',
          description: 'Generates missing target progress records for all active targets at 1 AM daily',
          priority: 8,
          maxAttempts: 3,
        },
      );

      this.logger.log(`Daily target progress generation task scheduled with ID: ${task.id}`);
      this.logger.log('Task will run every day at 1:00 AM Nairobi time (22:00 UTC)');

      return task;

    } catch (error) {
      this.logger.error('Failed to set up daily target progress generation task:', error);
      throw error;
    }
  }

  /**
   * Manually trigger the daily target progress generation (for testing)
   */
  async triggerDailyTargetProgressGeneration(date?: string) {
    this.logger.log('Manually triggering daily target progress generation...');

    try {
      const task = await this.backgroundTaskService.runNow(
        'daily-target-progress-generation',
        {
          date: date || undefined, // Use provided date or default to today
        },
        {
          name: 'Manual Daily Target Progress Generation',
          description: 'Manually triggered target progress generation',
          priority: 9,
          maxAttempts: 3,
        },
      );

      this.logger.log(`Manual target progress generation triggered with task ID: ${task.id}`);
      return task;

    } catch (error) {
      this.logger.error('Failed to trigger manual target progress generation:', error);
      throw error;
    }
  }


}
