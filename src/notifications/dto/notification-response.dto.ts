import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { NotificationType, NotificationPriority } from '@prisma/client';

export class NotificationResponseDto {
  @ApiProperty({
    description: 'Notification ID',
    example: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Type of notification',
    enum: NotificationType,
    example: NotificationType.LEAD_ASSIGNED,
  })
  type: NotificationType;

  @ApiProperty({
    description: 'Notification title',
    example: 'New Lead Assigned',
  })
  title: string;

  @ApiProperty({
    description: 'Notification message',
    example: 'You have been assigned a new lead: <PERSON>',
  })
  message: string;

  @ApiProperty({
    description: 'ID of the user who should receive the notification',
    example: 'uuid',
  })
  recipient_id: string;

  @ApiPropertyOptional({
    description: 'ID of the user who triggered the notification',
    example: 'uuid',
  })
  sender_id?: string;

  @ApiPropertyOptional({
    description: 'Type of entity related to the notification',
    example: 'lead',
  })
  entity_type?: string;

  @ApiPropertyOptional({
    description: 'ID of the related entity',
    example: 'uuid',
  })
  entity_id?: string;

  @ApiProperty({
    description: 'Whether the notification has been read',
    example: false,
  })
  is_read: boolean;

  @ApiPropertyOptional({
    description: 'When the notification was read',
    example: '2024-01-15T10:30:00Z',
  })
  read_at?: Date;

  @ApiProperty({
    description: 'Notification priority',
    enum: NotificationPriority,
    example: NotificationPriority.NORMAL,
  })
  priority: NotificationPriority;

  @ApiPropertyOptional({
    description: 'Optional expiration date for the notification',
    example: '2024-12-31T23:59:59Z',
  })
  expires_at?: Date;

  @ApiProperty({
    description: 'When the notification was created',
    example: '2024-01-15T10:00:00Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'When the notification was last updated',
    example: '2024-01-15T10:30:00Z',
  })
  updated_at: Date;

  @ApiPropertyOptional({
    description: 'Sender user information',
  })
  sender?: {
    id: string;
    name: string;
    email: string;
    rm_code: string;
  };

  @ApiPropertyOptional({
    description: 'Recipient user information',
  })
  recipient?: {
    id: string;
    name: string;
    email: string;
    rm_code: string;
  };
}
