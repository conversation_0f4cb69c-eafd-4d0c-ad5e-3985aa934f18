import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { NotificationHandler, NotificationContext } from './notification.handler.interface';
import { NotificationType, NotificationPriority } from '@prisma/client';

@Injectable()
export class TwoByTwoHitlistAssignedHandler implements NotificationHandler {
  private readonly logger = new Logger(TwoByTwoHitlistAssignedHandler.name);
  readonly type = NotificationType.TWO_BY_TWO_HITLIST_ASSIGNED;

  constructor(private readonly prisma: PrismaService) {}

  shouldHandle(context: NotificationContext): boolean {
    return context.entityType === 'two_by_two_hitlist' && context.entityData?.assignedUserId;
  }

  async generateNotification(context: NotificationContext): Promise<{
    title: string;
    message: string;
    priority: NotificationPriority;
    data?: Record<string, any>;
    recipientIds: string[];
  }> {
    const { entityId, entityData, triggerUserId } = context;
    
    try {
      // Get two by two phase details
      const twoByTwoPhase = await this.prisma.twoByTwoPhase.findUnique({
        where: { id: entityId },
        include: {
          customer_service_hitlist_record: {
            include: {
              customer_service_hitlist: {
                include: {
                  uploader: {
                    select: {
                      id: true,
                      name: true,
                      email: true,
                      rm_code: true,
                    },
                  },
                },
              },
            },
          },
          assigned_to: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
        },
      });

      if (!twoByTwoPhase) {
        this.logger.warn(`Two by two phase ${entityId} not found for notification`);
        return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
      }

      if (!twoByTwoPhase.assigned_to) {
        this.logger.warn(`No assigned user found for two by two phase ${entityId}`);
        return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
      }

      // Get the user who uploaded the hitlist (trigger user)
      let uploadedByUser: { id: string; name: string; email: string; rm_code: string } | null = null;
      if (triggerUserId) {
        uploadedByUser = await this.prisma.user.findUnique({
          where: { id: triggerUserId },
          select: {
            id: true,
            name: true,
            email: true,
            rm_code: true,
          },
        });
      }

      const phaseDisplayName = this.getPhaseDisplayName(twoByTwoPhase.type);
      const title = `New 2by2by2 Hitlist Phase Assigned`;
      const message = `You have been assigned a new ${phaseDisplayName} phase for customer: ${twoByTwoPhase.customer_service_hitlist_record.customer_name}`;
      
      const data = {
        phaseId: twoByTwoPhase.id,
        hitlistRecordId: twoByTwoPhase.customer_service_hitlist_record.id,
        hitlistId: twoByTwoPhase.customer_service_hitlist_record.customer_service_hitlist.id,
        hitlistCode: twoByTwoPhase.customer_service_hitlist_record.customer_service_hitlist.code,
        customerName: twoByTwoPhase.customer_service_hitlist_record.customer_name,
        accountNumber: twoByTwoPhase.customer_service_hitlist_record.account_number,
        phoneNumber: twoByTwoPhase.customer_service_hitlist_record.phone_number,
        phaseType: twoByTwoPhase.type,
        phaseDisplayName,
        expectedCompletionDate: twoByTwoPhase.expected_completion_date,
        uploadedBy: uploadedByUser?.name || 'System',
        uploadedByRmCode: uploadedByUser?.rm_code,
        uploadDate: twoByTwoPhase.customer_service_hitlist_record.customer_service_hitlist.uploaded_date,
        createdAt: twoByTwoPhase.created_at,
      };

      return {
        title,
        message,
        priority: NotificationPriority.NORMAL,
        data,
        recipientIds: [twoByTwoPhase.assigned_to.id],
      };

    } catch (error) {
      this.logger.error(`Failed to generate two by two hitlist assigned notification: ${error.message}`, error.stack);
      return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
    }
  }

  private getPhaseDisplayName(phaseType: string): string {
    switch (phaseType) {
      case 'first2':
        return 'First 2 Days';
      case 'second2':
        return 'Second 2 Weeks';
      case 'third2':
        return 'Third 2 Months';
      default:
        return phaseType;
    }
  }
}
