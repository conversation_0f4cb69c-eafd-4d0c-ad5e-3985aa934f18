import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsDateString } from 'class-validator';

export enum VoiceEventType {
  RINGING = 'Ringing',
  ANSWERED = 'Answered',
  COMPLETED = 'Completed',
  FAILED = 'Failed',
  BUSY = 'Busy',
  NO_ANSWER = 'NoAnswer',
}

export class VoiceEventDto {
  @ApiProperty({
    description: 'Session ID for the call',
    example: 'ATVoice_12345678-1234-1234-1234-123456789012',
  })
  @IsString()
  @IsOptional()
  sessionId?: string;

  @ApiProperty({
    description: 'Type of voice event',
    enum: VoiceEventType,
    example: VoiceEventType.ANSWERED,
  })
  @IsEnum(VoiceEventType)
  @IsOptional()
  eventType?: VoiceEventType;

  @ApiProperty({
    description: 'Phone number that initiated the call',
    example: '+254700000000',
  })
  @IsString()
  @IsOptional()
  from?: string;

  @ApiProperty({
    description: 'Phone number that received the call',
    example: '+254700000001',
  })
  @IsString()
  @IsOptional()
  to?: string;

  @ApiProperty({
    description: 'Duration of the call in seconds (for completed calls)',
    example: 120,
  })
  @IsOptional()
  durationInSeconds?: number;

  @ApiProperty({
    description: 'Recording URL if call was recorded',
    example: 'https://voice.africastalking.com/recordings/12345.mp3',
  })
  @IsString()
  @IsOptional()
  recordingUrl?: string;

  @ApiProperty({
    description: 'Timestamp of the event',
    example: '2025-01-15T10:30:00.000Z',
  })
  @IsDateString()
  @IsOptional()
  timestamp?: string;

  @ApiProperty({
    description: 'Additional event data from Africa\'s Talking',
    example: { hangupCause: 'NORMAL_CLEARING' },
  })
  @IsOptional()
  eventData?: Record<string, any>;
}
