import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateTargetDto } from './dto/create-target.dto';
import { UpdateTargetDto } from './dto/update-target.dto';
import { CreateIndividualTargetDto } from './dto/create-individual-target.dto';
import { TargetResponseDto } from './dto/target-response.dto';
import { TargetBreakdownResponseDto } from './dto/target-breakdown-response.dto';
import { MyTargetsResponseDto } from './dto/my-targets-response.dto';
import { DailyTargetHandler } from './handlers/daily-target.handler';
import { BackgroundTaskService } from '../scheduled-tasks/background-task.service';
import { isApplicable, getTodayAt2AM } from '../common/utils/date.utils';
import { startOfDay, endOfDay } from 'date-fns';

const todayStart = startOfDay(new Date());
const todayEnd = endOfDay(new Date());

/**
 * Service handling target-related business logic
 * Provides operations for creating and managing targets
 */
@Injectable()
export class TargetsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly dailyTargetHandler: DailyTargetHandler,
    private readonly backgroundTaskService: BackgroundTaskService,
  ) {}

  /**
   * Creates targets based on the provided data
   * @param createTargetDto - Data for creating targets
   * @param userBranchId - Branch ID of the logged-in user
   * @param userPermissions - User permissions for access control
   * @param loggedInUserId - ID of the logged-in user
   * @returns Promise<TargetResponseDto> - Created target with sample data
   */
  async createTargets(
    createTargetDto: CreateTargetDto,
    userBranchId: string,
    userPermissions: string[] = [],
    loggedInUserId: string,
  ): Promise<TargetResponseDto> {
    const {
      metricType,
      targetValue,
      frequency,
      startDate,
      endDate,
      scope,
      assignTo,
      activity,
    } = createTargetDto;

    // Validate scope and assignTo IDs
    if (scope === 'role') {
      await this.dailyTargetHandler.validateRoles(assignTo);
    } else if (scope === 'individual') {
      await this.dailyTargetHandler.validateUsers(assignTo);
    }

    // Handle different frequencies and scopes
    let createdTargetIds: string[];

    if (frequency === 'daily') {
      if (scope === 'role') {
        // For role scope daily targets, use background task for permission-based user selection
        const task = await this.backgroundTaskService.runNow(
          'target-creation',
          {
            metricType,
            targetValue,
            frequency,
            startDate,
            endDate,
            activity,
            scope,
            assignTo,
            userBranchId,
            loggedInUserId,
          },
          {
            name: 'Role Target Creation',
            description: `Creating ${assignTo.length} role targets with permission-based user selection`,
            priority: 8,
            maxAttempts: 3,
            createdBy: loggedInUserId,
          },
        );

        createdTargetIds = [task.id];
      } else {
        // For individual scope daily targets, create targets immediately then trigger background task for progress
        createdTargetIds = await this.dailyTargetHandler.handleDailyTargets(
          createTargetDto,
          userBranchId,
        );

        // Trigger background task to create progress records
        await this.backgroundTaskService.runNow(
          'individual-target-progress',
          {
            targetIds: createdTargetIds,
            userBranchId: userBranchId,
            scope: 'individual',
          },
          {
            name: 'Individual Target Progress Creation',
            description: `Creating progress records for ${createdTargetIds.length} individual targets`,
            priority: 5,
            maxAttempts: 3,
            createdBy: loggedInUserId,
          },
        );
      }
    } else {
      // For weekly and custom frequencies, use the original logic
      createdTargetIds = await this.handleNonDailyTargets(
        createTargetDto,
        userBranchId,
      );
    }

    // Return sample data based on scope
    if (scope === 'individual') {
      // Get user details for individual scope
      const user = await this.prisma.user.findFirst({
        where: { id: { in: assignTo } },
        include: {
          role: {
            select: {
              name: true,
            },
          },
        },
      });

      return {
        id: createdTargetIds[0] || 'e6dd1952-0f0e-4365-a28a-c88bf7fc6244',
        metric: metricType,
        assigned_to: {
          id: user?.id || assignTo[0],
          name: user?.name || 'John Doe',
          role: user?.role?.name || 'Branch Manager',
        },
        frequency: frequency,
        activity: activity,
        value: targetValue,
        start_date: startDate,
        end_date: endDate ?? null,
        users_count: 1,
        exempted_users: 0,
        scope: 'individual',
        activity_count: 0,
        total_calls_made: 0,
        total_visits_made: 0,
        total_target: targetValue,
      };
    } else {
      // Get role details for role scope
      const role = await this.prisma.role.findFirst({
        where: { id: { in: assignTo } },
        include: {
          users: {
            select: {
              id: true,
            },
          },
        },
      });

      // For role scope with background processing, return task reference
      if (frequency === 'daily' && scope === 'role') {
        return {
          id: createdTargetIds[0], // Use the background task ID as a reference
          metric: metricType,
          assigned_to: role?.name || 'Role',
          frequency: frequency,
          activity: activity,
          value: targetValue,
          start_date: startDate,
          end_date: endDate ?? null,
          users_count: 0, // Will be determined during background processing
          exempted_users: 0,
          scope: 'role',
          total_calls_made: 0,
          total_visits_made: 0,
          total_target: 0, // Will be calculated during background processing
        };
      } else {
        // For non-daily role targets, return normal response
        return {
          id: createdTargetIds[0] || '67509206-01ec-4e45-9269-1130fef4f661',
          metric: metricType,
          assigned_to: role?.name || 'Branch Manager',
          frequency: frequency,
          activity: activity,
          value: targetValue,
          start_date: startDate,
          end_date: endDate ?? null,
          users_count: role?.users?.length || 3,
          exempted_users: 0,
          scope: 'role',
          total_calls_made: 0,
          total_visits_made: 0,
          total_target: targetValue * (role?.users?.length || 3),
        };
      }
    }
  }

  /**
   * Handle creation of non-daily targets (weekly, custom)
   * @param createTargetDto - Data for creating targets
   * @param userBranchId - Branch ID of the logged-in user
   * @returns Promise<string[]> - Array of created target IDs
   */
  private async handleNonDailyTargets(
    createTargetDto: CreateTargetDto,
    userBranchId: string,
  ): Promise<string[]> {
    const {
      metricType,
      targetValue,
      frequency,
      startDate,
      endDate,
      scope,
      assignTo,
      activity,
    } = createTargetDto;

    // Parse dates for non-daily targets
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : null;

    // For non-daily targets, endDate is required
    if (!end) {
      throw new BadRequestException(
        'End date is required for weekly and custom frequency targets',
      );
    }

    // Create targets for each assignTo ID
    const targetsData = assignTo.map((id) => ({
      metric_type: metricType as 'Call' | 'Visit',
      target_value: targetValue,
      frequency: frequency as 'weekly' | 'custom',
      activity: activity as 'LEADS' | 'CUSTOMER_SERVICE' | 'LOAN_ACTIVITIES',
      start_date: start,
      end_date: end,
      branch_id: userBranchId,
      ...(scope === 'role' ? { role_id: id } : { user_id: id }),
    }));

    const createdTargets: string[] = [];
    for (const targetData of targetsData) {
      const target = await this.prisma.target.create({
        data: {
          ...targetData,
          end_date: targetData.end_date || undefined, // Convert null to undefined for Prisma
        },
      });
      createdTargets.push(target.id);
    }

    return createdTargets;
  }

  /**
   * Gets all targets formatted for UI display
   * @param userBranchId - Branch ID of the logged-in user
   * @param status - Optional status filter (defaults to 'active')
   * @param type - Optional type filter ('role' or 'individual')
   * @param userPermissions - User permissions for access control
   * @returns Promise<TargetResponseDto[]> - Array of formatted targets
   */
  async getAllTargets(
    userBranchId: string,
    status?: string,
    type?: 'role' | 'individual',
    userPermissions?: string[],
  ): Promise<TargetResponseDto[]> {
    // Check permissions - return empty array if no permissions
    if (!userPermissions || userPermissions.length === 0) {
      const hasViewAllBranches = userPermissions?.includes(
        'targets.view.all.branches',
      );
      const hasViewMyBranch = userPermissions?.includes(
        'targets.view.my.branch',
      );

      if (!hasViewAllBranches && !hasViewMyBranch) {
        return [];
      }
    }

    // Determine status filter - default to 'active' if not provided
    const statusFilter = status?.toLowerCase() || 'active';
    const statusEnum =
      statusFilter === 'active'
        ? 'Active'
        : statusFilter === 'expired'
          ? 'Expired'
          : statusFilter === 'archived'
            ? 'Archived'
            : 'Active';

    // Build where clause
    const whereClause: any = {
      status: statusEnum as any,
    };

    // Add type filter
    if (type === 'role') {
      whereClause.role_id = { not: null };
    } else if (type === 'individual') {
      whereClause.user_id = { not: null };
    }

    // Add branch filter based on permissions
    const hasViewAllBranches = userPermissions?.includes(
      'targets.view.all.branches',
    );
    const hasViewMyBranch = userPermissions?.includes('targets.view.my.branch');
    const hasViewMyRegion = userPermissions?.includes('targets.view.my.region');

    if (hasViewAllBranches) {
      // Can see all branches - no branch filter
    } else if (hasViewMyRegion) {
      // Can see targets from all branches in their region
      const userBranch = await this.prisma.branch.findUnique({
        where: { id: userBranchId },
        select: { region_id: true },
      });

      if (userBranch) {
        const regionBranches = await this.prisma.branch.findMany({
          where: { region_id: userBranch.region_id },
          select: { id: true },
        });

        whereClause.branch_id = {
          in: regionBranches.map((branch) => branch.id),
        };
      } else {
        // Fallback to user's branch if region not found
        whereClause.branch_id = userBranchId;
      }
    } else if (hasViewMyBranch) {
      // Can see only their branch
      whereClause.branch_id = userBranchId;
    } else {
      // No permissions - return empty (already handled above, but double check)
      return [];
    }

    const targets = await this.prisma.target.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: {
              select: {
                name: true,
              },
            },
          },
        },
        role: {
          select: {
            id: true,
            name: true,
          },
        },
        exempted_target_users: {
          select: {
            id: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    return Promise.all(
      targets.map(async (target) => {
        const isIndividual = target.user_id !== null;

        // Get activity count for individual targets
        let activityCount: number | undefined;
        if (isIndividual && target.user_id) {
          const interactionType = target.metric_type.toLowerCase() as
            | 'call'
            | 'visit';
          activityCount = await this.prisma.activity.count({
            where: {
              performed_by_user_id: target.user_id,
              interaction_type: interactionType,
              created_at: {
                gte: target.start_date,
                lte: target.end_date || new Date(),
              },
            },
          });
        }

        // Calculate total calls and visits made
        const [totalCallsMade, totalVisitsMade] = await Promise.all([
          this.calculateTotalActivities(target, 'call'),
          this.calculateTotalActivities(target, 'visit'),
        ]);

        // Calculate total target, users count, and exempted users
        const [totalTarget, usersCount] = await Promise.all([
          this.calculateTotalTarget(target),
          this.calculateUsersCount(target),
        ]);

        const exemptedUsers = this.getExemptedUsersCount(target);

        // Format assigned_to based on scope
        const assigned_to = target.user
          ? {
              id: target.user.id,
              name: target.user.name,
              role: target.user.role?.name,
            }
          : target.role?.name || 'Unknown';

        // Calculate achieved users for role targets
        const achievedUsers = target.role_id
          ? await this.calculateAchievedUsers(target.id)
          : 0;

        return {
          id: target.id,
          metric: target.metric_type,
          assigned_to,
          frequency: target.frequency,
          activity: target.activity,
          value: target.target_value,
          start_date: target.start_date.toISOString().split('T')[0],
          end_date: target.end_date
            ? target.end_date.toISOString().split('T')[0]
            : null,
          scope: target.role_id ? 'role' : 'individual',
          users_count: usersCount,
          exempted_users: exemptedUsers,
          achieved_users: achievedUsers,
          status: target.status,
          total_calls_made: totalCallsMade,
          total_visits_made: totalVisitsMade,
          total_target: totalTarget,
          ...(isIndividual && { activity_count: activityCount }),
        };
      }),
    );
  }

  /**
   * Gets count of targets by type with permission-based access control
   * @param userBranchId - Branch ID of the logged-in user
   * @param userPermissions - User permissions for access control
   * @returns Promise<{role_targets_count: number, individual_targets_count: number}>
   */
  async getTargetsCount(
    userBranchId: string,
    userPermissions?: string[],
  ): Promise<{ role_targets_count: number; individual_targets_count: number }> {
    // Check permissions - return zero counts if no permissions
    if (!userPermissions || userPermissions.length === 0) {
      const hasViewAllBranches = userPermissions?.includes(
        'targets.view.all.branches',
      );
      const hasViewMyBranch = userPermissions?.includes(
        'targets.view.my.branch',
      );

      if (!hasViewAllBranches && !hasViewMyBranch) {
        return { role_targets_count: 0, individual_targets_count: 0 };
      }
    }

    // Build where clause based on permissions
    const whereClause: any = {};

    // Add branch filter based on permissions
    const hasViewAllBranches = userPermissions?.includes(
      'targets.view.all.branches',
    );
    const hasViewMyBranch = userPermissions?.includes('targets.view.my.branch');
    const hasViewMyRegion = userPermissions?.includes('targets.view.my.region');

    if (hasViewAllBranches) {
      // Can see all branches - no branch filter
    } else if (hasViewMyRegion) {
      // Can see targets from all branches in their region
      const userBranch = await this.prisma.branch.findUnique({
        where: { id: userBranchId },
        select: { region_id: true },
      });

      if (userBranch) {
        const regionBranches = await this.prisma.branch.findMany({
          where: { region_id: userBranch.region_id },
          select: { id: true },
        });

        whereClause.branch_id = {
          in: regionBranches.map((branch) => branch.id),
        };
      } else {
        // Fallback to user's branch if region not found
        whereClause.branch_id = userBranchId;
      }
    } else if (hasViewMyBranch) {
      // Can see only their branch
      whereClause.branch_id = userBranchId;
    } else {
      // No permissions - return zero counts
      return { role_targets_count: 0, individual_targets_count: 0 };
    }

    // Get counts for both types
    const [roleTargetsCount, individualTargetsCount] = await Promise.all([
      this.prisma.target.count({
        where: {
          ...whereClause,
          role_id: { not: null },
        },
      }),
      this.prisma.target.count({
        where: {
          ...whereClause,
          user_id: { not: null },
        },
      }),
    ]);

    return {
      role_targets_count: roleTargetsCount,
      individual_targets_count: individualTargetsCount,
    };
  }

  /**
   * Updates a target by ID
   * @param id - Target ID to update
   * @param updateTargetDto - Data to update
   * @returns Promise<void> - Confirmation of target update
   */
  async updateTarget(
    id: string,
    updateTargetDto: UpdateTargetDto,
  ): Promise<void> {
    const { metricType, targetValue, frequency, startDate, endDate, activity } =
      updateTargetDto;

    // Check if target exists
    const existingTarget = await this.prisma.target.findUnique({
      where: { id },
    });

    if (!existingTarget) {
      throw new NotFoundException(`Target with ID '${id}' not found`);
    }

    // Parse dates if provided (no validation needed)

    // Build update data object
    const updateData: any = {};

    if (metricType !== undefined) {
      updateData.metric_type = metricType as 'Call' | 'Visit';
    }
    if (targetValue !== undefined) {
      updateData.target_value = targetValue;
    }
    if (frequency !== undefined) {
      updateData.frequency = frequency as 'daily' | 'weekly' | 'custom';
    }
    if (activity !== undefined) {
      updateData.activity = activity as
        | 'LEADS'
        | 'CUSTOMER_SERVICE'
        | 'LOAN_ACTIVITIES';
    }
    if (startDate !== undefined) {
      updateData.start_date = new Date(startDate);
    }
    if (endDate !== undefined) {
      updateData.end_date = endDate ? new Date(endDate) : undefined;
    }

    // Update the target
    await this.prisma.target.update({
      where: { id },
      data: updateData,
    });
  }

  /**
   * Deletes a target by ID
   * @param id - Target ID to delete
   * @returns Promise<void> - Confirmation of target deletion
   */
  async deleteTarget(id: string): Promise<void> {
    // Check if target exists
    const existingTarget = await this.prisma.target.findUnique({
      where: { id },
    });

    if (!existingTarget) {
      throw new NotFoundException(`Target with ID '${id}' not found`);
    }

    // Delete the target
    await this.prisma.target.delete({
      where: { id },
    });
  }

  /**
   * Gets breakdown of users for a role target
   * @param id - Target ID (must be a role target)
   * @returns Promise<TargetBreakdownResponseDto[]> - Array of user breakdowns
   */
  async getTargetBreakdown(id: string): Promise<TargetBreakdownResponseDto[]> {
    // Get the target and ensure it's a role target
    const target = await this.prisma.target.findUnique({
      where: { id },
      include: {
        role: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!target) {
      throw new NotFoundException(`Target with ID '${id}' not found`);
    }

    if (!target.role_id) {
      throw new BadRequestException(
        'Target breakdown is only available for role targets',
      );
    }

    // Get exempted user IDs for this target
    const exemptedUserIds = await this.prisma.exemptedTargetUser.findMany({
      where: {
        target_id: id,
      },
      select: {
        user_id: true,
      },
    });

    const exemptedIds = exemptedUserIds.map((exempted) => exempted.user_id);

    // Get all users with this role and branch, excluding exempted users
    const users = await this.prisma.user.findMany({
      where: {
        role_id: target.role_id,
        branch_id: target.branch_id, // Filter by target's branch
        id: {
          notIn: exemptedIds,
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    // Calculate calls and visits for each user from today's TargetProgress records
    const breakdown = await Promise.all(
      users.map(async (user) => {
        // Get today's TargetProgress record for this user and target
        const todayProgress = await this.prisma.targetProgress.findFirst({
          where: {
            target_id: target.id,
            user_id: user.id,
            for_date: {
              gte: todayStart,
              lte: todayEnd,
            },
          },
        });

        // Determine counts based on target metric type
        let callsCount = 0;
        let visitsCount = 0;

        if (todayProgress) {
          if (target.metric_type.toLowerCase() === 'call') {
            callsCount = todayProgress.achieved_count;
          } else if (target.metric_type.toLowerCase() === 'visit') {
            visitsCount = todayProgress.achieved_count;
          }
        }

        return {
          user_id: user.id,
          name: user.name,
          calls_made: callsCount,
          visits_made: visitsCount,
          target: target.target_value,
        };
      }),
    );

    return breakdown;
  }

  /**
   * Creates an individual target from an existing target
   * @param id - Source target ID
   * @param createIndividualTargetDto - Data for creating individual target
   * @returns Promise<TargetResponseDto> - The created individual target
   */
  async createIndividualTarget(
    id: string,
    createIndividualTargetDto: CreateIndividualTargetDto,
  ): Promise<TargetResponseDto> {
    const { newTarget, userId } = createIndividualTargetDto;

    // Get the source target
    const sourceTarget = await this.prisma.target.findUnique({
      where: { id },
    });

    if (!sourceTarget) {
      throw new NotFoundException(`Target with ID '${id}' not found`);
    }

    // Validate user exists
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        role: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    // Convert newTarget to number
    const targetValue = parseInt(newTarget, 10);
    if (isNaN(targetValue) || targetValue < 1) {
      throw new BadRequestException(
        'New target must be a valid number greater than 0',
      );
    }

    // Create the individual target
    const createdTarget = await this.prisma.target.create({
      data: {
        metric_type: sourceTarget.metric_type,
        target_value: targetValue,
        frequency: sourceTarget.frequency,
        start_date: sourceTarget.start_date,
        end_date: sourceTarget.end_date,
        branch_id: sourceTarget.branch_id, // Include branch_id from source target
        user_id: userId,
        // role_id is intentionally not set for individual targets
      },
      include: {
        exempted_target_users: {
          select: {
            id: true,
          },
        },
      },
    });

    // Transfer all TargetProgress records from the source target to the new individual target
    await this.prisma.targetProgress.updateMany({
      where: {
        target_id: id, // Source target ID
        user_id: userId,
      },
      data: {
        target_id: createdTarget.id, // New individual target ID
      },
    });

    // Calculate activity count for the individual target
    const interactionType = createdTarget.metric_type.toLowerCase() as
      | 'call'
      | 'visit';
    const activityCount = await this.prisma.activity.count({
      where: {
        performed_by_user_id: userId,
        interaction_type: interactionType,
        created_at: {
          gte: createdTarget.start_date,
          lte: createdTarget.end_date || new Date(),
        },
      },
    });

    // Add user to exempted target users for the source target
    await this.prisma.exemptedTargetUser.create({
      data: {
        user_id: userId,
        target_id: id, // Source target ID
      },
    });

    // Calculate total calls and visits made
    const [totalCallsMade, totalVisitsMade] = await Promise.all([
      this.calculateTotalActivities(createdTarget, 'call'),
      this.calculateTotalActivities(createdTarget, 'visit'),
    ]);

    // Calculate total target, users count, and exempted users
    const [totalTarget, usersCount] = await Promise.all([
      this.calculateTotalTarget(createdTarget),
      this.calculateUsersCount(createdTarget),
    ]);

    const exemptedUsers = this.getExemptedUsersCount(createdTarget);

    // Return the formatted response
    return {
      id: createdTarget.id,
      metric: createdTarget.metric_type,
      assigned_to: {
        id: user.id,
        name: user.name,
        role: user.role?.name,
      },
      frequency: createdTarget.frequency,
      activity: createdTarget.activity,
      value: createdTarget.target_value,
      start_date: createdTarget.start_date.toISOString().split('T')[0],
      end_date: createdTarget.end_date
        ? createdTarget.end_date.toISOString().split('T')[0]
        : null,
      scope: 'individual',
      users_count: usersCount,
      exempted_users: exemptedUsers,
      total_calls_made: totalCallsMade,
      total_visits_made: totalVisitsMade,
      total_target: totalTarget,
      activity_count: activityCount,
    };
  }

  /**
   * Calculates total activities for a target based on interaction type
   * @param target - Target object with relations
   * @param interactionType - Type of interaction ('call' or 'visit')
   * @returns Promise<number> - Total count of activities
   */
  private async calculateTotalActivities(
    target: any,
    interactionType: 'call' | 'visit',
  ): Promise<number> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    // Check if target metric type matches the interaction type
    const targetMetricType = target.metric_type.toLowerCase();
    if (targetMetricType !== interactionType) {
      return 0; // Return 0 if metric type doesn't match
    }

    // Query TargetProgress records for today's date and sum achieved_count
    const result = await this.prisma.targetProgress.aggregate({
      where: {
        target_id: target.id,
        for_date: {
          gte: todayStart,
          lte: todayEnd,
        },
      },
      _sum: {
        achieved_count: true,
      },
    });

    return result?._sum.achieved_count || 0;
  }

  /**
   * Calculates total target value adjusted for exempted users
   * @param target - Target object with relations
   * @returns Promise<number> - Total target value
   */
  private async calculateTotalTarget(target: any): Promise<number> {
    if (target.role_id) {
      // For role targets: count users in role and branch minus exempted users, then multiply by target value
      const [totalUsersInRole, exemptedUsersCount] = await Promise.all([
        this.prisma.user.count({
          where: {
            role_id: target.role_id,
            branch_id: target.branch_id,
          },
        }),
        target.exempted_target_users.length,
      ]);

      const activeUsers = totalUsersInRole - exemptedUsersCount;
      return Math.max(0, activeUsers) * target.target_value;
    } else if (target.user_id) {
      // For individual targets: return the target value as is
      return target.target_value;
    }

    return 0;
  }

  /**
   * Calculates the number of users using a target
   * @param target - Target object with relations
   * @returns Promise<number> - Number of users using the target
   */
  private async calculateUsersCount(target: any): Promise<number> {
    if (target.user_id) {
      // Individual target: always 1 user
      return 1;
    } else if (target.role_id) {
      // Role target: count users in role and branch minus exempted users
      const [totalUsersInRole, exemptedUsersCount] = await Promise.all([
        this.prisma.user.count({
          where: {
            role_id: target.role_id,
            branch_id: target.branch_id,
          },
        }),
        target.exempted_target_users.length,
      ]);

      return Math.max(0, totalUsersInRole - exemptedUsersCount);
    }

    return 0;
  }

  /**
   * Gets the number of exempted users for a target
   * @param target - Target object with relations
   * @returns number - Number of exempted users
   */
  private getExemptedUsersCount(target: any): number {
    return target.exempted_target_users.length;
  }

  /**
   * Get today's targets for the logged-in user
   * @param userId - ID of the logged-in user
   * @returns Promise<MyTargetsResponseDto> - Today's targets and progress
   */
  async getMyTargets(userId: string): Promise<MyTargetsResponseDto> {
    // Get user to verify they exist
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { id: true },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    // Fetch all target progress records of the user whose for_date matches today's date
    const targetProgressRecords = await this.prisma.targetProgress.findMany({
      where: {
        user_id: userId,
        for_date: {
          gte: startOfDay,
          lte: endOfDay,
        },
      },
      include: {
        target: {
          select: {
            id: true,
            activity: true,
          },
        },
      },
    });

    // Map to the required response format
    const activities = targetProgressRecords.map((progress) => ({
      id: progress.target.id, // id of the target which the progress record belongs to
      is_applicable: progress.is_applicable, // of the target progress
      target_value: progress.target_value, // that of the target progress
      done_count: progress.achieved_count, // use the achieved_count field of the target progress
      activity: progress.target.activity, // use the value target's activity field
    }));

    return { activities };
  }

  /**
   * Calculates the number of users who achieved the target for today
   * @param targetId - Target ID
   * @returns Promise<number> - Number of users who achieved the target
   */
  private async calculateAchievedUsers(targetId: string): Promise<number> {
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    const count = await this.prisma.targetProgress.count({
      where: {
        target_id: targetId,
        for_date: {
          gte: startOfDay,
          lte: endOfDay,
        },
        is_achieved: true,
      },
    });

    return count;
  }

  /**
   * Archives a target by setting status to Archived and updating archived_at
   * @param id - Target ID to archive
   * @returns Promise<void> - Confirmation of target archival
   */
  async archiveTarget(id: string): Promise<void> {
    // Check if target exists
    const existingTarget = await this.prisma.target.findUnique({
      where: { id },
    });

    if (!existingTarget) {
      throw new NotFoundException(`Target with ID '${id}' not found`);
    }

    // Update the target status to Archived and set archived_at
    await this.prisma.target.update({
      where: { id },
      data: {
        status: 'Archived',
        archived_at: new Date(),
      },
    });
  }
}
