import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
  ApiNotFoundResponse,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SupportTicketsService } from './support-tickets.service';
import { TicketIdPipe } from '../common/pipes/ticket-id.pipe';
import {
  SupportTicketResponseDto,
  CreateSupportTicketDto,
  UpdateSupportTicketDto,
} from './dto/support-ticket.dto';

@ApiTags('support-tickets')
@Controller('support_tickets')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SupportTicketsController {
  constructor(private readonly supportTicketsService: SupportTicketsService) {}

  /**
   * Gets all support tickets for the logged-in user
   * GET /support_tickets
   */
  @Get()
  @ApiOperation({
    summary: 'Get user support tickets',
    description: 'Retrieves all support tickets submitted by the currently authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Support tickets retrieved successfully',
    type: [SupportTicketResponseDto],
  })
  async getUserSupportTickets(@Request() req: any): Promise<SupportTicketResponseDto[]> {
    return this.supportTicketsService.getUserSupportTickets(req.user.id);
  }

  /**
   * Creates a new support ticket
   * POST /support_tickets
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create support ticket',
    description: 'Creates a new support ticket for the currently authenticated user',
  })
  @ApiBody({
    type: CreateSupportTicketDto,
    description: 'Support ticket data',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Support ticket created successfully',
    type: SupportTicketResponseDto,
  })
  async createSupportTicket(
    @Request() req: any,
    @Body(ValidationPipe) createDto: CreateSupportTicketDto,
  ): Promise<SupportTicketResponseDto> {
    return this.supportTicketsService.createSupportTicket(req.user.id, createDto);
  }

  /**
   * Updates a support ticket
   * PATCH /support_tickets/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update support ticket',
    description: 'Updates a support ticket. Only provided fields will be updated.',
  })
  @ApiParam({
    name: 'id',
    description: 'Support ticket ID',
    example: 'ST-2025-0001',
  })
  @ApiBody({
    type: UpdateSupportTicketDto,
    description: 'Support ticket update data (all fields optional)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Support ticket updated successfully',
    type: SupportTicketResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Support ticket not found',
  })
  async updateSupportTicket(
    @Param('id', TicketIdPipe) ticketId: string,
    @Request() req: any,
    @Body(ValidationPipe) updateDto: UpdateSupportTicketDto,
  ): Promise<SupportTicketResponseDto> {
    return this.supportTicketsService.updateSupportTicket(ticketId, req.user.id, updateDto);
  }

  /**
   * Deletes a support ticket
   * DELETE /support_tickets/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete support ticket',
    description: 'Deletes a support ticket',
  })
  @ApiParam({
    name: 'id',
    description: 'Support ticket ID',
    example: 'ST-2025-0001',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Support ticket deleted successfully',
  })
  @ApiNotFoundResponse({
    description: 'Support ticket not found',
  })
  async deleteSupportTicket(
    @Param('id', TicketIdPipe) ticketId: string,
    @Request() req: any,
  ): Promise<void> {
    return this.supportTicketsService.deleteSupportTicket(ticketId, req.user.id);
  }
}
