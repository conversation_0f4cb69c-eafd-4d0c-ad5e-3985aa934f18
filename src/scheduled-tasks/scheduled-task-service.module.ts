import { Module } from '@nestjs/common';
import { ScheduledTaskService } from './scheduled-task.service';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Module that provides only the ScheduledTaskService
 * without the task handlers. This is used by the scheduler to query tasks
 * without executing them.
 */
@Module({
  imports: [PrismaModule],
  providers: [ScheduledTaskService],
  exports: [ScheduledTaskService],
})
export class ScheduledTaskServiceModule {}
