import { ApiProperty } from '@nestjs/swagger';

/**
 * PurposeOfActivityResponseDto
 * 
 * Data Transfer Object for Purpose of Activity responses.
 * Defines the structure of data returned by the API endpoints.
 * Includes metadata about related entities for better API usability.
 */
export class PurposeOfActivityResponseDto {
  /**
   * Unique identifier for the purpose of activity
   */
  @ApiProperty({
    description: 'Unique identifier for the purpose of activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  /**
   * Name of the activity purpose
   */
  @ApiProperty({
    description: 'Name of the activity purpose',
    example: 'Product Demo',
  })
  name: string;

  /**
   * Detailed description of the activity purpose
   */
  @ApiProperty({
    description: 'Detailed description of the activity purpose',
    example: 'Demonstrate product features and capabilities to potential customers',
    nullable: true,
  })
  description: string | null;

  /**
   * Count of general activities using this purpose
   */
  @ApiProperty({
    description: 'Number of general activities associated with this purpose',
    example: 15,
  })
  general_activities_count: number;

  /**
   * Count of specific activities using this purpose
   */
  @ApiProperty({
    description: 'Number of specific activities associated with this purpose',
    example: 42,
  })
  activities_count: number;

  /**
   * Total count of all activities using this purpose
   */
  @ApiProperty({
    description: 'Total number of activities (general + specific) using this purpose',
    example: 57,
  })
  total_activities_count: number;

  /**
   * Indicates if this purpose is currently in use
   */
  @ApiProperty({
    description: 'Whether this purpose is currently being used by any activities',
    example: true,
  })
  is_in_use: boolean;

  /**
   * UUID of the purpose category this purpose belongs to
   */
  @ApiProperty({
    description: 'UUID of the purpose category this purpose belongs to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  purpose_category_id: string;

  /**
   * Purpose category details
   */
  @ApiProperty({
    description: 'Purpose category this purpose belongs to',
    type: 'object',
    properties: {
      id: { type: 'string', example: '550e8400-e29b-41d4-a716-************' },
      name: { type: 'string', example: 'Sales Activities' },
      description: { type: 'string', example: 'Category for sales-related purposes', nullable: true },
    },
  })
  purpose_category: {
    id: string;
    name: string;
    description: string | null;
  };

  /**
   * ID of the user who added this category
   */
  @ApiProperty({
    description: 'ID of the user who added this category',
    example: '550e8400-e29b-41d4-a716-************',
    nullable: true,
  })
  added_by?: string | null;

  /**
   * User who added this category
   */
  @ApiProperty({
    description: 'User who added this category',
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'John Doe',
      email: '<EMAIL>',
      rm_code: 'RM001',
    },
    nullable: true,
  })
  added_by_user?: {
    id: string;
    name: string;
    email: string;
    rm_code: string;
  } | null;

  /**
   * Timestamp when the purpose was created
   */
  @ApiProperty({
    description: 'Timestamp when the purpose was created',
    example: '2025-07-31T10:30:00.000Z',
  })
  created_at: string;
}

/**
 * PurposeOfActivityListResponseDto
 * 
 * Response DTO for paginated list of purposes with metadata
 */
export class PurposeOfActivityListResponseDto {
  /**
   * Array of purpose of activity objects
   */
  @ApiProperty({
    description: 'Array of purpose of activity objects',
    type: [PurposeOfActivityResponseDto],
  })
  data: PurposeOfActivityResponseDto[];

  /**
   * Total number of purposes in the database
   */
  @ApiProperty({
    description: 'Total number of purposes in the database',
    example: 25,
  })
  total: number;

  /**
   * Number of purposes returned in this response
   */
  @ApiProperty({
    description: 'Number of purposes returned in this response',
    example: 10,
  })
  count: number;

  /**
   * Success message
   */
  @ApiProperty({
    description: 'Success message',
    example: 'Retrieved 10 purposes of activity successfully',
  })
  message: string;
}
