import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QueueService } from './queue.service';

/**
 * Queue Producer Module - Only for adding jobs to queues
 * Used in API container - does NOT include processors
 */
@Module({
  imports: [
    ConfigModule,
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        connection: {
          host: configService.get('QUEUE_REDIS_HOST') || 'redis',
          port: configService.get('QUEUE_REDIS_PORT') || 6379,
          password: configService.get('QUEUE_REDIS_PASSWORD') || undefined,
          db: configService.get('QUEUE_REDIS_DB') || 1,
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue(
      { name: 'email' },
      { name: 'reports' },
      { name: 'data-processing' },
      { name: 'scheduled-tasks' },
      { name: 'notifications' },
    ),
  ],
  providers: [QueueService],
  exports: [QueueService, BullModule],
})
export class QueueProducerModule {}
