import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { NotificationType, NotificationPriority } from '@prisma/client';
import { CreateNotificationDto } from '../dto/create-notification.dto';
import { NotificationsService } from '../notifications.service';

export interface CreateNotificationOptions {
  type: NotificationType;
  title: string;
  message: string;
  recipient_id: string;
  sender_id?: string;
  entity_type?: string;
  entity_id?: string;
  priority?: NotificationPriority;
  expires_at?: Date | string;
  data?: Record<string, any>;
}

@Injectable()
export class NotificationUtils {
  private readonly logger = new Logger(NotificationUtils.name);

  constructor(
    private readonly notificationsService: NotificationsService,
  ) {}

  /**
   * Create a notification with optional fields
   * @param options - Notification creation options
   * @returns Promise<NotificationResponseDto | null>
   */
  async createNotification(options: CreateNotificationOptions) {
    try {
      const createNotificationDto: CreateNotificationDto = {
        type: options.type,
        title: options.title,
        message: options.message,
        recipient_id: options.recipient_id,
        sender_id: options.sender_id,
        entity_type: options.entity_type,
        entity_id: options.entity_id,
        priority: options.priority || NotificationPriority.NORMAL,
        expires_at: options.expires_at ? new Date(options.expires_at).toISOString() : undefined,
        data: options.data,
      };

      this.logger.log(`Creating notification: ${options.title} for user ${options.recipient_id}`);
      return await this.notificationsService.create(createNotificationDto);
    } catch (error) {
      this.logger.error(`Failed to create notification: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create multiple notifications for different recipients
   * @param baseOptions - Base notification options
   * @param recipientIds - Array of recipient user IDs
   * @returns Promise<Array<NotificationResponseDto | null>>
   */
  async createBulkNotifications(
    baseOptions: Omit<CreateNotificationOptions, 'recipient_id'>,
    recipientIds: string[]
  ): Promise<Array<any>> {
    const notifications: Array<any> = [];

    for (const recipientId of recipientIds) {
      try {
        const notification = await this.createNotification({
          ...baseOptions,
          recipient_id: recipientId,
        });
        notifications.push(notification);
      } catch (error) {
        this.logger.error(`Failed to create notification for user ${recipientId}: ${error.message}`);
        notifications.push(null);
      }
    }

    return notifications;
  }

  /**
   * Create a follow-up reminder notification
   * @param followUpId - Follow-up ID
   * @param userId - User ID to notify
   * @param leadName - Name of the lead
   * @param followUpType - Type of follow-up (call/visit)
   * @param followUpTime - Scheduled follow-up time
   * @returns Promise<NotificationResponseDto | null>
   */
  async createFollowUpReminderNotification(
    followUpId: string,
    userId: string,
    leadName: string,
    followUpType: string,
    followUpTime: Date
  ) {
    const title = 'Upcoming Follow-Up Reminder';
    const message = `You have a ${followUpType === 'call' ? 'Call' : 'Visit'} with ${leadName} scheduled in 2 hours.`;

    return this.createNotification({
      type: NotificationType.FOLLOW_UP_DUE,
      title,
      message,
      recipient_id: userId,
      entity_type: 'follow_up',
      entity_id: followUpId,
      priority: NotificationPriority.HIGH,
      data: {
        followUpId,
        leadName,
        followUpType,
        scheduledTime: followUpTime.toISOString(),
      },
    });
  }

  /**
   * Create an overdue follow-up notification
   * @param userId - User ID to notify
   * @param leadName - Name of the lead
   * @param followUpType - Type of follow-up (call/visit)
   * @param scheduledTime - Originally scheduled time
   * @returns Promise<NotificationResponseDto | null>
   */
  async createOverdueFollowUpNotification(
    userId: string,
    leadName: string,
    followUpType: string,
    scheduledTime: Date
  ) {
    const title = 'Missed Follow-Up';
    const timeStr = scheduledTime.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
    
    const message = `You missed a ${followUpType === 'call' ? 'Call' : 'Visit'} with ${leadName} scheduled for ${timeStr}.`;

    return this.createNotification({
      type: NotificationType.FOLLOW_UP_OVERDUE,
      title,
      message,
      recipient_id: userId,
      entity_type: 'follow_up',
      priority: NotificationPriority.HIGH,
      data: {
        leadName,
        followUpType,
        scheduledTime: scheduledTime.toISOString(),
      },
    });
  }
}
