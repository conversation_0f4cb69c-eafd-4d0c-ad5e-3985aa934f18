import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { PrismaService } from '../../prisma/prisma.service';
import { OutboundCallService } from '../../voice/outbound-call.service';
import { TaskHandler } from './task-handler.interface';

export interface UpdateActivityCallDataPayload {
  activityId: string;
  phoneNumber: string;
}

@Injectable()
export class UpdateActivityCallDataHandler implements TaskHandler {
  private readonly logger = new Logger(UpdateActivityCallDataHandler.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly outboundCallService: OutboundCallService,
  ) {}

  getTaskType(): string {
    return 'update-activity-call-data';
  }

  getDescription(): string {
    return 'Updates activity records with call data from outbound call records by matching phone numbers';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payload) {
      errors.push('Payload is required');
      return { isValid: false, errors };
    }

    if (!payload.activityId || typeof payload.activityId !== 'string') {
      errors.push('activityId is required and must be a string');
    }

    if (!payload.phoneNumber || typeof payload.phoneNumber !== 'string') {
      errors.push('phoneNumber is required and must be a string');
    }

    return { isValid: errors.length === 0, errors };
  }

  async handle(payload: UpdateActivityCallDataPayload, job: Job): Promise<any> {
    this.logger.log(
      `Processing activity call data update for activity: ${payload.activityId}`,
    );

    try {
      // Find the activity
      const activity = await this.prisma.activity.findUnique({
        where: { id: payload.activityId },
      });

      if (!activity) {
        this.logger.warn(`Activity not found: ${payload.activityId}`);
        return {
          success: false,
          message: `Activity not found: ${payload.activityId}`,
        };
      }

      await job.updateProgress(30);

      // Find the latest outbound call record matching the phone number (last 8 digits)
      const outboundCall =
        await this.outboundCallService.findLatestByPhoneNumber(
          payload.phoneNumber,
        );

      if (!outboundCall) {
        this.logger.log(
          `No matching outbound call found for phone number: ${payload.phoneNumber}`,
        );
        return {
          success: false,
          message: `No matching outbound call found for phone number: ${payload.phoneNumber}`,
        };
      }

      await job.updateProgress(50);

      this.logger.log(
        `Found matching outbound call: ${outboundCall.id} for activity: ${payload.activityId}`,
      );

      // Prepare update data - only update fields that have values
      const updateData: any = {};

      if (outboundCall.sessionId) {
        updateData.session_id = outboundCall.sessionId;
      }

      if (outboundCall.recordingUrl) {
        updateData.recording_url = outboundCall.recordingUrl;
      }

      if (
        outboundCall.dialDurationInSeconds !== null &&
        outboundCall.dialDurationInSeconds !== undefined
      ) {
        updateData.call_duration_seconds = outboundCall.dialDurationInSeconds;
      }

      // Only update if we have data to update
      if (Object.keys(updateData).length > 0) {
        await this.prisma.activity.update({
          where: { id: payload.activityId },
          data: updateData,
        });

        this.logger.log(
          `Successfully updated activity ${payload.activityId} with call data:`,
          updateData,
        );
      } else {
        this.logger.log(
          `No call data available to update for activity: ${payload.activityId}`,
        );
      }

      await job.updateProgress(90);

      // Delete the outbound call record after successful update
      await this.outboundCallService.delete(outboundCall.id);
      this.logger.log(`Deleted outbound call record: ${outboundCall.id}`);

      await job.updateProgress(100);

      return {
        success: true,
        message: `Successfully updated activity ${payload.activityId} with call data`,
        updatedFields: Object.keys(updateData),
        outboundCallId: outboundCall.id,
      };
    } catch (error) {
      this.logger.error(
        `Failed to update activity call data for ${payload.activityId}:`,
        error,
      );
      throw error; // Re-throw to trigger retry mechanism
    }
  }

  /**
   * Helper method to extract last 8 digits from phone number
   */
  private extractLast8Digits(phoneNumber: string): string {
    return phoneNumber.replace(/\D/g, '').slice(-8);
  }

  /**
   * Helper method to compare phone numbers by last 8 digits
   */
  private phoneNumbersMatch(phone1: string, phone2: string): boolean {
    const digits1 = this.extractLast8Digits(phone1);
    const digits2 = this.extractLast8Digits(phone2);

    return digits1.length >= 8 && digits2.length >= 8 && digits1 === digits2;
  }
}
