import {
  Controller,
  Get,
  Patch,
  Body,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
  ApiNotFoundResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ProfileService } from './profile.service';
import { ProfileResponseDto } from './dto/profile-response.dto';

@ApiTags('profile')
@Controller('profile')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  /**
   * Gets the profile of the logged-in user
   * GET /profile/me
   */
  @Get('me')
  @ApiOperation({
    summary: 'Get current user profile',
    description:
      'Retrieves the profile information of the currently authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Profile retrieved successfully',
    type: ProfileResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
  })
  async getMyProfile(@Request() req: any): Promise<ProfileResponseDto> {
    return this.profileService.getMyProfile(req.user.id);
  }

  /**
   * Updates the profile of the logged-in user
   * PATCH /profile/me
   */
  @Patch('me')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('profile_photo'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update current user profile',
    description: `
      Updates the profile information of the currently authenticated user.
      Only the fields provided will be updated. All fields are optional.
    `,
  })
  @ApiBody({
    description: 'Profile update data with optional profile photo',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'John Doe',
          description: 'User name (optional)',
        },
        email: {
          type: 'string',
          example: '<EMAIL>',
          description: 'User email (optional)',
        },
        phone_number: {
          type: 'string',
          example: '+254700123456',
          description: 'User phone number (optional)',
        },
        profile_photo: {
          type: 'string',
          format: 'binary',
          description: 'Profile photo file (optional)',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Profile updated successfully',
    type: ProfileResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
  })
  async updateMyProfile(
    @Request() req: any,
    @Body() updateData: any, // Use any for form data
    @UploadedFile() profilePhoto?: Express.Multer.File,
  ): Promise<ProfileResponseDto> {
    return this.profileService.updateMyProfile(
      req.user.id,
      updateData,
      profilePhoto,
    );
  }
}
