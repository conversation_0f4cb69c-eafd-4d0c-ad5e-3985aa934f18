import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { InboundCallDto } from './dto/inbound-call.dto';

@Injectable()
export class InboundCallService {
  private readonly logger = new Logger(InboundCallService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create inbound call activity
   * @param callData - Inbound call data from Africa's Talking
   * @returns Created activity
   */
  async createInboundCallActivity(callData: InboundCallDto) {
    console.log("Hello from inbound call service");
    console.log(callData);
    this.logger.log(`Creating inbound call activity for session ${callData.sessionId}`);
    
    try {
      const lead = await this.findLeadByPhone(callData.callerNumber);
      
      if (!lead) {
        this.logger.warn(`No lead found for phone number ${callData.callerNumber}`);
        return null;
      }
      // Get default user for the activity (fallback)
      const defaultUser = await this.getDefaultUser();

      // Determine the user to associate with this activity
      // Priority: 1. RM user (rm_user_id), 2. Assigned user, 3. Default user
      let performedByUserId = lead.rm_user_id || lead.assigned_user || defaultUser.id;
      
      // If using RM user or assigned user, verify they exist and have phone number
      if (lead.rm_user_id || lead.assigned_user) {
        const user = await this.prisma.user.findUnique({
          where: { id: performedByUserId },
          select: { id: true, phone_number: true }
        });
        
        // If user doesn't exist or has no phone number, fall back to default user
        if (!user || !user.phone_number) {
          this.logger.warn(`User ${performedByUserId} not found or has no phone number, using default user`);
          performedByUserId = defaultUser.id;
        }
      }

      // Create activity for inbound call attempt
      const activity = await this.prisma.activity.create({
        data: {
          lead_id: lead.id,
          activity_type: 'inbound_call',
          interaction_type: 'call',
          call_direction: 'INBOUND',
          call_status: 'ringing', // Will be updated based on call outcome
          session_id: callData.sessionId,
          at_call_id: callData.callId,
          performed_by_user_id: performedByUserId,
          branch_id: lead.branch_id || defaultUser.branch_id,
          notes: `Inbound call received from ${callData.callerNumber}`,
        }
      });

      this.logger.log(`Created inbound call activity ${activity.id} for lead ${lead.id}`);
      return activity;
    } catch (error) {
      this.logger.error(`Error creating inbound call activity: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update call status and related information
   * @param sessionId - Africa's Talking session ID
   * @param status - Call status
   * @param duration - Call duration in seconds
   * @param recordingUrl - URL to call recording
   * @param callId - Africa's Talking call ID
   */
  async updateCallStatus(
    sessionId: string, 
    status: string, 
    duration?: string, 
    recordingUrl?: string,
    callId?: string
  ) {
    this.logger.log(`Updating call status for session ${sessionId} to ${status}`);
    
    try {
      let activityStatus: string;
      
      switch (status) {
        case 'answered':
          activityStatus = 'success';
          break;
        case 'no-answer':
        case 'busy':
          activityStatus = 'missed';
          break;
        case 'failed':
          activityStatus = 'failed';
          break;
        case 'Aborted':
          activityStatus = 'aborted';
          break;
        case 'Completed':
          activityStatus = 'completed';
          break;
        default:
          activityStatus = 'completed';
      }

      const updateData: any = {
        call_status: activityStatus,
        updated_at: new Date()
      };

      if (duration) {
        updateData.call_duration_seconds = parseInt(duration);
      }

      if (recordingUrl) {
        updateData.recording_url = recordingUrl;
      }

      if (callId) {
        updateData.at_call_id = callId;
      }

      await this.prisma.activity.updateMany({
        where: { session_id: sessionId },
        data: updateData
      });

      this.logger.log(`Updated call status for session ${sessionId} to ${activityStatus}`);
    } catch (error) {
      this.logger.error(`Error updating call status: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update recording URL when recording is available
   * @param sessionId - Call session ID
   * @param recordingUrl - Recording URL
   * @param callId - Africa's Talking call ID
   */
  async updateRecording(sessionId: string, recordingUrl: string, callId?: string) {
    this.logger.log(`Updating recording for session ${sessionId}: ${recordingUrl}`);
    
    try {
      const updateData: any = {
        recording_url: recordingUrl,
        updated_at: new Date()
      };

      if (callId) {
        updateData.at_call_id = callId;
      }

      await this.prisma.activity.updateMany({
        where: { session_id: sessionId },
        data: updateData
      });

      this.logger.log(`Updated recording for session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Error updating recording: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find lead by phone number
   * @param phoneNumber - Phone number to search for
   * @returns Lead or null
   */
  private async findLeadByPhone(phoneNumber: string) {
    try {
      // Normalize phone number
      const normalizedPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');
      
      return await this.prisma.lead.findFirst({
        where: {
          phone_number: {
            contains: normalizedPhone,
          },
        },
      });
    } catch (error) {
      this.logger.error(`Error finding lead by phone ${phoneNumber}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get default user for activities
   * @returns Default user
   */
  private async getDefaultUser() {
    try {
      // Try to get first available user
      const user = await this.prisma.user.findFirst({
        where: {
          phone_number: {
            not: null,
          },
        },
      });

      if (!user) {
        throw new NotFoundException('No users found in the system');
      }

      return user;
    } catch (error) {
      this.logger.error(`Error getting default user: ${error.message}`);
      throw error;
    }
  }
}
