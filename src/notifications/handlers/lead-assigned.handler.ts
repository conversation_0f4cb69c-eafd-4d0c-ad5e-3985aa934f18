import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { NotificationHandler, NotificationContext } from './notification.handler.interface';
import { NotificationType, NotificationPriority } from '@prisma/client';

@Injectable()
export class LeadAssignedHandler implements NotificationHandler {
  private readonly logger = new Logger(LeadAssignedHandler.name);
  readonly type = NotificationType.LEAD_ASSIGNED;

  constructor(private readonly prisma: PrismaService) {}

  shouldHandle(context: NotificationContext): boolean {
    return context.entityType === 'lead' && context.entityData?.assignedUserId;
  }

  async generateNotification(context: NotificationContext): Promise<{
    title: string;
    message: string;
    priority: NotificationPriority;
    data?: Record<string, any>;
    recipientIds: string[];
  }> {
    const { entityId, entityData, triggerUserId } = context;
    
    try {
      // Get lead details
      const lead = await this.prisma.lead.findUnique({
        where: { id: entityId },
        include: {
          customer_category: true,
          branch: true,
        },
      });

      if (!lead) {
        this.logger.warn(`Lead ${entityId} not found for notification`);
        return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
      }

      // Check if lead has an assigned RM user
      if (!lead.rm_user_id) {
        this.logger.warn(`No assigned RM user found for lead ${entityId}`);
        return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
      }

      // Get the assigned user details
      const assignedUser = await this.prisma.user.findUnique({
        where: { id: lead.rm_user_id },
        select: {
          id: true,
          name: true,
          email: true,
          rm_code: true,
        },
      });

      if (!assignedUser) {
        this.logger.warn(`Assigned user with ID ${lead.rm_user_id} not found for lead ${entityId}`);
        return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
      }

      // Get the user who assigned the lead (trigger user)
      let assignedByUser: { id: string; name: string; email: string; rm_code: string } | null = null;
      if (triggerUserId) {
        assignedByUser = await this.prisma.user.findUnique({
          where: { id: triggerUserId },
          select: {
            id: true,
            name: true,
            email: true,
            rm_code: true,
          },
        });
      }

      const title = 'New Lead Assigned';
      const message = `You have been assigned a new lead: ${lead.customer_name || 'Unknown Customer'}`;
      
      const data = {
        leadId: lead.id,
        customerName: lead.customer_name,
        customerCategory: lead.customer_category?.name,
        branchName: lead.branch?.name,
        assignedBy: assignedByUser?.name || 'System',
        assignedByRmCode: assignedByUser?.rm_code,
        phoneNumber: lead.phone_number,
        leadStatus: lead.lead_status,
        createdAt: lead.created_at,
      };

      return {
        title,
        message,
        priority: NotificationPriority.NORMAL,
        data,
        recipientIds: [assignedUser.id],
      };

    } catch (error) {
      this.logger.error(`Failed to generate lead assigned notification: ${error.message}`, error.stack);
      return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
    }
  }
}