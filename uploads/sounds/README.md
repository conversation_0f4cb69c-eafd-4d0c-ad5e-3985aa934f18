# Sounds Directory

This directory contains audio files used by the Africa's Talking voice integration.

## Files

### ringback.wav
This is the ringback tone that plays while a call is being connected. 

**Requirements:**
- Format: WAV, MP3, or other supported audio formats
- Duration: Typically 3-10 seconds (will loop if needed)
- Quality: Clear audio, appropriate volume level
- Content: Professional ringback tone or music

**Usage:**
The `ringback.wav` file is referenced in the XML response sent to Africa's Talking when handling voice callbacks. It plays while the call is being connected to provide a better user experience.

## Adding New Audio Files

1. Place audio files in this directory
2. Update the voice service to reference the new files in XML responses
3. Ensure files are accessible via the application's static file serving

## Notes

- Audio files should be optimized for telephony (8kHz or 16kHz sample rate recommended)
- Keep file sizes reasonable to ensure quick loading
- Test audio quality over phone calls, not just computer speakers
- Consider different audio formats for compatibility

## Current Status

**ringback.wav**: Placeholder file - replace with actual ringback tone audio file
