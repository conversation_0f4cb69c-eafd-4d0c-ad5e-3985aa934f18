import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UseGuards,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { BranchesService } from './branches.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { BranchResponseDto } from './dto/branch-response.dto';
import { BranchUsersResponseDto } from './dto/branch-users.dto';
import {
  PaginationDto,
  PaginatedResponseDto,
} from '../common/dto/pagination.dto';

/**
 * Controller handling all branch-related HTTP endpoints
 * Provides RESTful API for branch management
 */
@ApiTags('Branches')
@Controller('branches')
export class BranchesController {
  constructor(private readonly branchesService: BranchesService) {}

  /**
   * Creates a new branch
   * POST /branches
   */
  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new branch',
    description:
      'Creates a new branch in a specific region. Branch names must be unique within each region.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Branch created successfully',
    type: BranchResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Region not found' })
  @ApiConflictResponse({
    description: 'Branch name already exists in this region',
  })
  async create(
    @Body(ValidationPipe) createBranchDto: CreateBranchDto,
    @Req() req: any,
  ): Promise<BranchResponseDto> {
    return this.branchesService.create(createBranchDto, req.user.id);
  }

  /**
   * Retrieves all branches with pagination and search
   * GET /branches
   */
  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all branches',
    description: `
      Retrieves a paginated list of branches with optional search functionality.
      Search works on both branch and region names.

      Permission-based filtering (when usePermission=true):
      - users.view.all.branches: Returns all branches
      - users.view.my.branch: Returns only user's branch
      - No permissions: Returns all branches (fallback)
    `,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for branch or region names',
  })
  @ApiQuery({
    name: 'usePermission',
    required: false,
    type: Boolean,
    description: 'Apply permission-based filtering (default: false)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Branches retrieved successfully',
    type: PaginatedResponseDto<BranchResponseDto>,
  })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto,
    @Query('usePermission') usePermission?: string,
    @Req() req?: any,
  ): Promise<PaginatedResponseDto<BranchResponseDto>> {
    const shouldUsePermission = usePermission === 'true';
    return this.branchesService.findAll(
      paginationDto,
      shouldUsePermission,
      req?.user?.permissions || [],
      req?.user?.branch_id,
    );
  }

  /**
   * Retrieves a single branch by ID
   * GET /branches/:id
   */
  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get branch by ID',
    description:
      'Retrieves a single branch by its UUID. Includes region information.',
  })
  @ApiParam({ name: 'id', description: 'Branch UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Branch retrieved successfully',
    type: BranchResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Branch not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<BranchResponseDto> {
    return this.branchesService.findOne(id);
  }

  /**
   * Updates an existing branch
   * PATCH /branches/:id
   */
  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update branch',
    description:
      'Updates an existing branch. Can change name and/or region. Branch names must remain unique within each region.',
  })
  @ApiParam({ name: 'id', description: 'Branch UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Branch updated successfully',
    type: BranchResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Branch or region not found' })
  @ApiConflictResponse({
    description: 'Branch name already exists in the target region',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateBranchDto: UpdateBranchDto,
  ): Promise<BranchResponseDto> {
    return this.branchesService.update(id, updateBranchDto);
  }
  /**
   * Deletes a branch
   * DELETE /branches/:id
   */
  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete branch',
    description:
      'Deletes a branch by ID. Cannot delete branches that have associated users or leads.',
  })
  @ApiParam({ name: 'id', description: 'Branch UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Branch deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Branch not found' })
  @ApiConflictResponse({
    description: 'Branch has associated users or leads and cannot be deleted',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.branchesService.remove(id);
  }

  /**
   * Retrieves all users in a specific branch
   * GET /branches/:id/users
   */
  @Get(':id/users')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get users by branch',
    description:
      'Retrieves all users assigned to a specific branch. Returns only user ID and name for each user.',
  })
  @ApiParam({ name: 'id', description: 'Branch UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Users retrieved successfully',
    type: BranchUsersResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Branch not found' })
  async getUsersByBranch(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<BranchUsersResponseDto> {
    return this.branchesService.getUsersByBranch(id);
  }
}
