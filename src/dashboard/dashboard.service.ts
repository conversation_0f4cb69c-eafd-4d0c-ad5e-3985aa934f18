import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { BranchManagerDashboardDto } from './dto/branch-manager-dashboard.dto';
import { CustomerServiceDashboardDto } from './dto/customer-service-dashboard.dto';
import {
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  getDaysInMonth,
  subMonths,
  startOfDay,
  endOfDay,
} from 'date-fns';

/**
 * Service handling dashboard-related business logic
 */
@Injectable()
export class DashboardService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Gets branch manager dashboard data
   * @param branchId - Branch ID of the logged-in user
   * @param section - Dashboard section to return (first, second, third)
   * @returns Promise<BranchManagerDashboardDto> - Dashboard data
   */
  async getBranchManagerDashboard(
    branchId: string,
    section: string = 'first',
  ): Promise<BranchManagerDashboardDto> {
    if (section === 'second') {
      const now = new Date();
      const startOfCurrentMonth = startOfMonth(now);
      const endOfCurrentMonth = endOfMonth(now);

      // Get second section data
      const monthlyHitlistProgress = await this.getMonthlyHitlistProgress(
        branchId,
        startOfCurrentMonth,
        endOfCurrentMonth,
      );
      const callsVisitsVsTarget = await this.getCallsVisitsVsTarget(
        branchId,
        startOfCurrentMonth,
        endOfCurrentMonth,
      );
      const callsVsTargetsPerOfficer = await this.getCallsVsTargetsPerOfficer(
        branchId,
        startOfCurrentMonth,
        endOfCurrentMonth,
      );
      const visitsVsTargetsPerOfficer = await this.getVisitsVsTargetsPerOfficer(
        branchId,
        startOfCurrentMonth,
        endOfCurrentMonth,
      );

      return {
        monthly_hitlist_progress: monthlyHitlistProgress,
        calls_visits_vs_target: callsVisitsVsTarget,
        calls_vs_targets_per_officer: callsVsTargetsPerOfficer,
        visits_vs_targets_per_officer: visitsVsTargetsPerOfficer,
      };
    }

    if (section === 'third') {
      // Return empty object for third section for now
      return {} as any;
    }

    // Default to first section
    const now = new Date();
    const startOfCurrentMonth = startOfMonth(now);
    const endOfCurrentMonth = endOfMonth(now);

    // Summary cards data
    const summaryCards = await this.getSummaryCards(
      branchId,
      startOfCurrentMonth,
      endOfCurrentMonth,
    );

    // Branch conversion trend data
    const branchConversionTrend = await this.getBranchConversionTrend(branchId);

    // Calls and visits data
    const callsAndVisits = await this.getCallsAndVisits(
      branchId,
      startOfCurrentMonth,
      endOfCurrentMonth,
    );

    // Dormant records data
    const dormantRecords = await this.getDormantRecords(
      branchId,
      startOfCurrentMonth,
      endOfCurrentMonth,
    );

    return {
      summary_cards: summaryCards,
      branch_conversion_trend: branchConversionTrend,
      calls_and_visits: callsAndVisits,
      dormant_records: dormantRecords,
    };
  }

  /**
   * Gets summary cards data
   * @param branchId - Branch ID
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @returns Promise<any> - Summary cards data
   */
  private async getSummaryCards(
    branchId: string,
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
  ) {
    // Hitlist size (all leads in branch)
    const [hitlistSizeAll, hitlistSizeThisMonth] = await Promise.all([
      this.prisma.lead.count({
        where: { branch_id: branchId },
      }),
      this.prisma.lead.count({
        where: {
          branch_id: branchId,
          created_at: {
            gte: startOfCurrentMonth,
            lte: endOfCurrentMonth,
          },
        },
      }),
    ]);

    // Converted customers (leads with account_number)
    const [convertedCustomersAll, convertedCustomersThisMonth] =
      await Promise.all([
        this.prisma.lead.count({
          where: {
            branch_id: branchId,
            account_number: { not: null },
          },
        }),
        this.prisma.lead.count({
          where: {
            branch_id: branchId,
            account_number: { not: null },
            account_number_assigned_at: {
              gte: startOfCurrentMonth,
              lte: endOfCurrentMonth,
            },
          },
        }),
      ]);

    // Calls completed (activities with interaction_type "call")
    const [callsCompletedAll, callsCompletedThisMonth] = await Promise.all([
      this.prisma.activity.count({
        where: {
          interaction_type: 'call',
          performed_by: {
            branch_id: branchId,
          },
        },
      }),
      this.prisma.activity.count({
        where: {
          interaction_type: 'call',
          performed_by: {
            branch_id: branchId,
          },
          created_at: {
            gte: startOfCurrentMonth,
            lte: endOfCurrentMonth,
          },
        },
      }),
    ]);

    // Visits (activities with interaction_type "visit")
    const [visitsAll, visitsThisMonth] = await Promise.all([
      this.prisma.activity.count({
        where: {
          interaction_type: 'visit',
          performed_by: {
            branch_id: branchId,
          },
        },
      }),
      this.prisma.activity.count({
        where: {
          interaction_type: 'visit',
          performed_by: {
            branch_id: branchId,
          },
          created_at: {
            gte: startOfCurrentMonth,
            lte: endOfCurrentMonth,
          },
        },
      }),
    ]);

    return {
      hitlist_size: {
        all: hitlistSizeAll,
        this_month: hitlistSizeThisMonth,
      },
      converted_customers: {
        all: convertedCustomersAll,
        this_month: convertedCustomersThisMonth,
      },
      calls_completed: {
        all: callsCompletedAll,
        this_month: callsCompletedThisMonth,
      },
      visits: {
        all: visitsAll,
        this_month: visitsThisMonth,
      },
    };
  }

  /**
   * Gets branch conversion trend data by year and month
   * @param branchId - Branch ID
   * @returns Promise<any> - Conversion trend data
   */
  private async getBranchConversionTrend(branchId: string) {
    // Get all leads with account_number_assigned_at for the branch
    const conversions = await this.prisma.lead.findMany({
      where: {
        branch_id: branchId,
        account_number: { not: null },
        account_number_assigned_at: { not: null },
      },
      select: {
        account_number_assigned_at: true,
      },
    });

    // Group conversions by year and month
    const conversionsByYearMonth: Record<number, Record<number, number>> = {};

    conversions.forEach((conversion) => {
      if (conversion.account_number_assigned_at) {
        const year = conversion.account_number_assigned_at.getFullYear();
        const month = conversion.account_number_assigned_at.getMonth(); // 0-11

        if (!conversionsByYearMonth[year]) {
          conversionsByYearMonth[year] = {};
        }

        if (!conversionsByYearMonth[year][month]) {
          conversionsByYearMonth[year][month] = 0;
        }

        conversionsByYearMonth[year][month]++;
      }
    });

    // Format the data as required
    const trendData: Record<string, (number | null)[]> = {};

    // If no data, return current year with all nulls
    if (Object.keys(conversionsByYearMonth).length === 0) {
      const currentYear = new Date().getFullYear();
      trendData[currentYear.toString()] = Array(12).fill(null);
    } else {
      Object.keys(conversionsByYearMonth).forEach((yearStr) => {
        const year = parseInt(yearStr);
        const monthlyData: (number | null)[] = [];

        for (let month = 0; month < 12; month++) {
          const conversionCount = conversionsByYearMonth[year][month];
          monthlyData.push(conversionCount || null);
        }

        trendData[year.toString()] = monthlyData;
      });
    }

    return trendData;
  }

  /**
   * Gets calls and visits data
   * @param branchId - Branch ID
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @returns Promise<any> - Calls and visits data
   */
  private async getCallsAndVisits(
    branchId: string,
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
  ) {
    // Total data
    const [
      leadsCallsTotal,
      leadsVisitsTotal,
      customerServiceCallsTotal,
      customerServiceVisitsTotal,
    ] = await Promise.all([
      // Leads calls (with lead_id not null)
      this.prisma.activity.count({
        where: {
          interaction_type: 'call',
          lead_id: { not: null },
          performed_by: {
            branch_id: branchId,
          },
        },
      }),
      // Leads visits (with lead_id not null)
      this.prisma.activity.count({
        where: {
          interaction_type: 'visit',
          lead_id: { not: null },
          performed_by: {
            branch_id: branchId,
          },
        },
      }),
      // Customer service calls (with customer_service_hitlist_record_id not null)
      this.prisma.activity.count({
        where: {
          interaction_type: 'call',
          customer_service_hitlist_record_id: { not: null },
          performed_by: {
            branch_id: branchId,
          },
        },
      }),
      // Customer service visits (with customer_service_hitlist_record_id not null)
      this.prisma.activity.count({
        where: {
          interaction_type: 'visit',
          customer_service_hitlist_record_id: { not: null },
          performed_by: {
            branch_id: branchId,
          },
        },
      }),
    ]);

    // This month data
    const [
      leadsCallsThisMonth,
      leadsVisitsThisMonth,
      customerServiceCallsThisMonth,
      customerServiceVisitsThisMonth,
    ] = await Promise.all([
      // Leads calls this month
      this.prisma.activity.count({
        where: {
          interaction_type: 'call',
          lead_id: { not: null },
          performed_by: {
            branch_id: branchId,
          },
          created_at: {
            gte: startOfCurrentMonth,
            lte: endOfCurrentMonth,
          },
        },
      }),
      // Leads visits this month
      this.prisma.activity.count({
        where: {
          interaction_type: 'visit',
          lead_id: { not: null },
          performed_by: {
            branch_id: branchId,
          },
          created_at: {
            gte: startOfCurrentMonth,
            lte: endOfCurrentMonth,
          },
        },
      }),
      // Customer service calls this month
      this.prisma.activity.count({
        where: {
          interaction_type: 'call',
          customer_service_hitlist_record_id: { not: null },
          performed_by: {
            branch_id: branchId,
          },
          created_at: {
            gte: startOfCurrentMonth,
            lte: endOfCurrentMonth,
          },
        },
      }),
      // Customer service visits this month
      this.prisma.activity.count({
        where: {
          interaction_type: 'visit',
          customer_service_hitlist_record_id: { not: null },
          performed_by: {
            branch_id: branchId,
          },
          created_at: {
            gte: startOfCurrentMonth,
            lte: endOfCurrentMonth,
          },
        },
      }),
    ]);

    return {
      total: {
        leads: {
          calls: leadsCallsTotal,
          visits: leadsVisitsTotal,
        },
        customer_service: {
          calls: customerServiceCallsTotal,
          visits: customerServiceVisitsTotal,
        },
        loans: {
          calls: 0,
          visits: 0,
        },
      },
      this_month: {
        leads: {
          calls: leadsCallsThisMonth,
          visits: leadsVisitsThisMonth,
        },
        customer_service: {
          calls: customerServiceCallsThisMonth,
          visits: customerServiceVisitsThisMonth,
        },
        loans: {
          calls: 0,
          visits: 0,
        },
      },
    };
  }

  /**
   * Gets dormant records data
   * @param branchId - Branch ID
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @returns Promise<any> - Dormant records data
   */
  private async getDormantRecords(
    branchId: string,
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
  ) {
    // Get all dormancy hitlist records for the branch
    const dormancyRecords =
      await this.prisma.customerServiceHitlistRecord.findMany({
        where: {
          customer_service_hitlist: {
            type: 'Dormancy',
            uploader: {
              branch_id: branchId,
            },
          },
        },
        select: {
          id: true,
          activities: {
            where: {
              interaction_type: 'call',
            },
            select: {
              id: true,
              created_at: true,
            },
          },
        },
      });

    // Calculate total counts
    let totalUncontacted = 0;
    let totalContacted = 0;
    let thisMonthUncontacted = 0;
    let thisMonthContacted = 0;

    dormancyRecords.forEach((record) => {
      const hasActivities = record.activities.length > 0;
      const hasThisMonthActivities = record.activities.some(
        (activity) =>
          activity.created_at >= startOfCurrentMonth &&
          activity.created_at <= endOfCurrentMonth,
      );

      if (hasActivities) {
        totalContacted++;
      } else {
        totalUncontacted++;
      }

      if (hasThisMonthActivities) {
        thisMonthContacted++;
      } else {
        thisMonthUncontacted++;
      }
    });

    return {
      total: {
        uncontacted: totalUncontacted,
        contacted: totalContacted,
      },
      this_month: {
        uncontacted: thisMonthUncontacted,
        contacted: thisMonthContacted,
      },
    };
  }

  /**
   * Gets monthly hitlist progress data
   * @param branchId - Branch ID
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @returns Promise<any> - Monthly hitlist progress data
   */
  private async getMonthlyHitlistProgress(
    branchId: string,
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
  ) {
    // Get all leads created this month in the branch
    const leadsThisMonth = await this.prisma.lead.findMany({
      where: {
        branch_id: branchId,
        created_at: {
          gte: startOfCurrentMonth,
          lte: endOfCurrentMonth,
        },
      },
      select: {
        id: true,
        activities: {
          select: {
            id: true,
          },
        },
      },
    });

    let contacted = 0;
    let remaining = 0;

    leadsThisMonth.forEach((lead) => {
      if (lead.activities.length > 0) {
        contacted++;
      } else {
        remaining++;
      }
    });

    return {
      contacted,
      remaining,
    };
  }

  /**
   * Gets calls and visits vs target data
   * @param branchId - Branch ID
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @returns Promise<any> - Calls and visits vs target data
   */
  private async getCallsVisitsVsTarget(
    branchId: string,
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
  ) {
    // Get actual calls and visits from TargetProgress
    const actualProgress = await this.prisma.targetProgress.findMany({
      where: {
        for_date: {
          gte: startOfCurrentMonth,
          lte: endOfCurrentMonth,
        },
        target: {
          branch_id: branchId,
          metric_type: {
            in: ['Call', 'Visit'],
          },
        },
      },
      include: {
        target: true,
      },
    });

    let actualCalls = 0;
    let actualVisits = 0;

    actualProgress.forEach((progress) => {
      if (progress.target.metric_type === 'Call') {
        actualCalls += progress.achieved_count;
      } else if (progress.target.metric_type === 'Visit') {
        actualVisits += progress.achieved_count;
      }
    });

    // Get target calls and visits
    const targets = await this.prisma.target.findMany({
      where: {
        branch_id: branchId,
        metric_type: {
          in: ['Call', 'Visit'],
        },
        OR: [
          {
            start_date: {
              lte: endOfCurrentMonth,
            },
            end_date: {
              gte: startOfCurrentMonth,
            },
          },
          {
            start_date: {
              lte: endOfCurrentMonth,
            },
            end_date: null,
          },
        ],
      },
    });

    let targetCalls = 0;
    let targetVisits = 0;

    const today = new Date();
    targets.forEach((target) => {
      const targetDays = this.calculateTargetDaysFromStartToToday(
        target,
        startOfCurrentMonth,
        today,
      );
      const targetValue = targetDays * target.target_value;

      if (target.metric_type === 'Call') {
        targetCalls += targetValue;
      } else if (target.metric_type === 'Visit') {
        targetVisits += targetValue;
      }
    });

    return {
      actual: {
        name: 'Actual',
        data: [actualCalls, actualVisits],
      },
      target: {
        name: 'Target',
        data: [targetCalls, targetVisits],
      },
    };
  }

  /**
   * Gets calls vs targets per officer data
   * @param branchId - Branch ID
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @returns Promise<any> - Calls vs targets per officer data
   */
  private async getCallsVsTargetsPerOfficer(
    branchId: string,
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
  ) {
    // Get all users in the branch
    const users = await this.prisma.user.findMany({
      where: {
        branch_id: branchId,
      },
      select: {
        id: true,
        name: true,
      },
    });

    const result: Array<{
      user_name: string;
      calls_made: number;
      calls_target: number;
    }> = [];

    const today = new Date();

    for (const user of users) {
      // Get calls made by this user - only TargetProgress records between start of month and today
      const callsProgress = await this.prisma.targetProgress.findMany({
        where: {
          user_id: user.id,
          for_date: {
            gte: startOfCurrentMonth,
            lte: today,
          },
          target: {
            branch_id: branchId,
            metric_type: 'Call',
          },
        },
        include: {
          target: true,
        },
      });

      const callsMade = callsProgress.reduce(
        (sum, progress) => sum + progress.achieved_count,
        0,
      );

      // Get call targets for this user
      const callTargets = await this.prisma.target.findMany({
        where: {
          user_id: user.id,
          branch_id: branchId,
          metric_type: 'Call',
          OR: [
            {
              start_date: {
                lte: today,
              },
              end_date: {
                gte: startOfCurrentMonth,
              },
            },
            {
              start_date: {
                lte: today,
              },
              end_date: null,
            },
          ],
        },
      });

      let callsTarget = 0;
      callTargets.forEach((target) => {
        const targetDays = this.calculateTargetDaysFromStartToToday(
          target,
          startOfCurrentMonth,
          today,
        );
        callsTarget += targetDays * target.target_value;
      });

      result.push({
        user_name: user.name,
        calls_made: callsMade,
        calls_target: callsTarget,
      });
    }

    return result;
  }

  /**
   * Gets visits vs targets per officer data
   * @param branchId - Branch ID
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @returns Promise<any> - Visits vs targets per officer data
   */
  private async getVisitsVsTargetsPerOfficer(
    branchId: string,
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
  ) {
    // Get all users in the branch
    const users = await this.prisma.user.findMany({
      where: {
        branch_id: branchId,
      },
      select: {
        id: true,
        name: true,
      },
    });

    const result: Array<{
      user_name: string;
      visits_made: number;
      visits_target: number;
    }> = [];

    const today = new Date();

    for (const user of users) {
      // Get visits made by this user - only TargetProgress records between start of month and today
      const visitsProgress = await this.prisma.targetProgress.findMany({
        where: {
          user_id: user.id,
          for_date: {
            gte: startOfCurrentMonth,
            lte: today,
          },
          target: {
            branch_id: branchId,
            metric_type: 'Visit',
          },
        },
        include: {
          target: true,
        },
      });

      const visitsMade = visitsProgress.reduce(
        (sum, progress) => sum + progress.achieved_count,
        0,
      );

      // Get visit targets for this user
      const visitTargets = await this.prisma.target.findMany({
        where: {
          user_id: user.id,
          branch_id: branchId,
          metric_type: 'Visit',
          OR: [
            {
              start_date: {
                lte: today,
              },
              end_date: {
                gte: startOfCurrentMonth,
              },
            },
            {
              start_date: {
                lte: today,
              },
              end_date: null,
            },
          ],
        },
      });

      let visitsTarget = 0;
      visitTargets.forEach((target) => {
        const targetDays = this.calculateTargetDaysFromStartToToday(
          target,
          startOfCurrentMonth,
          today,
        );
        visitsTarget += targetDays * target.target_value;
      });

      result.push({
        user_name: user.name,
        visits_made: visitsMade,
        visits_target: visitsTarget,
      });
    }

    return result;
  }

  /**
   * Calculates the number of target days from start of month to today for a given target
   * @param target - Target object
   * @param startOfCurrentMonth - Start of current month
   * @param today - Today's date
   * @returns number - Number of target days from start of month to today
   */
  private calculateTargetDaysFromStartToToday(
    target: any,
    startOfCurrentMonth: Date,
    today: Date,
  ): number {
    const targetStartDate = new Date(target.start_date);
    const targetEndDate = target.end_date ? new Date(target.end_date) : null;

    // Determine the effective start and end dates within the period from start of month to today
    const effectiveStartDate =
      targetStartDate > startOfCurrentMonth
        ? targetStartDate
        : startOfCurrentMonth;
    const effectiveEndDate =
      targetEndDate && targetEndDate < today ? targetEndDate : today;

    // Calculate the number of days
    const timeDiff = effectiveEndDate.getTime() - effectiveStartDate.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 to include both start and end dates

    return Math.max(0, daysDiff);
  }

  /**
   * Calculates the number of target days in the full month for a given target
   * @param target - Target object
   * @param startOfMonth - Start of month
   * @param endOfMonth - End of month
   * @returns number - Number of target days in the month
   */
  private calculateTargetDaysInFullMonth(
    target: any,
    startOfMonth: Date,
    endOfMonth: Date,
  ): number {
    const targetStartDate = new Date(target.start_date);
    const targetEndDate = target.end_date ? new Date(target.end_date) : null;

    // Determine the effective start and end dates within the month
    const effectiveStartDate =
      targetStartDate > startOfMonth ? targetStartDate : startOfMonth;
    const effectiveEndDate =
      targetEndDate && targetEndDate < endOfMonth ? targetEndDate : endOfMonth;

    // Calculate the number of days
    const timeDiff = effectiveEndDate.getTime() - effectiveStartDate.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 to include both start and end dates

    return Math.max(0, daysDiff);
  }

  /**
   * Gets customer service dashboard data
   * @param user - Logged-in user object
   * @param officer - Officer type (cxo)
   * @returns Promise<CustomerServiceDashboardDto> - Customer service dashboard data
   */
  async getCustomerServiceDashboard(
    user: any,
    officer: string = 'cxo',
  ): Promise<CustomerServiceDashboardDto> {
    if (officer === 'cxo') {
      const now = new Date();
      const startOfCurrentMonth = startOfMonth(now);
      const endOfCurrentMonth = endOfMonth(now);
      const startOfLastMonth = startOfMonth(subMonths(now, 1));
      const endOfLastMonth = endOfMonth(subMonths(now, 1));
      const today = startOfDay(now);

      // Get summary cards data
      const summaryCards = await this.getCustomerServiceSummaryCards(
        user.branch_id,
        startOfCurrentMonth,
        endOfCurrentMonth,
        today,
      );

      // Get personal metrics
      const personal = await this.getCustomerServicePersonalMetrics(
        user,
        startOfCurrentMonth,
        endOfCurrentMonth,
        startOfLastMonth,
        endOfLastMonth,
        today,
      );

      // Get additional metrics
      const completionByPhase = await this.getCompletionByPhase(
        user.branch_id,
        startOfCurrentMonth,
        endOfCurrentMonth,
      );
      const twoByTwoCompletion = await this.getTwoByTwoCompletion(
        user.branch_id,
      );
      const monthlyCustomerFeedback = await this.getMonthlyCustomerFeedback(
        startOfCurrentMonth,
        endOfCurrentMonth,
      );
      const monthlyCallStatus = await this.getMonthlyCallStatus(
        startOfCurrentMonth,
        endOfCurrentMonth,
      );
      const monthlyDormantProgress = await this.getMonthlyDormantProgress(
        user.branch_id,
        startOfCurrentMonth,
        endOfCurrentMonth,
      );
      const overdueCallsTrend = await this.getOverdueCallsTrend();
      const overdueCalls = await this.getOverdueCalls();

      return {
        summary_cards: summaryCards,
        personal: personal,
        completion_by_phase: completionByPhase,
        '2by2by2_completion': twoByTwoCompletion,
        monthly_customer_feedback: monthlyCustomerFeedback,
        monthly_call_status: monthlyCallStatus,
        monthly_dormant_progress: monthlyDormantProgress,
        overdue_calls_trend: overdueCallsTrend,
        overdue_calls: overdueCalls,
      };
    }

    // Default fallback
    return {
      summary_cards: {
        total_dormant_size: { total: 0, this_month: 0 },
        total_2by2by2_size: { total: 0, this_month: 0 },
        calls_this_month: 0,
        overdue_calls_this_month: 0,
      },
      personal: {
        calls_today: 0,
        overdue_calls: 0,
        upcoming_calls: [],
        my_calls_this_month: 0,
        monthly_calls_vs_targets: {
          last_month: { target: 0, calls: 0 },
          this_month: { target: 0, calls: 0 },
        },
      },
      completion_by_phase: [],
      '2by2by2_completion': { completed: 0, in_progress: 0 },
      monthly_customer_feedback: [],
      monthly_call_status: [],
      monthly_dormant_progress: { completed: 0, remaining: 0 },
      overdue_calls_trend: [],
      overdue_calls: [],
    };
  }

  /**
   * Gets customer service summary cards data
   * @param branchId - Branch ID
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @param today - Today's date
   * @returns Promise<any> - Summary cards data
   */
  private async getCustomerServiceSummaryCards(
    branchId: string,
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
    today: Date,
  ) {
    // Total dormant size - all time
    const totalDormantSizeAll =
      await this.prisma.customerServiceHitlistRecord.count({
        where: {
          customer_service_hitlist: {
            type: 'Dormancy',
            uploader: {
              branch_id: branchId,
            },
          },
        },
      });

    // Total dormant size - this month
    const totalDormantSizeThisMonth =
      await this.prisma.customerServiceHitlistRecord.count({
        where: {
          customer_service_hitlist: {
            type: 'Dormancy',
            uploader: {
              branch_id: branchId,
            },
            uploaded_date: {
              gte: startOfCurrentMonth,
              lte: endOfCurrentMonth,
            },
          },
        },
      });

    // Total 2by2by2 size - all time
    const total2by2by2SizeAll =
      await this.prisma.customerServiceHitlistRecord.count({
        where: {
          customer_service_hitlist: {
            type: '2by2by2',
            uploader: {
              branch_id: branchId,
            },
          },
        },
      });

    // Total 2by2by2 size - this month
    const total2by2by2SizeThisMonth =
      await this.prisma.customerServiceHitlistRecord.count({
        where: {
          customer_service_hitlist: {
            type: '2by2by2',
            uploader: {
              branch_id: branchId,
            },
            uploaded_date: {
              gte: startOfCurrentMonth,
              lte: endOfCurrentMonth,
            },
          },
        },
      });

    // Calls this month
    const callsThisMonth = await this.prisma.activity.count({
      where: {
        interaction_type: {
          equals: 'call',
          mode: 'insensitive',
        },
        performed_by: {
          branch_id: branchId,
        },
        created_at: {
          gte: startOfCurrentMonth,
          lte: endOfCurrentMonth,
        },
      },
    });

    // Overdue calls this month
    const overdueCallsThisMonth = await this.prisma.twoByTwoPhase.count({
      where: {
        expected_completion_date: {
          gte: startOfCurrentMonth,
          lte: endOfCurrentMonth,
          lt: today,
        },
        is_completed: false,
      },
    });

    return {
      total_dormant_size: {
        total: totalDormantSizeAll,
        this_month: totalDormantSizeThisMonth,
      },
      total_2by2by2_size: {
        total: total2by2by2SizeAll,
        this_month: total2by2by2SizeThisMonth,
      },
      calls_this_month: callsThisMonth,
      overdue_calls_this_month: overdueCallsThisMonth,
    };
  }

  /**
   * Gets customer service personal metrics data
   * @param user - Logged-in user object
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @param startOfLastMonth - Start of last month
   * @param endOfLastMonth - End of last month
   * @param today - Today's date
   * @returns Promise<any> - Personal metrics data
   */
  private async getCustomerServicePersonalMetrics(
    user: any,
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
    startOfLastMonth: Date,
    endOfLastMonth: Date,
    today: Date,
  ) {
    // Calls today - TwoByTwoPhase records with expected_completion_date today (date only) and assigned to user
    const twoByTwoPhasesToday = await this.prisma.twoByTwoPhase.count({
      where: {
        expected_completion_date: {
          gte: startOfDay(today),
          lt: startOfDay(new Date(today.getTime() + 24 * 60 * 60 * 1000)), // Next day
        },
        assigned_to_user_id: user.id,
      },
    });

    // Calls today - CustomerServiceHitlistRecord records with rm_code matching user and type Dormancy
    const dormancyCallsToday =
      await this.prisma.customerServiceHitlistRecord.count({
        where: {
          rm_code: user.rm_code,
          customer_service_hitlist: {
            type: 'Dormancy',
          },
        },
      });

    const callsToday = twoByTwoPhasesToday + dormancyCallsToday;

    // Overdue calls - TwoByTwoPhase records with expected_completion_date < today (date only) and is_completed false
    const overdueCalls = await this.prisma.twoByTwoPhase.count({
      where: {
        expected_completion_date: {
          lt: startOfDay(today),
        },
        is_completed: false,
      },
    });

    // Upcoming calls - TwoByTwoPhase records with expected_completion_date > today (date only) and assigned to user
    const upcomingCallsData = await this.prisma.twoByTwoPhase.findMany({
      where: {
        expected_completion_date: {
          gte: startOfDay(new Date(today.getTime() + 24 * 60 * 60 * 1000)), // Tomorrow
        },
        assigned_to_user_id: user.id,
      },
      include: {
        customer_service_hitlist_record: true,
      },
      orderBy: {
        expected_completion_date: 'asc',
      },
    });

    const upcomingCalls = upcomingCallsData.map((phase) => ({
      name: phase.customer_service_hitlist_record.customer_name,
      date: phase.expected_completion_date || new Date(),
    }));

    // My calls this month
    const myCallsThisMonth = await this.prisma.activity.count({
      where: {
        performed_by_user_id: user.id,
        created_at: {
          gte: startOfCurrentMonth,
          lte: endOfCurrentMonth,
        },
      },
    });

    // Monthly calls vs targets
    const thisMonthTargets = await this.calculateMonthlyTargets(
      user,
      startOfCurrentMonth,
      endOfCurrentMonth,
    );
    const lastMonthTargets = await this.calculateMonthlyTargets(
      user,
      startOfLastMonth,
      endOfLastMonth,
    );

    const thisMonthCalls = await this.prisma.activity.count({
      where: {
        performed_by_user_id: user.id,
        interaction_type: {
          equals: 'call',
          mode: 'insensitive',
        },
        created_at: {
          gte: startOfCurrentMonth,
          lte: endOfCurrentMonth,
        },
      },
    });

    const lastMonthCalls = await this.prisma.activity.count({
      where: {
        performed_by_user_id: user.id,
        interaction_type: {
          equals: 'call',
          mode: 'insensitive',
        },
        created_at: {
          gte: startOfLastMonth,
          lte: endOfLastMonth,
        },
      },
    });

    return {
      calls_today: callsToday,
      overdue_calls: overdueCalls,
      upcoming_calls: upcomingCalls,
      my_calls_this_month: myCallsThisMonth,
      monthly_calls_vs_targets: {
        last_month: {
          target: lastMonthTargets,
          calls: lastMonthCalls,
        },
        this_month: {
          target: thisMonthTargets,
          calls: thisMonthCalls,
        },
      },
    };
  }

  /**
   * Calculates monthly targets for a user
   * @param user - User object
   * @param startOfMonth - Start of month
   * @param endOfMonth - End of month
   * @returns Promise<number> - Total target for the month
   */
  private async calculateMonthlyTargets(
    user: any,
    startOfMonth: Date,
    endOfMonth: Date,
  ): Promise<number> {
    // Get targets for DORMANCY_HITLIST or TWO_BY_TWO_BY_TWO_HITLIST
    const targets = await this.prisma.target.findMany({
      where: {
        activity: {
          in: ['CUSTOMER_SERVICE'],
        },
        status: 'Active',
        AND: [
          {
            OR: [
              {
                // User is directly assigned to the target
                user_id: user.id,
              },
              {
                // User is part of the role and not exempted
                role_id: user.role_id,
                exempted_target_users: {
                  none: {
                    user_id: user.id,
                  },
                },
              },
            ],
          },
          {
            // Target overlaps with the month
            OR: [
              {
                start_date: {
                  lte: endOfMonth,
                },
                end_date: {
                  gte: startOfMonth,
                },
              },
              {
                start_date: {
                  lte: endOfMonth,
                },
                end_date: null,
              },
            ],
          },
        ],
      },
    });

    let totalTarget = 0;

    targets.forEach((target) => {
      const targetDays = this.calculateTargetDaysInFullMonth(
        target,
        startOfMonth,
        endOfMonth,
      );
      totalTarget += targetDays * target.target_value;
    });

    return totalTarget;
  }

  /**
   * Gets completion by phase data
   * @param branchId - Branch ID
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @returns Promise<any[]> - Completion by phase data
   */
  private async getCompletionByPhase(
    branchId: string,
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
  ) {
    const phases = [
      { name: 'First 2', type: 'first2' },
      { name: 'Second 2', type: 'second2' },
      { name: 'Third 2', type: 'third2' },
    ];

    const result: Array<{
      name: string;
      total: number;
      completed: number;
    }> = [];

    for (const phase of phases) {
      const total = await this.prisma.twoByTwoPhase.count({
        where: {
          type: phase.type as any,
          expected_completion_date: {
            gte: startOfCurrentMonth,
            lte: endOfCurrentMonth,
          },
          customer_service_hitlist_record: {
            customer_service_hitlist: {
              uploader: {
                branch_id: branchId,
              },
            },
          },
        },
      });

      const completed = await this.prisma.twoByTwoPhase.count({
        where: {
          type: phase.type as any,
          expected_completion_date: {
            gte: startOfCurrentMonth,
            lte: endOfCurrentMonth,
          },
          is_completed: true,
          customer_service_hitlist_record: {
            customer_service_hitlist: {
              uploader: {
                branch_id: branchId,
              },
            },
          },
        },
      });

      result.push({
        name: phase.name,
        total,
        completed,
      });
    }

    return result;
  }

  /**
   * Gets 2by2by2 completion data
   * @param branchId - Branch ID
   * @returns Promise<any> - 2by2by2 completion data
   */
  private async getTwoByTwoCompletion(branchId: string) {
    // Get all CustomerServiceHitlistRecord records with their phases for the branch
    const hitlistRecords =
      await this.prisma.customerServiceHitlistRecord.findMany({
        where: {
          customer_service_hitlist: {
            uploader: {
              branch_id: branchId,
            },
          },
        },
        include: {
          two_by_two_phases: {
            select: {
              is_completed: true,
            },
          },
        },
      });

    let completed = 0;
    let inProgress = 0;

    hitlistRecords.forEach((record) => {
      if (record.two_by_two_phases.length > 0) {
        // Check if all phases are completed
        const allCompleted = record.two_by_two_phases.every(
          (phase) => phase.is_completed,
        );

        if (allCompleted) {
          completed++;
        } else {
          inProgress++;
        }
      }
    });

    return {
      completed,
      in_progress: inProgress,
    };
  }

  /**
   * Gets monthly customer feedback data
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @returns Promise<any[]> - Monthly customer feedback data
   */
  private async getMonthlyCustomerFeedback(
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
  ) {
    const feedbackCategories =
      await this.prisma.customerFeedbackCategory.findMany();

    const result: Array<{
      name: string;
      value: number;
    }> = [];

    for (const category of feedbackCategories) {
      const value = await this.prisma.activity.count({
        where: {
          created_at: {
            gte: startOfCurrentMonth,
            lte: endOfCurrentMonth,
          },
          customer_feedback_id: category.id,
          customer_service_hitlist_record_id: {
            not: null,
          },
        },
      });

      result.push({
        name: category.name,
        value,
      });
    }

    return result;
  }

  /**
   * Gets monthly call status data
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @returns Promise<any[]> - Monthly call status data
   */
  private async getMonthlyCallStatus(
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
  ) {
    // Get unique call statuses from activities this month with customer_service_hitlist_record_id
    const uniqueStatuses = await this.prisma.activity.findMany({
      where: {
        created_at: {
          gte: startOfCurrentMonth,
          lte: endOfCurrentMonth,
        },
        customer_service_hitlist_record_id: {
          not: null,
        },
        call_status: {
          not: null,
        },
      },
      select: {
        call_status: true,
      },
      distinct: ['call_status'],
    });

    const result: Array<{
      name: string;
      value: number;
    }> = [];

    for (const status of uniqueStatuses) {
      if (status.call_status) {
        const value = await this.prisma.activity.count({
          where: {
            created_at: {
              gte: startOfCurrentMonth,
              lte: endOfCurrentMonth,
            },
            call_status: status.call_status,
            customer_service_hitlist_record_id: {
              not: null,
            },
          },
        });

        result.push({
          name: status.call_status,
          value,
        });
      }
    }

    return result;
  }

  /**
   * Gets monthly dormant progress data
   * @param branchId - Branch ID
   * @param startOfCurrentMonth - Start of current month
   * @param endOfCurrentMonth - End of current month
   * @returns Promise<any> - Monthly dormant progress data
   */
  private async getMonthlyDormantProgress(
    branchId: string,
    startOfCurrentMonth: Date,
    endOfCurrentMonth: Date,
  ) {
    // Get all dormancy records for the branch
    const dormancyRecords =
      await this.prisma.customerServiceHitlistRecord.findMany({
        where: {
          customer_service_hitlist: {
            type: 'Dormancy',
            uploader: {
              branch_id: branchId,
            },
          },
        },
        select: {
          id: true,
          activities: {
            where: {
              interaction_type: {
                equals: 'call',
                mode: 'insensitive',
              },
              created_at: {
                gte: startOfCurrentMonth,
                lte: endOfCurrentMonth,
              },
            },
            select: {
              id: true,
            },
          },
        },
      });

    let completed = 0;
    let remaining = 0;

    dormancyRecords.forEach((record) => {
      if (record.activities.length > 0) {
        completed++;
      } else {
        remaining++;
      }
    });

    return {
      completed,
      remaining,
    };
  }

  /**
   * Gets overdue calls trend for past 6 months
   * @returns Promise<any[]> - Overdue calls trend data
   */
  private async getOverdueCallsTrend() {
    const result: Array<{
      month: string;
      value: number | null;
    }> = [];
    const now = new Date();

    for (let i = 0; i < 6; i++) {
      const monthDate = subMonths(now, i);
      const startOfMonthDate = startOfMonth(monthDate);
      const endOfMonthDate = endOfMonth(monthDate);
      const today = startOfDay(now);

      // Get all TwoByTwoPhase records with expected_completion_date in this month
      const phases = await this.prisma.twoByTwoPhase.findMany({
        where: {
          expected_completion_date: {
            gte: startOfMonthDate,
            lte: endOfMonthDate,
          },
        },
        select: {
          execution_date: true,
          expected_completion_date: true,
          is_completed: true,
        },
      });

      // Count overdue calls based on the new criteria:
      // 1. expected_completion_date has been passed by today's date and is_completed is false
      // 2. execution_date > expected_completion_date and is_completed = true
      const overdueInMonth = phases.filter((phase) => {
        if (!phase.expected_completion_date) return false;

        const expectedDateOnly = new Date(
          phase.expected_completion_date.getFullYear(),
          phase.expected_completion_date.getMonth(),
          phase.expected_completion_date.getDate(),
        );

        // Condition 1: expected_completion_date has passed and is not completed
        if (expectedDateOnly < today && !phase.is_completed) {
          return true;
        }

        // Condition 2: execution_date > expected_completion_date and is completed
        if (phase.execution_date && phase.is_completed) {
          const executionDateOnly = new Date(
            phase.execution_date.getFullYear(),
            phase.execution_date.getMonth(),
            phase.execution_date.getDate(),
          );
          return executionDateOnly > expectedDateOnly;
        }

        return false;
      }).length;

      result.unshift({
        month: monthDate.toLocaleDateString('en-US', {
          month: 'long',
          year: 'numeric',
        }),
        value: overdueInMonth || null,
      });
    }

    return result;
  }

  /**
   * Gets overdue calls list
   * @returns Promise<any[]> - Overdue calls list
   */
  private async getOverdueCalls() {
    const now = new Date();
    const today = startOfDay(now);
    const startOfCurrentMonth = startOfMonth(now);
    const endOfCurrentMonth = endOfMonth(now);

    // Get all TwoByTwoPhase records with expected_completion_date in this month
    const phases = await this.prisma.twoByTwoPhase.findMany({
      where: {
        expected_completion_date: {
          gte: startOfCurrentMonth,
          lte: endOfCurrentMonth,
        },
      },
      include: {
        assigned_to: {
          select: {
            name: true,
          },
        },
        customer_service_hitlist_record: {
          select: {
            customer_name: true,
          },
        },
      },
    });

    const result: Array<{
      agent: string;
      customer_name: string;
      phase: string;
      date: Date;
      overdue_by: number;
      is_done: boolean;
    }> = [];

    phases.forEach((phase) => {
      if (!phase.expected_completion_date) return;

      const expectedDateOnly = new Date(
        phase.expected_completion_date.getFullYear(),
        phase.expected_completion_date.getMonth(),
        phase.expected_completion_date.getDate(),
      );

      let isOverdue = false;
      let overdueBy = 0;

      // Condition 1: expected_completion_date has been passed by today's date and is_completed is false
      if (expectedDateOnly < today && !phase.is_completed) {
        isOverdue = true;
        overdueBy = Math.ceil(
          (today.getTime() - expectedDateOnly.getTime()) /
            (1000 * 60 * 60 * 24),
        );
      }
      // Condition 2: execution_date > expected_completion_date and is_completed = true
      else if (phase.execution_date && phase.is_completed) {
        const executionDateOnly = new Date(
          phase.execution_date.getFullYear(),
          phase.execution_date.getMonth(),
          phase.execution_date.getDate(),
        );

        if (executionDateOnly > expectedDateOnly) {
          isOverdue = true;
          overdueBy = Math.ceil(
            (executionDateOnly.getTime() - expectedDateOnly.getTime()) /
              (1000 * 60 * 60 * 24),
          );
        }
      }

      if (isOverdue) {
        result.push({
          agent: phase.assigned_to.name,
          customer_name: phase.customer_service_hitlist_record.customer_name,
          phase: phase.type,
          date: phase.expected_completion_date,
          overdue_by: overdueBy,
          is_done: phase.is_completed,
        });
      }
    });

    return result;
  }
}
