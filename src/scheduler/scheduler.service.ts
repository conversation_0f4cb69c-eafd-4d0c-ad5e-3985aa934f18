import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as cron from 'node-cron';
import { QueueService } from '../queue/queue.service';
import { ScheduledTaskService } from '../scheduled-tasks/scheduled-task.service';

@Injectable()
export class SchedulerService implements OnModuleInit {
  private readonly logger = new Logger(SchedulerService.name);
  private scheduledTasks: Map<string, cron.ScheduledTask> = new Map();
  private pollingInterval: NodeJS.Timeout | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly queueService: QueueService,
    private readonly scheduledTaskService: ScheduledTaskService,
  ) {}

  async onModuleInit() {
    this.logger.log('🚀 Scheduler service initialized');
    // Note: startScheduler() will be called from the main bootstrap function
    // to avoid double initialization
  }

  async startScheduler() {
    this.logger.log(
      '🔄 Scheduler service initialized (polling handled by direct implementation)',
    );
    // Polling is now handled by the direct implementation in scheduler.ts
    // This method is kept for compatibility but doesn't start any polling
  }

  async stopScheduler() {
    this.logger.log('Stopping scheduled tasks...');

    // Stop the frequent polling interval
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
      this.logger.log('Stopped frequent polling interval');
    }

    for (const [name, task] of this.scheduledTasks) {
      task.stop();
      this.logger.log(`Stopped task: ${name}`);
    }

    this.scheduledTasks.clear();
    this.logger.log('All scheduled tasks stopped');
  }
}
