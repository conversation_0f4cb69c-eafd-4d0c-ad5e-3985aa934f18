#!/bin/bash

# Test script for all implemented endpoints
BASE_URL="http://localhost:3000"
TOKEN="test-token"

echo "Testing implemented endpoints..."

echo "1. Testing Support Tickets endpoints:"
echo "GET /support_tickets"
curl -X GET "$BASE_URL/support_tickets" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n\n"

echo "POST /support_tickets"
curl -X POST "$BASE_URL/support_tickets" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "Report a bug",
    "priority": "Medium",
    "title": "Test ticket",
    "description": "Test description",
    "status": "Open"
  }' \
  -w "\nStatus: %{http_code}\n\n"

echo "2. Testing Targets endpoints:"
echo "GET /targets?type=role"
curl -X GET "$BASE_URL/targets?type=role" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\nStatus: %{http_code}\n\n"

echo "GET /targets/count"
curl -X GET "$BASE_URL/targets/count" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\nStatus: %{http_code}\n\n"

echo "3. Testing Branches endpoint:"
echo "GET /branches?usePermission=true"
curl -X GET "$BASE_URL/branches?usePermission=true" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\nStatus: %{http_code}\n\n"

echo "4. Testing Customer Service Hitlist:"
echo "GET /customer-service/hitlist-template"
curl -X GET "$BASE_URL/customer-service/hitlist-template" \
  -w "\nStatus: %{http_code}\n\n"

echo "All tests completed!"
