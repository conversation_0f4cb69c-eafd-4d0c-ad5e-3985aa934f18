import { ApiProperty } from '@nestjs/swagger';

export class HitlistRecordDto {
  @ApiProperty({
    description: 'Customer name',
    example: '<PERSON>',
  })
  customerName: string;

  @ApiProperty({
    description: 'Account number',
    example: '**********',
  })
  accountNumber: string;

  @ApiProperty({
    description: 'Phone number',
    example: '+**********',
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Agent RM Code',
    example: 'RM001',
  })
  agent: string;

  @ApiProperty({
    description: 'CXO RM Code',
    example: 'RM002',
  })
  cxo: string;
}
