import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { VoiceController } from './voice.controller';
import { VoiceService } from './voice.service';
import { VoiceCallbackDto, CallDirection } from './dto/voice-callback.dto';
import { VoiceEventDto, VoiceEventType } from './dto/voice-event.dto';

describe('VoiceController', () => {
  let controller: VoiceController;
  let service: VoiceService;

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [VoiceController],
      providers: [
        VoiceService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    controller = module.get<VoiceController>(VoiceController);
    service = module.get<VoiceService>(VoiceService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('handleVoiceCallback', () => {
    it('should handle voice callback and return XML', async () => {
      const callbackData: VoiceCallbackDto = {
        sessionId: 'ATVoice_12345678-1234-1234-1234-123456789012',
        callerNumber: 'kb_tracker.kbtracker-1757149460516',
        clientDialedNumber: '+254700000001',
        direction: CallDirection.INBOUND,
        callSessionState: 'Ringing',
        callStartTime: '2025-01-15T10:30:00.000Z',
      };

      const result = await controller.handleVoiceCallback(callbackData);

      expect(result).toContain('<?xml version="1.0" encoding="UTF-8"?>');
      expect(result).toContain('<Response>');
      expect(result).toContain('<Dial');
      expect(result).toContain('record="true"');
      expect(result).toContain('</Response>');
    });

    it('should handle errors gracefully and return basic XML', async () => {
      jest
        .spyOn(service, 'handleVoiceCallback')
        .mockRejectedValue(new Error('Test error'));

      const callbackData: VoiceCallbackDto = {
        sessionId: 'ATVoice_error-test',
      };

      const result = await controller.handleVoiceCallback(callbackData);

      expect(result).toContain('<?xml version="1.0" encoding="UTF-8"?>');
      expect(result).toContain('<Response>');
      expect(result).toContain('</Response>');
    });
  });

  describe('handleVoiceEvent', () => {
    it('should handle voice event and return success response', async () => {
      const eventData: VoiceEventDto = {
        sessionId: 'ATVoice_12345678-1234-1234-1234-123456789012',
        eventType: VoiceEventType.ANSWERED,
        from: '+254700000000',
        to: '+254700000001',
        timestamp: '2025-01-15T10:30:15.000Z',
      };

      const result = await controller.handleVoiceEvent(eventData);

      expect(result.status).toBe('ok');
      expect(result.timestamp).toBeDefined();
      expect(result.sessionId).toBe(eventData.sessionId);
    });

    it('should handle completed call event with duration and recording', async () => {
      const eventData: VoiceEventDto = {
        sessionId: 'ATVoice_12345678-1234-1234-1234-123456789012',
        eventType: VoiceEventType.COMPLETED,
        from: '+254700000000',
        to: '+254700000001',
        durationInSeconds: 120,
        recordingUrl: 'https://voice.africastalking.com/recordings/12345.mp3',
        timestamp: '2025-01-15T10:32:00.000Z',
      };

      const result = await controller.handleVoiceEvent(eventData);

      expect(result.status).toBe('ok');
      expect(result.timestamp).toBeDefined();
      expect(result.sessionId).toBe(eventData.sessionId);
    });

    it('should handle errors gracefully and return basic success response', async () => {
      jest
        .spyOn(service, 'handleVoiceEvent')
        .mockRejectedValue(new Error('Test error'));

      const eventData: VoiceEventDto = {
        sessionId: 'ATVoice_error-test',
        eventType: VoiceEventType.FAILED,
      };

      const result = await controller.handleVoiceEvent(eventData);

      expect(result.status).toBe('ok');
      expect(result.timestamp).toBeDefined();
    });
  });
});
