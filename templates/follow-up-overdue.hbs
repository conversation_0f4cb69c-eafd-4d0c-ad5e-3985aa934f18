<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #ffebee;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #f44336;
        }
        .content {
            background-color: #ffffff;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .alert {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
            border-radius: 4px;
        }
        .details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .footer {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #666;
        }
        .urgent {
            color: #d32f2f;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="urgent">⚠️ {{title}}</h1>
    </div>
    
    <div class="content">
        <p>Hello {{recipientName}},</p>
        
        <div class="alert">
            <p><strong>{{message}}</strong></p>
        </div>
        
        {{#if data}}
        <div class="details">
            <h3>Follow-up Details:</h3>
            <ul>
                {{#if data.leadName}}
                <li><strong>Lead:</strong> {{data.leadName}}</li>
                {{/if}}
                {{#if data.followUpType}}
                <li><strong>Type:</strong> {{#if (eq data.followUpType 'call')}}Call{{else}}Visit{{/if}}</li>
                {{/if}}
                {{#if data.scheduledTime}}
                <li><strong>Originally Scheduled:</strong> {{formatDate data.scheduledTime}}</li>
                {{/if}}
            </ul>
        </div>
        {{/if}}
        
        <p>Please take action as soon as possible:</p>
        <ul>
            <li>Complete the follow-up if it was done but not recorded</li>
            <li>Reschedule the follow-up if needed</li>
            <li>Update the lead status accordingly</li>
        </ul>
        
        <p>Please log into the KB Tracker system to manage your follow-ups.</p>
        
        <p>Best regards,<br>
        KB Tracker System</p>
    </div>
    
    <div class="footer">
        <p>This is an automated notification from KB Tracker. Please do not reply to this email.</p>
        <p>Generated on {{timestamp}}</p>
    </div>
</body>
</html>
