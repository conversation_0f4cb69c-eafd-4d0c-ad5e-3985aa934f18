import {
  Controller,
  Post,
  Get,
  Body,
  HttpCode,
  HttpStatus,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Req,
  RawBodyRequest,
  Logger,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { VoiceService } from './voice.service';
import { VoiceCallbackDto } from './dto/voice-callback.dto';
import { VoiceEventDto } from './dto/voice-event.dto';
import {
  VoiceCallbackResponseDto,
  VoiceEventResponseDto,
} from './dto/voice-response.dto';
import { InboundCallService } from './inbound-call.service';
import { InboundCallDto } from './dto/inbound-call.dto';
import { CallStatusDto } from './dto/call-status.dto';
import { RecordingDto } from './dto/recording.dto';

/**
 * Controller handling Africa's Talking voice callback endpoints
 * Provides webhook endpoints for voice call integration
 */
@ApiTags('Voice')
@Controller('voice')
export class VoiceController {
  private readonly logger = new Logger(VoiceController.name);

  constructor(
    private readonly voiceService: VoiceService,

    // private readonly voiceService: VoiceService,
    private readonly inboundCallService: InboundCallService,
    // ) {}
  ) {}

  /**
   * Handle voice callback from Africa's Talking
   * POST /voice/callback
   *
   * This endpoint is called by Africa's Talking when there's an inbound or outbound call.
   * It must return valid XML with Content-Type: application/xml.
   */
  @Post('callback')
  @HttpCode(HttpStatus.OK)
  @Header('Content-Type', 'application/xml')
  @ApiOperation({
    summary: "Handle voice callback from Africa's Talking",
    description:
      "Webhook endpoint that receives call notifications from Africa's Talking and returns XML instructions for call handling. This endpoint logs all incoming call data and responds with XML that tells Africa's Talking how to handle the call.",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'XML response with call handling instructions',
    type: VoiceCallbackResponseDto,
    content: {
      'application/xml': {
        example:
          '<?xml version="1.0" encoding="UTF-8"?><Response><Dial record="true" sequential="true" phoneNumbers="" ringbackTone="https://www.cuteprofit.com/account/api/cp3.wav" /></Response>',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid callback data',
  })
  async handleVoiceCallback(
    @Body(new ValidationPipe({ transform: true }))
    callbackData: any,
  ): Promise<string> {
    this.logger.log('Received voice callback request');

    try {
      const response =
        await this.voiceService.handleVoiceCallback(callbackData);
      this.logger.log('Successfully processed voice callback');

      // Check if response has xml property (VoiceCallbackResponseDto) or is empty object
      if (response && typeof response === 'object' && 'xml' in response) {
        return response.xml;
      } else {
        // Return empty XML response for empty objects
        return '<?xml version="1.0" encoding="UTF-8"?><Response></Response>';
      }
    } catch (error) {
      this.logger.error('Error processing voice callback:', error);
      // Return a basic XML response even if there's an error
      return '<?xml version="1.0" encoding="UTF-8"?><Response><Dial record="true" sequential="true" phoneNumbers="" ringbackTone="" /></Response>';
    }
  }

  /**
   * Handle voice events from Africa's Talking
   * POST /voice/events
   *
   * This endpoint receives call lifecycle events (Ringing, Answered, Completed, Failed).
   * It can return a simple JSON response with HTTP 200.
   */
  @Post('events')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "Handle voice events from Africa's Talking",
    description:
      "Webhook endpoint that receives call lifecycle events from Africa's Talking such as Ringing, Answered, Completed, and Failed events. This endpoint logs all event data for monitoring and can be used to update call records in the database.",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Event processed successfully',
    type: VoiceEventResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid event data',
  })
  async handleVoiceEvent(
    @Body(new ValidationPipe({ transform: true })) eventData: any,
  ): Promise<any> {
    this.logger.log('Received voice event request');

    try {
      const response = await this.voiceService.handleVoiceEvent(eventData);
      this.logger.log('Successfully processed voice event');
      return response;
    } catch (error) {
      this.logger.error('Error processing voice event:', error);
      // Return a basic success response even if there's an error
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
      };
    }
  }
  // constructor(
  //   private readonly voiceService: VoiceService,
  //   private readonly inboundCallService: InboundCallService,
  // ) {}

  private logRawRequest(req: RawBodyRequest<Request>, endpoint: string) {
    console.log(`=== RAW REQUEST DATA - ${endpoint} ===`);
    console.log('Method:', req.method);
    console.log('URL:', req.url);
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Raw Body:', req.rawBody?.toString());
    console.log('Body as JSON:', JSON.stringify(req.body, null, 2));
    console.log('=====================================');
  }

  @Post('inbound')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "Handle inbound call webhook from Africa's Talking",
  })
  @ApiResponse({ status: 200, description: 'XML response for call routing' })
  async handleInboundCall(
    @Req() req: RawBodyRequest<Request>,
    @Headers('x-at-signature') signature?: string,
  ): Promise<string> {
    // Log the raw incoming data FIRST before any validation
    this.logRawRequest(req, 'INBOUND CALL');
    console.log('=== INBOUND CALL WEBHOOK DATA ===');
    console.log('Raw Body String:', req.rawBody?.toString());
    console.log('Parsed Body:', JSON.stringify(req.body, null, 2));
    console.log('Headers:', JSON.stringify({ signature }, null, 2));
    console.log('================================');

    // Parse the incoming data
    let callData: any = {};
    try {
      if (req.rawBody) {
        const bodyString = req.rawBody.toString();
        console.log('Body as string:', bodyString);

        try {
          callData = JSON.parse(bodyString);
          console.log('Parsed as JSON:', callData);
        } catch (jsonError) {
          console.log('Not JSON, trying URL encoded...');
          const params = new URLSearchParams(bodyString);
          callData = Object.fromEntries(params.entries());
          console.log('Parsed as URL encoded:', callData);
        }
      } else {
        callData = req.body || {};
      }
    } catch (error) {
      console.log('Error parsing body:', error);
      callData = req.body || {};
    }

    this.logger.log(
      `Received inbound call from ${callData.callerNumber} to ${callData.destinationNumber}`,
    );

    // Check if this is an active call or a completed call
    if (callData.isActive === '0') {
      this.logger.log(
        'Call is not active (isActive=0), treating as status update only',
      );
      // Don't generate XML response for completed calls
      return `<Response>
    <Dial
        phoneNumbers="+************"
        ringbackTone="http://mymediafile.com/playme.mp3"
        record="true"
        maxDuration="5"
        sequential="true"
    />
</Response>`;
    }

    try {
      // Create activity record first
      await this.inboundCallService.createInboundCallActivity(callData);

      // Generate routing response
      const response = await this.voiceService.handleInboundCall(callData);

      // Log the XML response being sent
      console.log('=== XML RESPONSE BEING SENT ===');
      console.log(response);
      console.log('===============================');

      return response; // Return XML response
    } catch (error) {
      this.logger.error(
        `Error handling inbound call: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Post('call-status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Handle call status updates from Africa's Talking" })
  @ApiResponse({ status: 200, description: 'Call status updated successfully' })
  async handleCallStatus(
    @Req() req: RawBodyRequest<Request>,
  ): Promise<{ message: string }> {
    // Log the raw incoming data FIRST before any validation
    this.logRawRequest(req, 'CALL STATUS');
    console.log('=== CALL STATUS WEBHOOK DATA ===');
    console.log('Raw Body String:', req.rawBody?.toString());
    console.log('Parsed Body:', JSON.stringify(req.body, null, 2));
    console.log('================================');

    // Parse the incoming data
    let statusData: any = {};
    try {
      if (req.rawBody) {
        const bodyString = req.rawBody.toString();
        console.log('Body as string:', bodyString);

        try {
          statusData = JSON.parse(bodyString);
          console.log('Parsed as JSON:', statusData);
        } catch (jsonError) {
          console.log('Not JSON, trying URL encoded...');
          const params = new URLSearchParams(bodyString);
          statusData = Object.fromEntries(params.entries());
          console.log('Parsed as URL encoded:', statusData);
        }
      } else {
        statusData = req.body || {};
      }
    } catch (error) {
      console.log('Error parsing body:', error);
      statusData = req.body || {};
    }

    this.logger.log(
      `Received call status update for session ${statusData.sessionId || 'unknown'}: ${statusData.status || 'unknown'}`,
    );

    // Check if we have the minimum required data
    if (!statusData.sessionId) {
      this.logger.warn(
        'No sessionId provided in call status update, skipping update',
      );
      return { message: 'Call status update skipped - no sessionId' };
    }

    try {
      await this.inboundCallService.updateCallStatus(
        statusData.sessionId,
        statusData.status || 'unknown',
        statusData.durationInSeconds,
        statusData.recordingUrl,
        statusData.callId,
      );

      return { message: 'Call status updated successfully' };
    } catch (error) {
      this.logger.error(
        `Error updating call status: ${error.message}`,
        error.stack,
      );
      // Don't throw error, just log it to prevent 500 errors
      return { message: 'Call status update failed but request processed' };
    }
  }

  @Post('recording')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Handle call recording data from Africa's Talking" })
  @ApiResponse({ status: 200, description: 'Recording URL saved successfully' })
  async handleRecording(
    @Req() req: RawBodyRequest<Request>,
  ): Promise<{ message: string }> {
    // Log the raw incoming data FIRST before any validation
    this.logRawRequest(req, 'RECORDING');
    console.log('=== RECORDING WEBHOOK DATA ===');
    console.log('Raw Body String:', req.rawBody?.toString());
    console.log('Parsed Body:', JSON.stringify(req.body, null, 2));
    console.log('==============================');

    // Try to parse the body manually
    let recordingData: any = {};
    try {
      if (req.rawBody) {
        const bodyString = req.rawBody.toString();
        console.log('Body as string:', bodyString);

        try {
          recordingData = JSON.parse(bodyString);
          console.log('Parsed as JSON:', recordingData);
        } catch (jsonError) {
          console.log('Not JSON, trying URL encoded...');
          const params = new URLSearchParams(bodyString);
          recordingData = Object.fromEntries(params.entries());
          console.log('Parsed as URL encoded:', recordingData);
        }
      }
    } catch (error) {
      console.log('Error parsing body:', error);
    }

    this.logger.log(
      `Received recording for session ${recordingData.sessionId || 'unknown'}: ${recordingData.recordingUrl || 'unknown'}`,
    );

    try {
      await this.inboundCallService.updateRecording(
        recordingData.sessionId,
        recordingData.recordingUrl,
        recordingData.callId,
      );

      return { message: 'Recording URL saved successfully' };
    } catch (error) {
      this.logger.error(
        `Error updating recording: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
