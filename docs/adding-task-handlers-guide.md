# Adding Task Handlers Guide

This guide explains how to properly add new task handlers to the scheduled tasks system.

## Overview

The task handler system allows you to create custom background task processors that can be executed by the worker container. Each handler is responsible for processing a specific task type.

## Architecture

- **API Container**: Creates and queues tasks using `BackgroundTaskService`
- **Scheduler Container**: Polls database and queues ready tasks (uses `QueueProducerModule`)
- **Worker Container**: Processes queued tasks using registered handlers (uses `QueueModule`)

## Step-by-Step Guide

### Step 1: Create the Task Handler

Create a new handler file in `src/scheduled-tasks/task-handlers/`:

```typescript
// src/scheduled-tasks/task-handlers/my-custom.handler.ts
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';

export interface MyCustomTaskPayload {
  // Define your payload interface
  message: string;
  userId?: string;
  // Add other required fields
}

@Injectable()
export class MyCustomTaskHandler implements TaskHandler {
  private readonly logger = new Logger(MyCustomTaskHandler.name);

  constructor(
    private readonly prisma: PrismaService,
    // Inject other services as needed
  ) {}

  getTaskType(): string {
    return 'my-custom-task';
  }

  getDescription(): string {
    return 'Description of what this task does';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payload || typeof payload !== 'object') {
      errors.push('Payload must be an object');
      return { isValid: false, errors };
    }

    if (!payload.message || typeof payload.message !== 'string') {
      errors.push('message is required and must be a string');
    }

    // Add other validation rules

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  async handle(payload: MyCustomTaskPayload, job: Job): Promise<any> {
    this.logger.log('Starting my custom task');
    console.log('My Custom Handler: Processing payload', payload);
    
    await job.updateProgress(10);

    try {
      // Your task logic here
      const { message, userId } = payload;
      
      await job.updateProgress(50);
      
      // Example: Database operations
      // const result = await this.prisma.someModel.create({...});
      
      await job.updateProgress(100);

      const result = {
        type: 'my-custom-task',
        success: true,
        message,
        timestamp: new Date().toISOString(),
      };

      this.logger.log(`Task completed successfully: ${JSON.stringify(result)}`);
      return result;

    } catch (error) {
      this.logger.error(`Task failed: ${error.message}`, error.stack);
      throw error;
    }
  }
}
```

### Step 2: Register the Handler in the Registry

Update `src/scheduled-tasks/task-handlers/task-handler.registry.ts`:

```typescript
// Add import
import { MyCustomTaskHandler } from './my-custom.handler';

@Injectable()
export class TaskHandlerRegistry {
  constructor(
    private readonly queueService: QueueService,
    private readonly consoleLogHandler: ConsoleLogHandler,
    private readonly dailyNairobiTaskHandler: DailyNairobiTaskHandler,
    private readonly targetCreationHandler: TargetCreationHandler,
    private readonly individualTargetProgressHandler: IndividualTargetProgressHandler,
    private readonly dailyTargetProgressGenerationHandler: DailyTargetProgressGenerationHandler,
    private readonly myCustomTaskHandler: MyCustomTaskHandler, // Add this
  ) {
    this.registerHandlers();
  }

  private registerHandlers() {
    // Register all task handlers
    this.registerHandler(this.consoleLogHandler);
    this.registerHandler(this.dailyNairobiTaskHandler);
    this.registerHandler(this.targetCreationHandler);
    this.registerHandler(this.individualTargetProgressHandler);
    this.registerHandler(this.dailyTargetProgressGenerationHandler);
    this.registerHandler(this.myCustomTaskHandler); // Add this

    this.logger.log(`Registered ${this.handlers.size} task handlers`);
  }
}
```

### Step 3: Add Handler to Module

Update `src/scheduled-tasks/scheduled-task.module.ts`:

```typescript
// Add import
import { MyCustomTaskHandler } from './task-handlers/my-custom.handler';

@Module({
  imports: [
    PrismaModule,
    forwardRef(() => QueueModule),
  ],
  providers: [
    ScheduledTaskService,
    TaskExecutionService,
    BackgroundTaskService,
    TaskHandlerRegistry,
    ConsoleLogHandler,
    DailyNairobiTaskHandler,
    TargetCreationHandler,
    IndividualTargetProgressHandler,
    DailyTargetProgressGenerationHandler,
    MyCustomTaskHandler, // Add this
  ],
  controllers: [ScheduledTaskController],
  exports: [
    ScheduledTaskService,
    TaskExecutionService,
    BackgroundTaskService,
    TaskHandlerRegistry,
  ],
})
export class ScheduledTaskModule {}
```

### Step 4: Use the Handler

Now you can use your handler from any service:

```typescript
// In any service
constructor(
  private readonly backgroundTaskService: BackgroundTaskService,
) {}

async triggerMyCustomTask() {
  const task = await this.backgroundTaskService.runNow(
    'my-custom-task',
    {
      message: 'Hello from my custom task',
      userId: 'some-user-id',
    },
    {
      name: 'My Custom Task',
      description: 'Processing custom business logic',
      priority: 5,
      maxAttempts: 3,
    },
  );
  
  return task;
}
```

## Important Notes

### Container Architecture
- **Scheduler**: Uses `QueueProducerModule` (no processors) - only schedules tasks
- **Worker**: Uses `QueueModule` (includes processors) - processes tasks
- **API**: Uses `QueueProducerModule` (no processors) - only creates tasks

### Best Practices

1. **Always validate payloads** in the `validatePayload` method
2. **Use proper error handling** with try-catch blocks
3. **Update job progress** regularly using `job.updateProgress()`
4. **Log important events** for debugging
5. **Use TypeScript interfaces** for payload types
6. **Keep handlers focused** on a single responsibility
7. **Use dependency injection** for services

### Testing

Test your handler by creating a task:

```typescript
// Create a test task
const task = await backgroundTaskService.runNow(
  'my-custom-task',
  { message: 'Test message' },
  { name: 'Test Task' }
);
```

Monitor the worker logs to see your handler in action.

## Troubleshooting

- **Handler not found**: Check if it's registered in the registry and module
- **Task not processing**: Ensure worker container is running and has the handler
- **Validation errors**: Check the `validatePayload` method implementation
- **Container issues**: Verify correct module imports (Producer vs Consumer)
