import { Test, TestingModule } from '@nestjs/testing';
import { BadGatewayException } from '@nestjs/common';
import { WebRTCController } from './webrtc.controller';
import { VoiceService } from './voice.service';
import { CapabilityTokenResponseDto } from './dto/capability-token.dto';

describe('WebRTCController', () => {
  let controller: WebRTCController;
  let voiceService: VoiceService;

  const mockVoiceService = {
    getCapabilityToken: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WebRTCController],
      providers: [
        {
          provide: VoiceService,
          useValue: mockVoiceService,
        },
      ],
    }).compile();

    controller = module.get<WebRTCController>(WebRTCController);
    voiceService = module.get<VoiceService>(VoiceService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getCapabilityToken', () => {
    it('should return capability token successfully', async () => {
      const mockTokenResponse: CapabilityTokenResponseDto = {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        clientName: 'kb-tracker-1704067800000',
        expiresAt: **********,
        additionalData: { ttl: 3600 },
      };

      mockVoiceService.getCapabilityToken.mockResolvedValue(mockTokenResponse);

      const result = await controller.getCapabilityToken();

      expect(result).toEqual(mockTokenResponse);
      expect(voiceService.getCapabilityToken).toHaveBeenCalledTimes(1);
    });

    it('should throw BadGatewayException when service fails', async () => {
      const errorMessage =
        "Failed to retrieve capability token from Africa's Talking";
      mockVoiceService.getCapabilityToken.mockRejectedValue(
        new BadGatewayException(errorMessage),
      );

      await expect(controller.getCapabilityToken()).rejects.toThrow(
        BadGatewayException,
      );
      await expect(controller.getCapabilityToken()).rejects.toThrow(
        errorMessage,
      );

      expect(voiceService.getCapabilityToken).toHaveBeenCalledTimes(2);
    });

    it('should re-throw any error from the service', async () => {
      const customError = new Error('Custom error');
      mockVoiceService.getCapabilityToken.mockRejectedValue(customError);

      await expect(controller.getCapabilityToken()).rejects.toThrow(
        customError,
      );
      expect(voiceService.getCapabilityToken).toHaveBeenCalledTimes(1);
    });
  });
});
