import { IsString, <PERSON>Optional, <PERSON>Enum, IsObject, IsDateString, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { NotificationType, NotificationPriority } from '@prisma/client';

export class CreateNotificationDto {
  @ApiProperty({
    description: 'Type of notification',
    enum: NotificationType,
    example: NotificationType.LEAD_ASSIGNED,
  })
  @IsEnum(NotificationType)
  type: NotificationType;

  @ApiProperty({
    description: 'Notification title',
    example: 'New Lead Assigned',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Notification message',
    example: 'You have been assigned a new lead: <PERSON>',
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'Additional data for the notification',
    example: { leadId: 'uuid', customerName: 'John Do<PERSON>' },
  })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;

  @ApiProperty({
    description: 'ID of the user who should receive the notification',
    example: 'uuid',
  })
  @IsUUID()
  recipient_id: string;

  @ApiPropertyOptional({
    description: 'ID of the user who triggered the notification',
    example: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  sender_id?: string;

  @ApiPropertyOptional({
    description: 'Type of entity related to the notification',
    example: 'lead',
  })
  @IsOptional()
  @IsString()
  entity_type?: string;

  @ApiPropertyOptional({
    description: 'ID of the related entity',
    example: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  entity_id?: string;

  @ApiPropertyOptional({
    description: 'Notification priority',
    enum: NotificationPriority,
    example: NotificationPriority.NORMAL,
  })
  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @ApiPropertyOptional({
    description: 'Optional expiration date for the notification',
    example: '2024-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  expires_at?: string;
}
