import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for role information in user responses
 *
 * @class RoleInfoDto
 */
export class RoleInfoDto {
  @ApiProperty({
    description: 'Unique identifier for the role',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the role',
    example: 'Manager',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Description of the role',
    example: 'Manager role with team oversight responsibilities',
  })
  description?: string;
}

/**
 * Data Transfer Object for branch information in user responses
 *
 * @class BranchInfoDto
 */
export class BranchInfoDto {
  @ApiProperty({
    description: 'Unique identifier for the branch',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the branch',
    example: 'Downtown Branch',
  })
  name: string;

  @ApiProperty({
    description: 'Region information',
    type: 'object',
    properties: {
      id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440002' },
      name: { type: 'string', example: 'North Region' },
    },
  })
  region: {
    id: string;
    name: string;
  };
}

/**
 * Data Transfer Object for user who added information in user responses
 *
 * @class AddedByUserInfoDto
 */
export class AddedByUserInfoDto {
  @ApiProperty({
    description: 'Unique identifier for the user',
    example: '550e8400-e29b-41d4-a716-446655440003',
  })
  id: string;

  @ApiProperty({
    description: 'Full name of the user',
    example: 'John Doe',
  })
  name: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Relationship Manager code',
    example: 'RM001',
  })
  rm_code: string;
}

/**
 * Data Transfer Object for user response data
 *
 * This DTO represents the structure of user data returned by the API,
 * including related role and branch information.
 *
 * @class UserResponseDto
 */
export class UserResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the user',
    example: '550e8400-e29b-41d4-a716-446655440003',
  })
  id: string;

  @ApiProperty({
    description: 'Full name of the user',
    example: 'John Doe',
  })
  name: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Phone number of the user in international (+1234567890) or local (07XXXXXXXX, 01XXXXXXXX) format',
    example: '+1234567890 or 0712345678 or 0123456789',
  })
  phone_number: string;

  @ApiProperty({
    description: 'Relationship Manager code',
    example: 'RM001',
  })
  rm_code: string;

  @ApiPropertyOptional({
    description: 'Last login timestamp',
    example: '2024-01-15T10:30:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  last_login?: Date;

  @ApiProperty({
    description: 'Timestamp when the user was created',
    example: '2024-01-15T08:00:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  created_at: Date;

  @ApiProperty({
    description: 'Timestamp when the user was last updated',
    example: '2024-01-15T14:30:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  updated_at: Date;

  @ApiPropertyOptional({
  description: 'ID of the user who added this user',
  example: '550e8400-e29b-41d4-a716-446655440004',
  })
  added_by?: string;

  @ApiPropertyOptional({
  description: 'User who added this user',
  type: AddedByUserInfoDto,
  })
  added_by_user?: AddedByUserInfoDto;

  @ApiProperty({
    description: 'Role information for this user',
    type: RoleInfoDto,
  })
  role: RoleInfoDto;

  @ApiProperty({
    description: 'Branch information for this user',
    type: BranchInfoDto,
  })
  branch: BranchInfoDto;

  @ApiProperty({
    description: 'Number of general activities associated with this user',
    example: 15,
  })
  generalActivitiesCount: number;

  @ApiProperty({
    description: 'Number of leads associated with this user',
    example: 8,
  })
  leadsCount: number;

  @ApiProperty({
    description: 'Number of loan activities associated with this user',
    example: 12,
  })
  loanActivitiesCount: number;

  @ApiProperty({
    description: 'Number of scheduled visits for this user',
    example: 5,
  })
  scheduledVisitsCount: number;

  @ApiProperty({
    description: 'Number of targets assigned to this user',
    example: 3,
  })
  targetsCount: number;
}
