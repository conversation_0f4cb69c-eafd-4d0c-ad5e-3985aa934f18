import { Modu<PERSON> } from '@nestjs/common';
import { NotificationsController } from './notifications.controller';
import { NotificationsService } from './notifications.service';
import { NotificationTriggerService } from './notification-trigger.service';
import { LeadAssignedHandler } from './handlers/lead-assigned.handler';
import { UserCreatedHandler } from './handlers/user-created.handler';
import { ActivityCreatedHandler } from './handlers/activity-created.handler';
import { DormancyHitlistAssignedHandler } from './handlers/dormancy-hitlist-assigned.handler';
import { TwoByTwoHitlistAssignedHandler } from './handlers/two-by-two-hitlist-assigned.handler';
import { NotificationUtils } from './utils/notification.utils';
import { PrismaModule } from '../prisma/prisma.module';
import { QueueProducerModule } from '../queue/queue-producer.module';

@Module({
  imports: [PrismaModule, QueueProducerModule],
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    NotificationTriggerService,
    NotificationUtils,
    LeadAssignedHandler,
    UserCreatedHandler,
    ActivityCreatedHandler,
    DormancyHitlistAssignedHandler,
    TwoByTwoHitlistAssignedHandler,
  ],
  exports: [NotificationsService, NotificationTriggerService, NotificationUtils],
})
export class NotificationsModule {}
