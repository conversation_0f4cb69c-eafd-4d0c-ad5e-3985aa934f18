import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class VoiceXmlService {
  private readonly logger = new Logger(VoiceXmlService.name);

  /**
   * Generate XML response to reject unknown numbers
   * @returns XML response string
   */
  generateRejectResponse(): string {
    this.logger.log('Generating reject response');
    return `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="en-US">Sorry, this number is not registered in our system.</Say>
  <Reject/>
</Response>`;
  }

  /**
   * Generate XML response to route call to specific user
   * @param userPhone - User's phone number to route to
   * @returns XML response string
   */
  generateRouteToUserResponse(userPhone: string): string {
    this.logger.log(`Generating route to user response for phone: ${userPhone}`);
    return `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say >Thank you for calling. Please hold while we connect you to your relationship manager.</Say>
  <Dial phoneNumbers="${userPhone}" record="true" recordingFormat="mp3" 
        recordingCallbackUrl="${process.env.WEBHOOK_BASE_URL}/voice/recording"/>
  <Say >Thank you for calling. Have a great day!</Say>
</Response>`;
  }

  /**
   * Generate default XML response for unknown routing
   * @returns XML response string
   */
  generateDefaultResponse(): string {
    this.logger.log('Generating default response');
    
    const defaultPhone = process.env.DEFAULT_AGENT_PHONE || '+************';
    const webhookUrl = process.env.WEBHOOK_BASE_URL || 'https://your-ngrok-url.ngrok.io/api/v1';
    
    console.log('Default agent phone:', defaultPhone);
    console.log('Webhook base URL:', webhookUrl);
    
    const xmlResponse = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  
  <Dial phoneNumbers="${defaultPhone}" record="true" recordingFormat="mp3"
        recordingCallbackUrl="${webhookUrl}/voice/recording"/>
  
</Response>`;
    
    console.log('Generated XML:', xmlResponse);
    return xmlResponse;
  }

  /**
   * Generate error XML response
   * @returns XML response string
   */
  generateErrorResponse(): string {
    this.logger.log('Generating error response');
    return `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="en-US">We apologize, but we are experiencing technical difficulties. Please try again later.</Say>
  <Hangup/>
</Response>`;
  }

  /**
   * Generate XML response for call with custom message
   * @param message - Custom message to say
   * @param phoneNumber - Phone number to dial (optional)
   * @returns XML response string
   */
  generateCustomResponse(message: string, phoneNumber?: string): string {
    this.logger.log(`Generating custom response with message: ${message}`);
    
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="en-US">${message}</Say>`;
    
    if (phoneNumber) {
      xml += `
  <Dial phoneNumbers="${phoneNumber}" record="true" recordingFormat="mp3" 
        recordingCallbackUrl="${process.env.WEBHOOK_BASE_URL}/voice/recording"/>`;
    }
    
    xml += `
</Response>`;
    
    return xml;
  }

  /**
   * Generate XML response for call with hold music
   * @param phoneNumber - Phone number to dial
   * @param holdMessage - Message to say while holding
   * @returns XML response string
   */
  generateHoldResponse(phoneNumber: string, holdMessage: string = "Please hold while we connect you."): string {
    this.logger.log(`Generating hold response for phone: ${phoneNumber}`);
    return `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="en-US">${holdMessage}</Say>
  <Play url="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav"/>
  <Dial phoneNumbers="${phoneNumber}" record="true" recordingFormat="mp3"
        recordingCallbackUrl="${process.env.WEBHOOK_BASE_URL}/voice/recording"/>
  <Say voice="en-US">Thank you for calling. Have a great day!</Say>
</Response>`;
  }
}
