import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';
import { isApplicable, getTodayAt2AM } from '../../common/utils/date.utils';

export interface IndividualTargetProgressPayload {
  targetIds: string[];
  userBranchId: string;
  scope: 'individual';
}

@Injectable()
export class IndividualTargetProgressHandler implements TaskHandler {
  private readonly logger = new Logger(IndividualTargetProgressHandler.name);

  constructor(
    private readonly prisma: PrismaService,
  ) {}

  getTaskType(): string {
    return 'individual-target-progress';
  }

  getDescription(): string {
    return 'Creates target progress records for individual scope targets';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payload || typeof payload !== 'object') {
      errors.push('Payload must be an object');
      return { isValid: false, errors };
    }

    // Validate required fields
    const requiredFields = ['targetIds', 'userBranchId', 'scope'];
    for (const field of requiredFields) {
      if (!payload[field]) {
        errors.push(`${field} is required`);
      }
    }

    // Validate specific field types and values
    if (payload.targetIds && !Array.isArray(payload.targetIds)) {
      errors.push('targetIds must be an array');
    }

    if (payload.userBranchId && typeof payload.userBranchId !== 'string') {
      errors.push('userBranchId must be a string');
    }

    if (payload.scope && payload.scope !== 'individual') {
      errors.push('scope must be "individual"');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  async handle(payload: IndividualTargetProgressPayload, job: Job): Promise<any> {
    this.logger.log('Starting individual target progress creation background task');
    console.log('Individual Target Progress Handler: Processing payload', payload);

    await job.updateProgress(10);

    const { targetIds, userBranchId, scope } = payload;

    try {
      // Verify targets exist and get their assigned users
      await job.updateProgress(30);
      const existingTargets = await this.prisma.target.findMany({
        where: { id: { in: targetIds } },
        select: { id: true, target_value: true, branch_id: true, user_id: true },
      });

      if (existingTargets.length !== targetIds.length) {
        const foundIds = existingTargets.map(t => t.id);
        const missingIds = targetIds.filter(id => !foundIds.includes(id));
        throw new Error(`Some targets were not found: ${missingIds.join(', ')}`);
      }

      // For individual targets, get only the users assigned to these specific targets
      await job.updateProgress(50);
      console.log('Individual Target Progress Handler: Getting assigned users for individual targets');

      const assignedUserIds = existingTargets
        .map(t => t.user_id)
        .filter((userId): userId is string => userId !== null);

      const uniqueUserIds = [...new Set(assignedUserIds)];
      console.log(`Individual Target Progress Handler: Found ${uniqueUserIds.length} unique assigned users:`, uniqueUserIds);

      // Create target progress records for today
      await job.updateProgress(70);
      await this.createTargetProgressRecords(targetIds, uniqueUserIds, existingTargets);

      await job.updateProgress(100);

      const result = {
        type: 'individual-target-progress',
        success: true,
        createdProgressRecords: targetIds.length * uniqueUserIds.length,
        targetIds,
        userIds: uniqueUserIds,
        timestamp: new Date().toISOString(),
      };

      this.logger.log(`Individual target progress creation completed successfully: ${JSON.stringify(result)}`);
      console.log('Individual Target Progress Handler: Task completed successfully', result);

      return result;

    } catch (error) {
      this.logger.error('Individual target progress creation failed:', error);
      console.error('Individual Target Progress Handler: Task failed', error);
      throw error;
    }
  }

  private async createTargetProgressRecords(
    targetIds: string[],
    userIds: string[],
    targets: { id: string; target_value: number; branch_id: string; user_id: string | null }[]
  ): Promise<void> {
    console.log('Individual Target Progress Handler: Creating progress records for targets:', targets, 'users:', userIds);

    const today = new Date();
    const forDate = getTodayAt2AM();
    const applicable = await isApplicable(today, this.prisma);

    console.log('Individual Target Progress Handler: Date info:', {
      today: today.toISOString(),
      forDate: forDate.toISOString(),
      applicable
    });

    const progressRecords: any[] = [];

    // For individual targets, create progress records only for the assigned user of each target
    for (const target of targets) {
      if (target.user_id) {
        progressRecords.push({
          target_id: target.id,
          user_id: target.user_id,
          for_date: forDate,
          achieved_count: 0,
          target_value: target.target_value,
          is_achieved: null,
          is_applicable: applicable,
        });
      }
    }

    console.log(`Individual Target Progress Handler: Created ${progressRecords.length} progress records:`, progressRecords);

    if (progressRecords.length > 0) {
      const result = await this.prisma.targetProgress.createMany({
        data: progressRecords,
        skipDuplicates: true,
      });
      console.log('Individual Target Progress Handler: Database createMany result:', result);
    } else {
      console.log('Individual Target Progress Handler: No progress records to create');
    }
  }
}
