import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { NotificationHandler, NotificationContext } from './notification.handler.interface';
import { NotificationType, NotificationPriority } from '@prisma/client';

@Injectable()
export class User<PERSON>reatedHandler implements NotificationHandler {
  private readonly logger = new Logger(UserCreatedHandler.name);
  readonly type = NotificationType.USER_CREATED;

  constructor(private readonly prisma: PrismaService) {}

  shouldHandle(context: NotificationContext): boolean {
    return context.entityType === 'user' && context.entityData?.isNewUser;
  }

  async generateNotification(context: NotificationContext): Promise<{
    title: string;
    message: string;
    priority: NotificationPriority;
    data?: Record<string, any>;
    recipientIds: string[];
  }> {
    const { entityId, triggerUserId } = context;
    
    try {
      // Get the newly created user details
      const newUser = await this.prisma.user.findUnique({
        where: { id: entityId },
        include: {
          role: true,
          branch: {
            include: {
              region: true,
            },
          },
          added_by_user: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
        },
      });

      if (!newUser) {
        this.logger.warn(`User ${entityId} not found for notification`);
        return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
      }

      // Get all admin users to notify them about the new user
      const adminUsers = await this.prisma.user.findMany({
        where: {
          role: {
            name: {
              contains: 'admin',
              mode: 'insensitive',
            },
          },
        },
        select: {
          id: true,
          name: true,
          email: true,
          rm_code: true,
        },
      });

      // Also notify the user who created this user (if different from admin)
      const recipientIds = adminUsers.map(admin => admin.id);
      if (newUser.added_by && !recipientIds.includes(newUser.added_by)) {
        recipientIds.push(newUser.added_by);
      }

      const title = 'New User Created';
      const message = `A new user has been created: ${newUser.name} (${newUser.rm_code})`;
      
      const data = {
        newUserId: newUser.id,
        newUserName: newUser.name,
        newUserEmail: newUser.email,
        newUserRmCode: newUser.rm_code,
        newUserRole: newUser.role.name,
        newUserBranch: newUser.branch.name,
        newUserRegion: newUser.branch.region.name,
        createdBy: newUser.added_by_user?.name || 'System',
        createdByRmCode: newUser.added_by_user?.rm_code,
        createdAt: newUser.created_at,
      };

      return {
        title,
        message,
        priority: NotificationPriority.NORMAL,
        data,
        recipientIds,
      };

    } catch (error) {
      this.logger.error(`Failed to generate user created notification: ${error.message}`, error.stack);
      return { title: '', message: '', priority: NotificationPriority.NORMAL, recipientIds: [] };
    }
  }
}
