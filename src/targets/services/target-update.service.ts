import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { isApplicable } from '../../common/utils/date.utils';

/**
 * Service for updating user target progress based on activities
 */
@Injectable()
export class TargetUpdateService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Updates user targets based on activity performed
   * @param activity - The activity type (e.g., 'LEADS', 'CUSTOMER_SERVICE', 'LOAN_ACTIVITIES')
   * @param interaction_type - The interaction type ('call' or 'visit')
   * @param user_id - The ID of the user who performed the activity
   */
  async updateUserTargets(
    activity: string,
    interaction_type: string,
    user_id: string,
  ): Promise<void> {
    try {
      // Get user details including role and branch
      const user = await this.prisma.user.findUnique({
        where: { id: user_id },
        include: {
          role: true,
          branch: true,
        },
      });

      if (!user) {
        console.log(`User not found for ID: ${user_id}`);
        return;
      }

      // Find matching targets for the user
      const matchingTargets = await this.findMatchingTargets(
        activity,
        interaction_type,
        user_id,
        user.role_id,
        user.branch_id,
      );

      if (matchingTargets.length === 0) {
        console.log(`No matching target for ${user.name}`);
        return;
      }

      // Process each matching target
      for (const target of matchingTargets) {
        await this.processTargetProgress(target, user_id);
      }

      // Update target-related counters based on interaction type
      await this.updateTargetRelatedCounters(user_id, interaction_type);
    } catch (error) {
      console.log(`Error updating user targets: ${error.message}`);
    }
  }

  /**
   * Finds targets that match the activity and interaction type for the user
   */
  private async findMatchingTargets(
    activity: string,
    interaction_type: string,
    user_id: string,
    role_id: string,
    branch_id: string,
  ) {
    try {
      const today = new Date();

      // Convert interaction_type to match metric_type format (capitalize first letter)
      const metricType =
        interaction_type.charAt(0).toUpperCase() +
        interaction_type.slice(1).toLowerCase();

      // Find targets that match the criteria
      const targets = await this.prisma.target.findMany({
        where: {
          activity: activity as any, // Cast to enum type
          metric_type: metricType as any, // Cast to enum type
          branch_id: branch_id, // Only targets from the same branch
          status: { not: 'Archived' }, // Exclude archived targets
          AND: [
            // Target assignment criteria
            {
              OR: [
                // Direct user assignment
                { user_id: user_id },
                // Role assignment where user belongs to the role
                {
                  role_id: role_id,
                  // User is not in exempted users for this target
                  exempted_target_users: {
                    none: {
                      user_id: user_id,
                    },
                  },
                },
              ],
            },
            // Target is still active (end_date is null or in the future)
            {
              OR: [{ end_date: null }, { end_date: { gte: today } }],
            },
          ],
        },
        include: {
          exempted_target_users: true,
        },
      });

      return targets;
    } catch (error) {
      console.log(`Error finding matching targets: ${error.message}`);
      return [];
    }
  }

  /**
   * Processes target progress for a specific target and user
   */
  private async processTargetProgress(
    target: any,
    user_id: string,
  ): Promise<void> {
    try {
      const today = new Date();
      const todayStart = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
      );
      const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000 - 1);

      // Check if there's an existing TargetProgress record for today
      const existingProgress = await this.prisma.targetProgress.findFirst({
        where: {
          target_id: target.id,
          user_id: user_id,
          for_date: {
            gte: todayStart,
            lte: todayEnd,
          },
        },
      });

      if (existingProgress) {
        // Update existing record
        await this.updateExistingProgress(existingProgress, target);
      } else {
        // Create new record
        await this.createNewProgress(target, user_id);
      }
    } catch (error) {
      console.log(`Error processing target progress: ${error.message}`);
    }
  }

  /**
   * Updates an existing target progress record
   */
  private async updateExistingProgress(
    existingProgress: any,
    target: any,
  ): Promise<void> {
    try {
      const newAchievedCount = existingProgress.achieved_count + 1;
      const isAchieved = newAchievedCount >= target.target_value;

      await this.prisma.targetProgress.update({
        where: { id: existingProgress.id },
        data: {
          achieved_count: newAchievedCount,
          is_achieved: isAchieved,
        },
      });

      console.log(
        `Updated target progress for target ${target.id}, new count: ${newAchievedCount}, achieved: ${isAchieved}`,
      );
    } catch (error) {
      console.log(`Error updating existing progress: ${error.message}`);
    }
  }

  /**
   * Creates a new target progress record for today
   */
  private async createNewProgress(target: any, user_id: string): Promise<void> {
    try {
      const today = new Date();
      const todayAt8AM = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        8,
        0,
        0,
        0,
      );

      // Check if today is applicable (not weekend or holiday)
      const applicable = await isApplicable(today, this.prisma);

      const isAchieved = 1 >= target.target_value;

      await this.prisma.targetProgress.create({
        data: {
          target_id: target.id,
          user_id: user_id,
          for_date: todayAt8AM,
          achieved_count: 1,
          target_value: target.target_value,
          is_achieved: isAchieved,
          is_applicable: applicable,
        },
      });

      console.log(
        `Created new target progress for target ${target.id}, user ${user_id}, achieved: ${isAchieved}`,
      );
    } catch (error) {
      console.log(`Error creating new progress: ${error.message}`);
    }
  }

  /**
   * Update target-related counters based on interaction type
   * @param user_id - The ID of the user
   * @param interaction_type - The type of interaction ('call' or 'visit')
   */
  private async updateTargetRelatedCounters(
    user_id: string,
    interaction_type: string,
  ): Promise<void> {
    try {
      if (interaction_type.toLowerCase() === 'call') {
        // Increment target_related_calls by 1
        await this.prisma.user.update({
          where: { id: user_id },
          data: {
            target_related_calls: {
              increment: 1,
            },
          },
        });
        console.log(`Incremented target_related_calls for user ${user_id}`);
      } else if (interaction_type.toLowerCase() === 'visit') {
        // Increment target_related_visits by 1
        await this.prisma.user.update({
          where: { id: user_id },
          data: {
            target_related_visits: {
              increment: 1,
            },
          },
        });
        console.log(`Incremented target_related_visits for user ${user_id}`);
      }
    } catch (error) {
      console.log(`Error updating target-related counters: ${error.message}`);
    }
  }
}
