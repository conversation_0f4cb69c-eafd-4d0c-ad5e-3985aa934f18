import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Data Transfer Object for profile response
 */
export class ProfileResponseDto {
  @ApiProperty({
    description: 'User name',
    example: '<PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiPropertyOptional({
    description: 'User phone number',
    example: '+254700123456',
  })
  phone_number: string | null;

  @ApiPropertyOptional({
    description: 'Profile photo URL',
    example: 'https://domain.com/media/photos/john.png',
  })
  profile_photo: string | null;

  @ApiProperty({
    description: 'Member since date',
    example: '2023-01-12T10:15:30Z',
  })
  member_since: string;

  @ApiProperty({
    description: 'Branch name',
    example: 'Nairobi Branch',
  })
  branch: string;

  @ApiProperty({
    description: 'Role name',
    example: 'Branch Manager',
  })
  role: string;
}

/**
 * Data Transfer Object for updating profile
 */
export class UpdateProfileDto {
  @ApiPropertyOptional({
    description: 'User name',
    example: '<PERSON>',
  })
  name?: string;

  @ApiPropertyOptional({
    description: 'User email',
    example: '<EMAIL>',
  })
  email?: string;

  @ApiPropertyOptional({
    description: 'User phone number',
    example: '+254700123456',
  })
  phone_number?: string;
}
