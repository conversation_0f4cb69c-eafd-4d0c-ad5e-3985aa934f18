import { Module } from '@nestjs/common';
import { ProfileController } from './profile.controller';
import { ProfileService } from './profile.service';
import { PrismaModule } from '../prisma/prisma.module';
import { CommonModule } from '../common/common.module';

/**
 * Profile Module
 * 
 * This module handles user profile operations including:
 * - Getting current user profile information
 * - Updating user profile with optional photo upload
 * 
 * @module ProfileModule
 */
@Module({
  imports: [PrismaModule, CommonModule],
  controllers: [ProfileController],
  providers: [ProfileService],
  exports: [ProfileService],
})
export class ProfileModule {}
