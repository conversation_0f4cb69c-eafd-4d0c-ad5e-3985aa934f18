import { NestFactory } from '@nestjs/core';
import { Logger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { SchedulerModule } from './scheduler.module';
import { SchedulerService } from './scheduler/scheduler.service';
import { ScheduledTaskService } from './scheduled-tasks/scheduled-task.service';
import { QueueService } from './queue/queue.service';
import { MemoryMonitor } from './common/utils/memory-monitor';

async function bootstrap() {
  const app = await NestFactory.create(SchedulerModule, {
    bufferLogs: true,
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  // Use pino logger for all application logging
  app.useLogger(app.get(Logger));

  const configService = app.get(ConfigService);
  const logger = app.get(Logger);
  const schedulerService = app.get(SchedulerService);

  // Check if scheduler is enabled
  const schedulerEnabled = configService.get('SCHEDULER_ENABLED') === 'true';
  const timezone = configService.get('SCHEDULER_TIMEZONE') || 'Africa/Nairobi';

  if (!schedulerEnabled) {
    logger.warn(
      '⚠️  Scheduler is disabled. Set SCHEDULER_ENABLED=true to enable.',
    );
    process.exit(0);
  }

  logger.log('⏰ Starting Scheduler Service...');
  logger.log(`Scheduler Configuration:`);
  logger.log(`- Enabled: ${schedulerEnabled}`);
  logger.log(`- Timezone: ${timezone}`);

  // Initialize the application
  await app.init();

  // Start memory monitoring
  MemoryMonitor.startMonitoring(30000, 1500); // Monitor every 30 seconds, warn at 1.5GB

  // Initialize scheduler service (for compatibility)
  await schedulerService.startScheduler();

  // DIRECT POLLING IMPLEMENTATION - Production ready
  logger.log('🔄 Starting database polling every 1 minute...');

  const scheduledTaskService = app.get(ScheduledTaskService);
  const queueService = app.get(QueueService);

  setInterval(async () => {
    const timestamp = new Date().toISOString();
    logger.log(
      `🔍 [${timestamp}] DIRECT polling database for scheduled tasks...`,
    );

    try {
      const tasksToProcess =
        await scheduledTaskService.getTasksReadyForExecution(50);
      logger.log(
        `📊 DIRECT found ${tasksToProcess.length} tasks in database query`,
      );

      if (tasksToProcess.length === 0) {
        logger.log('📭 DIRECT: No tasks ready for execution at this time');
        return;
      }

      logger.log(
        `🚀 DIRECT: Found ${tasksToProcess.length} tasks ready for execution`,
      );

      for (const task of tasksToProcess) {
        try {
          // First, mark the task as queued to prevent duplicate queuing
          await scheduledTaskService.update(task.id, {
            status: 'RUNNING', // Temporarily mark as running to prevent re-queuing
          });

          await queueService.addScheduledTaskJob(
            {
              taskId: task.id,
              type: task.type,
              payload: task.payload,
              attemptNumber: task.attempts + 1,
            },
            {
              priority: task.priority,
              attempts: task.max_attempts,
            },
          );

          logger.log(
            `✅ DIRECT: Queued scheduled task: ${task.id} (${task.type}) - ${task.name}`,
          );
        } catch (error) {
          logger.error(
            `❌ DIRECT: Failed to queue task ${task.id} (${task.type}):`,
            error,
          );

          // If queuing failed, reset the task status back to PENDING
          try {
            await scheduledTaskService.update(task.id, {
              status: 'PENDING',
            });
          } catch (resetError) {
            logger.error(`Failed to reset task ${task.id} status:`, resetError);
          }
        }
      }
    } catch (error) {
      logger.error('❌ DIRECT: Error in polling:', error);
    }
  }, 60000); // 1 minute

  logger.log('✅ DIRECT polling started');
  logger.log('✅ Scheduler started and polling database for tasks');

  logger.log('✅ Scheduler Service started successfully');

  // Handle graceful shutdown
  process.on('SIGTERM', async () => {
    logger.log('🛑 Received SIGTERM, shutting down gracefully...');
    await schedulerService.stopScheduler();
    await app.close();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.log('🛑 Received SIGINT, shutting down gracefully...');
    await schedulerService.stopScheduler();
    await app.close();
    process.exit(0);
  });

  // Keep the process alive
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
  });
}

bootstrap().catch((error) => {
  console.error('Failed to start scheduler service:', error);
  process.exit(1);
});
